<template>
    <div class="usemystyle POManagement">
        <div class="subsubtabs">
            <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
                <el-tab-pane :label="$t('Overview.AvailableOrders') + `(${Availablenum})`" name="Available">
                    <div class="InventorySearchBox">
                        <div class="searchbox">
                            <div class="datebox">
                                <div class="datepickbox">
                                    <el-date-picker
                                        v-model="timepicker"
                                        type="daterange"
                                        value-format="yyyy-MM-dd"
                                        range-separator="-"
                                        :start-placeholder="$t('DFM_RL._KSRQ')"
                                        :end-placeholder="$t('DFM_RL._JSRQ')"
                                    ></el-date-picker>
                                </div>
                            </div>
                            <div class="inputformbox" :style="{ width: item.width }" v-for="(item, index) in searchlist" :key="index">
                                <el-input v-if="item.type == 'input'" v-model="item.value" :myid="item.id" :placeholder="item.name"></el-input>
                                <el-select :style="{ width: item.width }" v-model="item.value" v-if="item.type == 'select'" :myid="item.id" :placeholder="item.name">
                                    <el-option v-for="(it, ind) in item.option" :key="ind" :label="it.label" :value="it.value"></el-option>
                                </el-select>
                            </div>
                            <el-button style="margin-left: 5px" size="small" icon="el-icon-refresh" @click="getsearch()">{{ this.$t('Inventory.refresh') }}</el-button>
                            <el-button size="small" style="margin-left: 5px" icon="el-icon-s-help" @click="getempty()">{{ this.$t('GLOBAL._CZ') }}</el-button>
                        </div>
                    </div>
                    <div class="tablebox">
                        <el-table border :data="AvailablePOManagemenList" style="width: 100%" height="520">
                            <el-table-column
                                v-for="(item, index) in header"
                                :key="index"
                                :align="item.align"
                                :prop="item.prop ? item.prop : item.value"
                                :label="$t(`$vuetify.dataTable.${tableId}.${item.value}`)"
                                :width="item.width"
                            >
                                <template slot-scope="scope">
                                    <span v-if="scope.column.property == 'operate'">
                                        <el-button
                                            size="mini"
                                            class="operatebtn"
                                            v-if="scope.row.ExecutionStatus == null || scope.row.ExecutionStatus == 3"
                                            @click="startOrder(scope)"
                                            icon="el-icon-video-play"
                                        >
                                            {{ $t('Overview.start') }}
                                        </el-button>
                                    </span>
                                    <span v-else-if="scope.column.property == 'PlanStartTime'">{{ $dayjs(scope.row.PlanStartTime).format('YYYY-MM-DD HH:mm') }}</span>
                                    <span v-else-if="scope.column.property == 'PlanEndTime'">{{ $dayjs(scope.row.PlanEndTime).format('YYYY-MM-DD HH:mm') }}</span>
                                    <span v-else-if="scope.column.property == 'Segment'">
                                        <div>{{ scope.row.SegmentCode }}</div>
                                    </span>
                                    <span v-else-if="scope.column.property == 'IsHavePreservative'">
                                        <i :class="scope.row[item.value] === '1' ? 'el-icon-star-on' : ''"></i>
                                    </span>
                                    <span v-else-if="scope.column.property == 'LineNominalSpeed'">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>
                                    <span v-else>{{ scope.row[item.prop] }}</span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>
                <el-tab-pane :label="$t('Overview.ActiveOrders') + `(${Activenum})`" name="Active">
                    <div class="InventorySearchBox">
                        <div class="searchbox">
                            <el-button style="margin-left: 5px" size="small" icon="el-icon-refresh" @click="getsearch()">{{ this.$t('Inventory.refresh') }}</el-button>
                            <el-button
                                class="tablebtn"
                                :disabled="tablechooselist > 0 ? false : true"
                                size="small"
                                @click="stopBtn()"
                                style="margin-left: 5px; width: 12vh"
                                icon="el-icon-circle-close"
                            >
                                {{ this.$t('Overview.Stop') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}
                            </el-button>
                            <!-- <el-button class="tablebtn" @click="holdBtn()" :disabled="tablechooselist > 0 ? false : true" size="small" style="margin-left: 5px; width: 12vh" icon="el-icon-video-pause">
                                {{ this.$t('Overview.Hold') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}
                            </el-button>
                            <el-button
                                class="tablebtn"
                                @click="ResumeBtn()"
                                :disabled="tablechooselist > 0 ? false : true"
                                size="small"
                                style="margin-left: 5px; width: 14vh"
                                icon="el-icon-video-play"
                            >
                                {{ this.$t('Overview.Resume') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}
                            </el-button> -->
                            <el-button class="tablebtn" @click="updateBtn()" :disabled="tablechooselist > 0 ? false : true" size="small" style="margin-left: 5px; width: 16vh" icon="el-icon-setting">
                                {{ this.$t('Overview.UpdateOrder') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}
                            </el-button>
                            <el-button
                                class="tablebtn"
                                @click="updateRemarkBtn()"
                                :disabled="tablechooselist > 0 ? false : true"
                                size="small"
                                style="margin-left: 5px; width: 16vh"
                                icon="el-icon-setting"
                            >
                                {{ this.$t('Overview.UpdateRemark') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}
                            </el-button>
                            <!-- <el-button
                                class="tablebtn"
                                @click="startNextBatchBtn()"
                                :disabled="tablechooselist > 0 && Number(selectTabelData.Number) < selectTabelData.BatchCount ? false : true"
                                size="small"
                                style="margin-left: 5px; width: 16vh"
                                icon="el-icon-setting"
                            >
                                {{ this.$t('Overview.NextBatch') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}
                            </el-button>
                            <el-button class="tablebtn" @click="AutoReport()" :disabled="tablechooselist > 0 ? false : true" size="small" style="margin-left: 5px; width: 16vh" icon="el-icon-setting">
                                {{ this.$t('Overview.AutoReport') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}
                            </el-button> -->
                        </div>
                    </div>

                    <div class="tablebox">
                        <el-table border :data="ActivePOManagemenList" ref="ActiveTable" highlight-current-row @current-change="handleSelectionChange" style="width: 100%" height="520">
                            <!-- <el-table-column type="selection" width="55" fixed="left"></el-table-column> -->
                            <el-table-column
                                v-for="(item, index) in Activeheader"
                                :key="index"
                                :align="item.align"
                                :prop="item.prop ? item.prop : item.value"
                                :label="$t(`$vuetify.dataTable.${tableId}.${item.value}`)"
                                :width="item.width"
                            >
                                <template slot-scope="scope">
                                    <span v-if="scope.column.property == 'ProcessOrder'">
                                        <div>{{ scope.row.ProcessOrder }}({{ scope.row.Number }})</div>
                                    </span>
                                    <!-- <span v-else-if="scope.column.property == 'Status'">
                                        {{ getExecutionhStatus(scope.row.Status) }}
                                    </span> -->
                                    <!-- <span v-else-if="scope.column.property == 'LineNominalSpeed'">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>-->
                                    <span v-else>{{ scope.row[item.prop] }}</span> 
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>

                <el-tab-pane :label="$t('Overview.History')" name="History">
                    <div class="InventorySearchBox">
                        <div class="searchbox">
                            <div class="datebox">
                                <div class="datepickbox">
                                    <el-date-picker
                                        v-model="timepicker"
                                        type="daterange"
                                        value-format="yyyy-MM-dd"
                                        range-separator="-"
                                        :start-placeholder="$t('DFM_RL._KSRQ')"
                                        :end-placeholder="$t('DFM_RL._JSRQ')"
                                    ></el-date-picker>
                                </div>
                            </div>
                            <div class="inputformbox" :style="{ width: item.width }" v-for="(item, index) in searchlist" :key="index">
                                <el-input v-if="item.type == 'input'" v-model="item.value" :myid="item.id" :placeholder="item.name"></el-input>
                            </div>
                            <el-button style="margin-left: 5px" size="small" icon="el-icon-refresh" @click="getsearch()">{{ this.$t('Inventory.refresh') }}</el-button>
                            <el-button size="small" style="margin-left: 5px" icon="el-icon-s-help" @click="getempty()">{{ this.$t('GLOBAL._CZ') }}</el-button>
                        </div>
                    </div>
                    <div class="tablebox">
                        <el-table border :data="HistroyList" style="width: 100%" height="520">
                            <el-table-column
                                v-for="(item, index) in Historyheader"
                                :key="index"
                                :align="item.align"
                                :prop="item.prop ? item.prop : item.value"
                                :label="$t(`$vuetify.dataTable.${tableId}.${item.value}`)"
                                :width="item.width"
                            >
                                <template slot-scope="scope">
                                    <span v-if="scope.column.property == 'Material'">
                                        <div>{{ scope.row.MaterialCode }}</div>
                                        <div style="color: #808080">{{ scope.row.MaterialName }}</div>
                                    </span>
                                    <span v-else-if="scope.column.property == 'SAP'">
                                        <div>{{ scope.row.SegmentCode }}</div>
                                        <div style="color: #808080">{{ scope.row.SegmentName }}</div>
                                    </span>
                                    <span v-else-if="scope.column.property == 'Status'">
                                        {{ getExecutionhStatus(scope.row.Status) }}
                                    </span>
                                    <span v-else-if="scope.column.property == 'NominalSpeed'">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>
                                    <span v-else>{{ scope.row[item.prop] }}</span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <div class="paginationbox">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="pageOptions.page"
                    :page-sizes="pageOptions.pageSizeitems"
                    :page-size="pageOptions.pageSize"
                    layout="total, sizes, prev, pager, next"
                    :total="pageOptions.total"
                    background
                ></el-pagination>
            </div>
        </div>
        <el-dialog :title="$t('Overview.StartOrder')" id="Startdialog" :visible.sync="StartModel" :width="IsPack == '0' ? '650px' : '650px'">
            <span slot="title" class="dialog-title">
                <div class="dialogtitlebox">
                    {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.StartOrder') }}
                    <div class="dialogsubtitlebox" style="display: inline">{{ chooseItem.ProcessOrder }}</div>
                </div>
            </span>
            <div class="splitdetailbox">
                <div class="splitdetailboxtitle">
                    {{ chooseItem.MaterialCode }}-{{ chooseItem.MaterialName }}
                    <el-tag class="splitdetailboxtitleTag" size="small" v-if="Activenum != 0">{{ ActivePOManagemenList[0] ? ActivePOManagemenList[0].ProcessOrder : '' }}</el-tag>
                </div>
                <div class="detailsnote" v-if="runningCode != '' && !chooseItem.isResume">
                    {{ $t('Overview.Note1') }}
                    <span style="font-weight: 600">{{ runningCode }}</span>
                    {{ $t('Overview.Note2') }}
                </div>
                <div style="display: flex">
                    <div :style="{ width: IsPack == '0' ? '100%' : '100%' }">
                        <div class="dialogdetailbox" v-for="(item, index) in Startlist" :key="index">
                            <div class="dialogdetailsinglelabel" :style="{ width: item.type == 'BatchCode' ? '20%' : '20%' }">{{ item.label }}{{ item.require ? ' *' : '' }}</div>
                            <div class="dialogdetailsinglevalue longwidthinput" :style="{ width: item.type == 'BatchCode' || item.type == 'checkBox' ? '400px' : '77%' }">
                                <el-input v-if="item.type == 'input'" v-model="item.value"></el-input>
                               
                                <div v-else-if="item.type == 'BatchCode'" style="display: flex">
                                    <el-input v-model="item.value"></el-input>
                                    <el-input v-model="item.value2" disabled></el-input>
                                    <el-input v-model="item.value3"></el-input>
                                    <el-button
                                        class="tablebtn"
                                        @click="getBatchCode()"
                                        size="mini"
                                        style="margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff"
                                        icon="el-icon-refresh"
                                    ></el-button>
                                </div>
                                <el-select clearable v-else-if="item.type == 'select'" v-model="item.value" filterable>
                                    <el-option v-for="it in item.option" :key="it.ID" :label="it.Number" :value="it.ID"></el-option>
                                </el-select>
                                <el-date-picker
                                    @change="GetDate(item.id)"
                                    v-else-if="item.type == 'date'"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    :disabled="item.disabled"
                                    v-model="item.value"
                                    type="datetime"
                                ></el-date-picker>
                                <span v-else-if="item.id == 'TargetQuantity'">{{ chooseItem.TargetQuantity }}{{ chooseItem.Unit1 }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button style="float: left" v-if="chooseItem.isResume">
                    {{ $t('Overview.bottleneck') }}
                </el-button>
                <el-button class="tablebtn" :disabled="IsDifferent" icon="el-icon-video-play" @click="ProducedStart()">
                    {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.Start') }}
                </el-button>
                <el-button @click="StartModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
        <el-dialog id="Stopdialog" :visible.sync="stopModel" width="650px">
            <span slot="title" class="dialog-title">
                <div class="dialogtitlebox">
                    {{ $t('Overview.StopNote') }}
                </div>
            </span>
            <div class="splitdetailbox">
                <div class="splitdetailboxtitle">
                    {{ selectTabelData != {} ? selectTabelData.MaterialName + '-' + selectTabelData.MaterialCode : '' }}
                    <el-tag class="splitdetailboxtitleTag" size="small">{{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</el-tag>
                </div>
                <div class="dialogdetailbox">
                    <div class="dialogdetailsinglelabel">{{ $t('Overview.EndTime') }}</div>
                    <div class="dialogdetailsinglevalue">
                        <el-date-picker disabled v-model="EndTime" type="datetime"></el-date-picker>
                    </div>
                </div>
                <div v-if="isComplete == true && selectTabelData.NeedQARelease == '1'">
                    <div class="dialogdetailbox" v-for="(item, index) in Completelist" :key="index">
                        <div class="dialogdetailsinglelabel">{{ item.label }}{{ item.require ? ' *' : '' }}</div>
                        <div class="dialogdetailsinglevalue">
                            <el-select @change="getData2(item)" v-model="item.value" clearable filterable :disabled="item.id == 'ProduceStatus'" v-if="item.type == 'select'">
                                <el-option v-for="(it, ind) in item.options" :key="ind" :label="it.label" :value="it.key"></el-option>
                            </el-select>
                            <span v-else>{{ item.value }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <span slot="footer" class="dialog-footer">
                <div style="float: left">
                    <el-checkbox v-model="isComplete">{{ $t('Overview.CompletePO') }}</el-checkbox>
                </div>
                <el-button class="tablebtn" icon="el-icon-circle-close" @click="StopProduced()">
                    {{ $t('Overview.Stop') }}
                </el-button>
                <el-button @click="stopModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
        <el-dialog id="Holddialog" :visible.sync="HoldModel" width="650px">
            <span slot="title" class="dialog-title">
                <div class="dialogtitlebox">
                    {{ $t('Overview.HoldNote') }}
                </div>
            </span>
            <div class="splitdetailbox">
                <div class="splitdetailboxtitle">
                    {{ selectTabelData != {} ? selectTabelData.MaterialName + '-' + selectTabelData.MaterialCode : '' }}
                    <el-tag class="splitdetailboxtitleTag" size="small">{{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</el-tag>
                </div>
                <div class="dialogdetailbox">
                    <div class="dialogdetailsinglelabel">{{ $t('Overview.EndTime') }} *</div>
                    <div class="dialogdetailsinglevalue">
                        <el-date-picker disabled v-model="EndTime" type="datetime"></el-date-picker>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <div style="float: left">
                    <el-checkbox v-model="isComplete">{{ $t('Overview.CompletePO') }}</el-checkbox>
                </div>
                <el-button class="tablebtn" icon="el-icon-video-pause" @click="HoldProduced()">
                    {{ $t('Overview.Hold') }}
                </el-button>
                <el-button @click="HoldModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
        <el-dialog id="Updatedialog" :visible.sync="UpdateModel" width="650px">
            <span slot="title" class="dialog-title">
                <div class="dialogtitlebox">{{ $t('Overview.UpdateNote') }} {{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</div>
            </span>
            <div class="splitdetailbox">
                <div class="splitdetailbox">
                    <div class="dialogdetailbox" v-for="(item, index) in Updatelist" :key="index">
                        <div class="dialogdetailsinglelabel">{{ item.label }}</div>
                        <div class="dialogdetailsinglevalue">
                            <span>{{ item.value }}</span>
                        </div>
                    </div>
                </div>
                <div class="splitdetailbox">
                    <div class="dialogdetailbox" v-for="(item, index) in Updateinputlist" :key="index">
                        <div class="dialogdetailsinglelabel" :style="{ width: item.type == 'BatchCode' ? '20%' : '20%' }">{{ item.label }}{{ item.require ? ' *' : '' }}</div>
                        <div class="dialogdetailsinglevalue longwidthinput" :style="{ width: item.type == 'BatchCode' ? '400px' : '77%' }">
                            <el-input v-if="item.type == 'input'" v-model="item.value"></el-input>

                            <div v-else-if="item.type == 'BatchCode'" style="display: flex">
                                <el-input v-model="item.value"></el-input>
                                <el-input v-model="item.value2" disabled></el-input>
                                <el-input v-model="item.value3"></el-input>
                                <el-button
                                    :disabled="Updateinputlist[0].value == ''"
                                    class="tablebtn"
                                    @click="getBatchCode2()"
                                    size="mini"
                                    style="margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff"
                                    icon="el-icon-refresh"
                                ></el-button>
                            </div>
                            <el-select clearable v-else-if="item.type == 'select'" v-model="item.value" filterable>
                                <el-option v-for="it in item.option" :key="it.ID" :label="it.BatchCode" :value="it.ID"></el-option>
                            </el-select>
                            <el-date-picker v-else-if="item.type == 'date'" :type="item.datetype" v-model="item.value"></el-date-picker>
                            <!-- <el-date-picker @change="GetDate2(item.id)" v-else-if="item.type == 'date'" :type="item.datetype" v-model="item.value"></el-date-picker> -->
                            <span v-else>{{ item.value }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="tablebtn" icon="el-icon-setting" @click="UpdateProduced()">
                    {{ $t('Overview.UpdateOrder') }}
                </el-button>
                <el-button @click="UpdateModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
        <el-dialog id="Updatedialog2" :visible.sync="UpdateRemark" width="650px">
            <span slot="title" class="dialog-title">
                <div class="dialogtitlebox">{{ $t('Overview.UpdateNote') }} {{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</div>
            </span>
            <div class="splitdetailbox">
                <div class="splitdetailbox">
                    <div class="dialogdetailbox" v-for="(item, index) in Updatelist" :key="index">
                        <div class="dialogdetailsinglelabel">{{ item.label }}</div>
                        <div class="dialogdetailsinglevalue">
                            <span>{{ item.value }}</span>
                        </div>
                    </div>
                </div>
                <div class="splitdetailbox">
                    <div class="dialogdetailbox" key="remark">
                        <div class="dialogdetailsinglelabel" :style="{ width: '20%' }">{{ $t('Overview.Comments') }}</div>
                        <div class="dialogdetailsinglevalue longwidthinput" :style="{ width: '77%' }">
                            <el-input v-model="Remark"></el-input>
                        </div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="tablebtn" icon="el-icon-setting" @click="UpdateOrderRemark()">
                    {{ $t('Overview.UpdateRemark') }}
                </el-button>
                <el-button @click="UpdateRemark = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';
import { POManagemenAvailable, POManagemenActive, POManagemenHistory } from '@/columns/factoryPlant/tableHeaders';
import { Message, MessageBox } from 'element-ui';
import { GetDataTreeList, GetDataItemList } from '@/api/factoryPlant/process.js';
import {
    GetProcessOrderView,
    GetProcessOrderView2,
    GetBBatchListView,
    GetBatchCode,
    PoProducedStart,
    PoProducedStop,
    PoProducedHold,
    PoProducedResume,
    PoProducedUpdatePo,
    AutoReport,
    UpdateOrderRemark,
    PoExecutionHistroy,
    GetRunOrder,
    GetEquipmentProcessOrderView,
    StartNextBatch
} from '@/api/Inventory/Overview.js';
import moment from 'moment';
import { ConsoleLogger } from '@microsoft/signalr/dist/esm/Utils';

export default {
    name: 'POManagement',

    data() {
        return {
            Completelist: [
                {
                    label: this.$t('POList.PlanQty'),
                    id: 'PlanQuantity',
                    value: ''
                },
                {
                    label: this.$t('POList.ActualQty'),
                    id: 'ActualQuantity',
                    value: ''
                },
                {
                    label: this.$t('POList.ProduceStatus'),
                    id: 'ProduceStatus',
                    require: true,
                    type: 'select',
                    options: [
                        {
                            key: 'NotComplete',
                            label: this.$t('POList.NotComplete')
                        },
                        {
                            key: 'OverComplete',
                            label: this.$t('POList.OverComplete')
                        },
                        {
                            key: 'CompleteAtOnce',
                            label: this.$t('POList.CompleteAtOnce')
                        }
                    ],
                    value: ''
                },
                {
                    label: this.$t('POList.Reason'),
                    id: 'Reason',
                    type: 'select',
                    require: true,
                    options: [],
                    value: ''
                }
            ],
            viewtitle: '',
            timepicker: [moment(new Date()).format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')],
            tableId: 'PRO_POManagement',
            runningCode: '',
            header: POManagemenAvailable,
            Activeheader: POManagemenActive,
            Historyheader: POManagemenHistory,
            ActivePOManagemenList: [],
            Activenum: 0,
            AvailablePOManagemenList: [],
            Availablenum: 0,
            HistroyList: [],
            Histroynum: 0,
            searchlist: [
                {
                    type: 'input',
                    name: this.$t('Overview.QuickSearch'),
                    id: 'QuickSearch',
                    value: ''
                }
            ],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            activeName: 'Available',
            StartModel: false,
            Startlist: [
                {
                    label: this.$t('Overview.StartTime'),
                    id: 'StartTime',
                    value: '',
                    disabled: true,
                    type: 'date'
                },
                {
                    label: this.$t('Overview.Batch'),
                    id: 'BatchId',
                    value: '',
                    require: true,
                    type: 'select',
                    option: []
                },
                {
                    label: this.$t('Overview.BatchCode'),
                    id: 'LotCode',
                    value: '',
                    value2: '',
                    value3: '',
                    require: true,
                    type: 'BatchCode'
                },
                {
                    label: this.$t('Overview.ProductionDate'),
                    id: 'ProductionDate',
                    require: true,
                    value: '',
                    type: 'date'
                },
                {
                    label: this.$t('Overview.ExpirationDate'),
                    id: 'ExpirationDate',
                    require: true,
                    value: '',
                    type: 'date',
                    disabled: true
                },
                {
                    label: this.$t('Overview.TargetQuantity'),
                    id: 'TargetQuantity',
                    value: ''
                },
                {
                    label: this.$t('Overview.CrewSize'),
                    id: 'CrewSize',
                    value: '',
                    type: 'input'
                },
                {
                    label: '',
                    id: 'IsUpdateLtxt',
                    value: false,
                    type: 'checkBox'
                }
            ],
            chooseItem: {
                ProcessOrder: '',
                isResume: false,
                TargetQuantity: '',
                Unit1: '',
                MaterialCode: '',
                MaterialName: ''
            },
            tablechooselist: 0,
            selectTabelData: {},
            stopModel: false,
            EndTime: new Date(),
            isComplete: false,
            HoldModel: false,
            UpdateModel: false,
            UpdateRemark: false,
            Updatelist: [
                {
                    label: this.$t('Overview.Material'),
                    value: '',
                    id: 'Material'
                },
                {
                    label: this.$t('Overview.TargetQuantity'),
                    value: '',
                    id: 'TargetQuantity'
                }
            ],
            Updateinputlist: [
                {
                    label: this.$t('Overview.ProductionDate'),
                    id: 'ProductionDate',
                    value: '',
                    require: true,
                    type: 'date',
                    datetype: 'datetime'
                },
                {
                    label: this.$t('Overview.BatchCode'),
                    id: 'LotCode',
                    value: '',
                    value2: '',
                    value3: '',
                    require: true,
                    type: 'BatchCode'
                },
                // {
                //     label: this.$t('Overview.DefaultBatchCode'),
                //     id: 'DefaultBatchCode',
                //     value: ''
                // },

                {
                    label: this.$t('Overview.ExpirationDate'),
                    id: 'ExpirationDate',
                    require: true,
                    value: '',
                    type: 'date',
                    datetype: 'datetime'
                },
                {
                    label: this.$t('Overview.Comments'),
                    id: 'Comments',
                    require: false,
                    value: '',
                    type: 'input'
                }
            ],
            Remark: '',
            ExecutionhStatus: [],
            EquipmentId: '',
            EquipmentCode: '',
            IsPack: '1',
            Text1: '',
            Text2: '',
            productionId: '',
            isEdit: false,
            IsDifferent: false,
            ReasonList: []
        };
    },
    methods: {
        getEquipmentModal(item, Equipmentitem) {
            this.productionId = item.ProductionOrderId;
            this.EquipmentId = item.ID;
            this.EquipmentCode = item.EquipmentCode;
            // if (Equipmentitem) {
            // } else {
            //     // this.EquipmentId = '';
            //     // this.EquipmentCode = '';
            // }
            this.getStatus();
            this.GetProcessOrderView();
            this.getPoExecutionHistroy();
        },
        async getStatus() {
            this.ReasonList = [];
            this.Completelist[2].options.forEach(async item => {
                let p = {
                    ItemCode: item.key
                };
                const res = await GetDataTreeList(p);
                res.response.data.forEach(item1 => {
                    this.ReasonList.push(item1);
                });
            });
        },
        getReasonName(key1, key2) {
            if (key1 === null || key1 === '' || key2 === null || key2 === '') {
                return '';
            }
            let name = key2;
            //console.log(this._i18n.locale);
            if (this._i18n.locale == 'en') {
                return name;
            }
            this.ReasonList.forEach(item => {
                if (item.ItemCode === key1 && item.ItemValue === key2) {
                    name = item.ItemName;
                }
            });
            //console.log(name);
            return name;
        },
        calculateDate(days) {
            const date = new Date();
            date.setDate(date.getDate() + days);
            return this.formatDate(date);
        },
        async getStauts() {
            let params2 = {
                ItemCode: 'ProductionExecutionStatus'
            };
            const res2 = await GetDataItemList(params2);
            let data2 = res2.response;
            this.ExecutionhStatus = data2;
        },
        getExecutionhStatus(val) {
            if (val) {
                let name = '';
                this.ExecutionhStatus.forEach(item => {
                    if (item.ItemValue == val) {
                        name = item.ItemName;
                    }
                });
                return name;
            }
        },
        getsearch() {
            this.pageOptions.page = 1;
            this.pageOptions.pageSize = 20;
            this.getStauts();
            this.GetProcessOrderView();
            this.getPoExecutionHistroy();
        },
        getempty() {
            this.QuickSearch = '';
            this.timepicker = [];
            this.pageOptions.page = 1;
            this.pageOptions.pageSize = 20;
            this.searchlist.forEach(item => {
                item.value = '';
            });
            this.GetProcessOrderView();
            this.getPoExecutionHistroy();
        },
        async getPoExecutionHistroy() {
            if (this.timepicker == null) {
                this.timepicker = [];
            }
            let params = {
                Key: this.searchlist[0].value,
                RunEquipmentId: this.EquipmentId,
                pageIndex: this.pageOptions.page,
                pageSize: this.pageOptions.pageSize,
                StartTime: this.timepicker[0],
                EndTime: this.timepicker[1] == undefined ? '' : this.timepicker[1] + ' 23:59:59'
            };
            let res = await PoExecutionHistroy(params);
            this.HistroyList = res.response.data;
            this.Histroynum = res.response.dataCount;
            if (this.activeName == 'History') {
                this.pageOptions.total = this.Histroynum;
            }
            let el = document.getElementsByClassName(`el-pagination__total`);
            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;
            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');
            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));
        },
        async getBatchList() {
            let params = {
                PoSegmentRequirementId: this.chooseItem.ID
            };
            let res = await GetBBatchListView(params);
            this.Startlist[1].option = res.response;
            this.Startlist[1].value = this.Startlist[1].option[0].ID;
            this.StartModel = true;
        },
        async getData2(item) {
            if (item.id == 'ProduceStatus') {
                if (item.value != '') {
                    let res = await this.$getNewDataDictionary(item.value);
                    //let res = this.ReasonList.find(x=>x.ItemCode = item.value)
                    // console.log(res);
                    let data = res;
                    if (data.length > 0) {
                        data.forEach(item1 => {
                            item1.key = item1.ItemValue;
                            // console.log(this._i18n.locale);
                            item1.label = this._i18n.locale === 'en' ? item1.ItemValue : item1.ItemName;
                        });
                    }
                    this.Completelist[3].options = data;
                    this.Completelist[3].value = '';
                }
            }
        },
        async getLtext(id) {
            if (this.IsPack === '0') {
                this.IsDifferent = false;
                let params = {
                    id: id
                };
                // let r = await GetCookOrderLtexts(params);
                // if (r.response.length == 2) {
                //     if (r.msg === '长文本不一致！') {
                //         this.IsDifferent = true;
                //     }
                //     this.Text1 = r.response[0].ProcessData;
                //     this.Text2 = r.response[1].ProcessData;
                // }
            }
        },
        async getBatchCode() {
            let date = moment(this.Startlist[3].value).format('YYYY-MM-DD HH:mm:ss');
            let p = {
                LineCode: this.Startlist[2].value,
                equipmentCode: this.EquipmentCode,
                productionDate: date,
                productionId: this.chooseItem.ProductionOrderId
            };
            let res = await GetBatchCode(p);
            if (res.response == null) {
                Message({
                    message: res.msg,
                    type: 'warning'
                });
            } else {
                this.Startlist[2].value = res.response.substring(0, 2);
                this.Startlist[2].value2 = res.response.substring(2, 5);
            }
        },
        async getBatchCode2() {
            let date = moment(this.Updateinputlist[0].value).format('YYYY-MM-DD HH:mm:ss');
            let p = {
                LineCode: this.Updateinputlist[1].value,
                equipmentCode: this.EquipmentCode,
                productionDate: date,
                productionId: this.selectTabelData.ProductionOrderId
            };
            let res = await GetBatchCode(p);
            if (res.response == null) {
                Message({
                    message: res.msg,
                    type: 'warning'
                });
            } else {
                this.Updateinputlist[1].value = res.response.substring(0, 2);
                this.Updateinputlist[1].value2 = res.response.substring(2, 5);
            }
        },
        async GetProcessOrderView() {
            if (this.timepicker == null) {
                this.timepicker = [];
            }
            let params = {
                Search: this.searchlist[0].value,
                EquipmentId: this.EquipmentId,
                pageIndex: this.pageOptions.page,
                pageSize: this.pageOptions.pageSize,
                StartTime: this.timepicker[0],
                EndTime: this.timepicker[1] == undefined ? '' : this.timepicker[1] + ' 23:59:59'
            };
            let res = await GetProcessOrderView(params);
            this.AvailablePOManagemenList = res.response.data;
            this.Availablenum = res.response.dataCount;
            let params2 = {
                Search: this.searchlist[0].value,
                EquipmentId: this.EquipmentId,
                ExecutionStatus: ['1', '5'],
                pageIndex: this.pageOptions.page,
                pageSize: this.pageOptions.pageSize
            };
            let res2 = await GetProcessOrderView2(params2);
            this.ActivePOManagemenList = res2.response.data;
            this.Activenum = res2.response.dataCount;
            if (this.activeName == 'Active') {
                this.pageOptions.total = this.Activenum;
            } else if (this.activeName == 'Available') {
                this.pageOptions.total = this.Availablenum;
            }
            let el = document.getElementsByClassName(`el-pagination__total`);
            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;
            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');
            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));
            // this.getNumTofather();
        },
        // getNumTofather() {
        //     this.$emit('getNum', this.ActivePOManagemenList);
        // },
        changePagination() {
            let el2 = document.getElementsByClassName(`el-select-dropdown__item`);
            for (let i = 0; i < el2.length; i++) {
                el2[i].innerHTML = el2[i].innerHTML.replace('条/页', this.$t('PAGINATION.MYPAGE'));
            }
        },
        async AutoReport() {
            let res = await AutoReport('', 'reportType=Consume&&equipmentCode=' + this.selectTabelData.EquipmentId);
            Message({
                message: res.msg,
                type: 'success'
            });
        },
        async UpdateProduced() {
            let flag = this.Updateinputlist.some(item => {
                if (item.require) {
                    return item.value == '';
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'warning'
                });
                return;
            }
            let params = {
                ExecutionId: this.selectTabelData.ExecutionId,
                LotCode: '',
                ProductionDate: '',
                ExpirationDate: ''
            };
            this.Updateinputlist.forEach(item => {
                if (item.id == 'LotCode') {
                    params[item.id] = item.value + item.value2 + item.value3;
                } else {
                    params[item.id] = item.value;
                }
            });
            params.ExpirationDate = moment(params.ExpirationDate).format('YYYY-MM-DD HH:mm:ss');
            if (params.LotCode.length > 10) {
                Message({
                    message: `${this.$t('Overview.BatchCodeLong')}`,
                    type: 'warning'
                });
                return;
            }
            let res = await PoProducedUpdatePo(params);
            this.GetProcessOrderView();
            this.UpdateModel = false;
            Message({
                message: res.msg,
                type: 'success'
            });
        },

        async UpdateOrderRemark() {
            let params = {
                ID: this.selectTabelData.ProductionOrderId,
                Remark: this.Remark
            };
            let res = await UpdateOrderRemark(params);
            this.GetProcessOrderView();
            this.getPoExecutionHistroy();
            this.UpdateRemark = false;
            Message({
                message: res.msg,
                type: 'success'
            });
        },
        async HoldProduced() {
            if (this.EndTime == null || this.EndTime == '') {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'warning'
                });
                return;
            }
            let params = {
                ExecutionId: this.selectTabelData.ExecutionId,
                EndTime: this.EndTime,
                IsComplete: this.isComplete
            };
            let res = await PoProducedHold(params);
            this.GetProcessOrderView();
            this.HoldModel = false;
            Message({
                message: res.msg,
                type: 'success'
            });
        },
        ResumeBtn() {
            this.startOrder(this.selectTabelData);
        },
        async StopProduced() {
            if (this.EndTime == null || this.EndTime == '') {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'warning'
                });
                return;
            }
            if (this.isComplete == true && this.selectTabelData.NeedQARelease == '1') {
                let flag = this.Completelist.some(item => {
                    if (item.require) {
                        return item.value == '' || item.value == null;
                    }
                });
                if (flag) {
                    Message({
                        message: `${this.$t('Inventory.ToOver')}`,
                        type: 'warning'
                    });
                    return;
                }
            }
            let params = {
                ExecutionId: this.selectTabelData.ExecutionId,
                EndTime: this.EndTime,
                IsComplete: this.isComplete,
                ProduceStatus: this.Completelist[2].value,
                Reason: this.Completelist[3].value
            };
            let res = await PoProducedStop(params);
            this.GetProcessOrderView();
            this.$emit('loadProgress', this.EquipmentId);
            this.stopModel = false;
            if (res.msg.includes('需要取样')) {
                MessageBox.alert(`${res.msg}`, '提示!', {
                    confirmButtonText: '确定',
                    callback: action => {}
                });
            } else {
                Message({
                    message: res.msg,
                    type: 'success'
                });
            }
        },
        stopBtn() {
            this.selectTabelData.Material = this.selectTabelData.MaterialName + '-' + this.selectTabelData.MaterialCode;
            this.selectTabelData.PlanQuantity = this.selectTabelData.TargetQuantity + this.selectTabelData.Unit1;
            this.selectTabelData.ActualQuantity = this.selectTabelData.ActualQty + this.selectTabelData.Unit1;
            this.Completelist.forEach(item => {
                item.value = '';
            });
            //console.log(this.Completelist[4]);
            this.Completelist.forEach(item => {
                for (let k in this.selectTabelData) {
                    if (item.id == k) {
                        item.value = this.selectTabelData[k];
                    }
                }
            });
            this.Completelist[3].options = [];
            let PlanQuantity = Number(this.selectTabelData.TargetQuantity);
            let ActualQuantity = Number(this.selectTabelData.ActualQty);
            if (PlanQuantity > ActualQuantity) {
                this.Completelist[2].value = 'NotComplete';
            } else if (PlanQuantity == ActualQuantity) {
                this.Completelist[2].value = 'CompleteAtOnce';
            } else {
                this.Completelist[2].value = 'OverComplete';
            }
            this.ReasonList.forEach(x => {
                if (x.ItemCode == this.Completelist[2].value) {
                    let res = this.Completelist[3].options.find(x1 => x1.ItemCode === this.Completelist[3].value && x1.ItemValue === x.ItemValue);
                    if (res === null || res === undefined || typeof res === 'undefined') {
                        x.key = x.ItemValue;
                        x.label = this._i18n.locale === 'en' ? x.ItemValue : x.ItemName;
                        this.Completelist[3].options.push(x);
                    }
                }
            });
            this.EndTime = new Date();
            this.stopModel = true;
        },
        holdBtn() {
            this.EndTime = new Date();
            this.HoldModel = true;
        },
        updateBtn() {
            this.Updateinputlist.forEach(item => {
                item.value = '';
                if (item.id == 'LotCode') {
                    item.value2 = '';
                    item.value3 = '';
                }
            });
            this.Updatelist.forEach(item => {
                item.value = this.selectTabelData[item.id];
            });
            this.Updatelist[1].value += this.selectTabelData.Unit1;
            this.Updateinputlist[0].value = this.selectTabelData.StartTime;
            //this.Updateinputlist[1].value2 = this.selectTabelData.BatchCode;
            this.Updateinputlist[2].value = this.selectTabelData.ExpirationDate;
            this.getBatchCode2();
            this.UpdateModel = true;
        },
        updateRemarkBtn() {
            this.Updatelist.forEach(item => {
                item.value = this.selectTabelData[item.id];
            });
            this.Updatelist[1].value += this.selectTabelData.Unit1;
            this.Remark = this.selectTabelData.Remark;
            this.UpdateRemark = true;
        },
        async startNextBatchBtn() {
            let params = {
                ExecutionId: this.selectTabelData.ExecutionId,
                Number: this.selectTabelData.Number,
                EquipmentId: this.selectTabelData.EquipmentId
            };
            let res = await StartNextBatch(params);
            this.GetProcessOrderView();
            this.getPoExecutionHistroy();
            if (res.msg.includes('需要取样')) {
                MessageBox.alert(`${res.msg}`, '提示!', {
                    confirmButtonText: `${this.$t('GLOBAL._QD')}`,
                    callback: action => {}
                });
            } else {
                Message({
                    message: res.msg,
                    type: 'success'
                });
            }
            // Message({
            //     message: res.msg,
            //     type: 'success'
            // });
        },
        handleClick(key) {
            this.QuickSearch = '';
            this.timepicker = [];
            this.pageOptions.page = 1;
            this.pageOptions.pageSize = 20;
            this.$refs.ActiveTable.setCurrentRow(null);
            this.searchlist.forEach(item => {
                item.value = '';
            });
            if (this.activeName == 'Active') {
                this.pageOptions.total = this.Activenum;
            } else if (this.activeName == 'Available') {
                this.pageOptions.total = this.Availablenum;
            } else {
                this.pageOptions.total = this.Histroynum;
            }
        },
        GetDate(id) {
            if (id == 'ProductionDate') {
                if (this.chooseItem.NeedQARelease == '1') {
                    this.Startlist[4].value = this.addDays(this.Startlist[3].value, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);
                } else {
                    this.Startlist[4].value = this.addDays(this.chooseItem.PlanStartTime, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);
                }
            }
        },
        GetDate2(id) {
            if (id == 'ProductionDate') {
                if (this.selectTabelData.NeedQARelease == '1') {
                    this.Updateinputlist[2].value = this.addDays(this.Updateinputlist[0].value, this.selectTabelData.Mhdhb, this.selectTabelData.Iprkz);
                } else {
                    this.Updateinputlist[2].value = this.addDays(this.selectTabelData.PlanStartTime, this.selectTabelData.Mhdhb, this.selectTabelData.Iprkz);
                }
            }
        },
        addDays(date, number, interval) {
            const newDate1 = new Date(date);
            const newDate = new Date(newDate1.getFullYear(), newDate1.getMonth(), newDate1.getDate());
            this.DateAdd(interval, number, newDate);
            newDate.setDate(newDate.getDate() + 1); // 增加一天
            newDate.setSeconds(newDate.getSeconds() - 1); // 减去1秒
            return newDate;
        },
        DateAdd(interval, number, date) {
            switch (interval) {
                case 'Y': {
                    date.setFullYear(date.getFullYear() + number);
                    return date;
                }
                case 'Q': {
                    date.setMonth(date.getMonth() + number * 3);
                    return date;
                }
                case 'M': {
                    date.setMonth(date.getMonth() + number);
                    return date;
                }
                case 'W': {
                    date.setDate(date.getDate() + number * 7);
                    return date;
                }
                case 'D': {
                    date.setDate(date.getDate() + number);
                    return date;
                }
                case 'h': {
                    date.setHours(date.getHours() + number);
                    return date;
                }
                case 'm': {
                    date.setMinutes(date.getMinutes() + number);
                    return date;
                }
                case 's': {
                    date.setSeconds(date.getSeconds() + number);
                    return date;
                }
                default: {
                    date.setDate(date.getDate() + number);
                    return date;
                }
            }
        },
        startOrder(item) {
            if (item.row) {
                this.IsPack = item.row.NeedQARelease;
                //this.getLtext(item.row.ProductionOrderId);
                this.chooseItem = item.row;
                this.chooseItem.isResume = false;
            } else {
                this.chooseItem = item;
                this.chooseItem.isResume = true;
            }
            this.MyGetRunOrder();
            this.Startlist.forEach((item, index) => {
                if (index == 0) {
                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
                } else if (index == 3) {
                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
                } else if (index == 2) {
                    item.value = '';
                    item.value2 = '';
                    item.value3 = '';
                } else if (item.id == 'IsUpdateLtxt') {
                    item.value = false;
                } else {
                    item.value = '';
                }
            });
            this.GetDate('ProductionDate');
            this.getBatchList();
            //this.getBatchCode();
        },
        async MyGetRunOrder() {
            let res = await GetRunOrder('', this.EquipmentId);
            this.runningCode = res.response;
            if (this.runningCode == null) {
                this.runningCode = '';
            }
        },
        async ProducedStart() {
            if (this.IsDifferent == true) {
                this.$message.warning('工艺长文本对比不通过,不允许启动工单！');
                return;
            }
            let flag = this.Startlist.some(item => {
                if (item.require) {
                    return item.value == '';
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'warning'
                });
                return;
            }
            let params = {
                SegmentId: this.chooseItem.SegmentId,
                ProductionOrderId: this.chooseItem.ProductionOrderId,
                PoSegmentRequirementId: this.chooseItem.ID,
                BatchId: '',
                LotCode: '',
                EquipmentId: this.EquipmentId,
                StartTime: '',
                ProductionDate: '',
                ExpirationDate: ''
            };
            if (this.chooseItem.isResume == true) {
                params.ExecutionId = this.chooseItem.ExecutionId;
            } else {
                params.ExecutionId = '';
            }
            this.Startlist.forEach(item => {
                if (item.id == 'LotCode') {
                    params[item.id] = item.value + item.value2 + item.value3;
                } else {
                    params[item.id] = item.value;
                }
            });
            //params.ExpirationDate = moment(params.ExpirationDate).format('YYYY-MM-DD HH:mm:ss');
            // if (params.LotCode.length > 10) {
            //     Message({
            //         message: `${this.$t('Overview.BatchCodeLong')}`,
            //         type: 'warning'
            //     });
            //     return;
            // }
            let res;
            if (this.chooseItem.isResume == true) {
                res = await PoProducedResume(params);
            } else {
                res = await PoProducedStart(params);
            }
            this.GetProcessOrderView();
            this.$emit('loadProgress', this.EquipmentId);
            this.StartModel = false;
            Message({
                message: res.msg,
                type: 'success'
            });
        },
        handleSelectionChange(val) {
            if (val != null) {
                this.selectTabelData = val;
                this.selectTabelData.Material = this.selectTabelData.MaterialName + '-' + this.selectTabelData.MaterialCode;
                this.tablechooselist = 1;
            } else {
                this.selectTabelData = {};
                this.tablechooselist = 0;
            }
        },
        handleSizeChange(val) {
            this.pageOptions.pageSize = val;
            this.GetProcessOrderView();
            this.getPoExecutionHistroy();
        },
        handleCurrentChange(val) {
            this.pageOptions.page = val;
            this.GetProcessOrderView();
            this.getPoExecutionHistroy();
        }
    }
};
</script>
<style lang="scss" scoped>
.POManagement {
    .searchboxtitle {
        font-size: 1.7vh;
        color: #767777;
        padding-bottom: 5px;
        margin-left: 10px;
    }
    .el-tabs {
        height: 97%;
    }
    .subsubtabs {
        .el-tabs--border-card {
            border: 0 !important;
            box-shadow: none !important;
        }
    }
    .paginationbox {
        height: 10vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .dialogdetailbox {
        display: flex;
        align-items: center;
        width: 100%;
        margin-top: 10px;
        .dialogdetailsinglelabel {
            font-weight: 600;
            width: 47%;
            text-align: right;
        }
        .dialogdetailsinglevalue {
            width: 78%;
            margin-left: 20px;
        }
    }
    .splitdetailbox {
        padding-bottom: 10px;
        border: 1px solid #e8e8e8;
        margin-bottom: 5px;
        .splitdetailboxtitle {
            background: #f5f5f5;
            height: 3.5vh;
            display: flex;
            align-items: center;
            padding-left: 5px;
            font-size: 1.1rem;
            color: #303133;
        }
        .detailsnote {
            background-color: #fdf6ec;
            border-color: #faecd8;
            color: #e6a23c;
            padding: 8px;
            font-size: 0.9rem;
            margin: 5px 10px 0px 10px;
        }
        .detailsnote2 {
            background-color: #fdf6ec;
            border-color: #faecd8;
            color: #e6a23c;
            padding: 8px;
            font-size: 1.2rem;
            margin: 5px 10px 0px 10px;
        }
        .detailsnote3 {
            background-color: #f5fdec;
            border-color: #faecd8;
            color: hsl(135, 55%, 44%);
            padding: 8px;
            font-size: 1.2rem;
            margin: 5px 10px 0px 10px;
        }
        .splitdetailboxtitleTag {
            margin-left: 5px;
            background: #5cb85c;
            color: #fff;
            border-color: #5cb85c;
        }
    }
}
.el-dialog__body {
    .el-input {
        width: 250px !important;
    }
    .longwidthinput {
        .el-input {
            width: 400px !important;
        }
        .el-select {
            width: 400px !important;
        }
    }
    .el-select {
        width: 250px !important;
    }
}
</style>
