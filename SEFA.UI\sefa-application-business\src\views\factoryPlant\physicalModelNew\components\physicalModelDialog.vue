<!-- add 新增 -->
<!-- delete 批量删除 -->
<!-- edit 修改 -->
<template>
    <v-dialog v-model="dialog" persistent max-width="720px">
        <!-- 新增 -->
        <v-card ref="form">
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                {{ $t('DFM_WLMX._XZMX') }}
                <v-icon @click="closedialog">mdi-close</v-icon>
            </v-card-title>
            <!-- 表单内容 -->
            <v-card-text class="mt-7">
                <v-container>
                    <v-form ref="addform" v-model="valid">
                        <v-row>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <treeselect v-model="ParentNameId.ParentId" no-results-text="暂无数据" :multiple="false"
                                    :placeholder="$t('DFM_WLMX._SJCD')" :options="treeDatas" :normalizer="normalizer" />
                            </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-text-field ref="EquipmentName" v-model="formModel.EquipmentName"
                                    :rules="[v => !!v || '名称不能为空']" outlined dense :label="$t('DFM_WLMX._MC')"
                                    required></v-text-field>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-text-field ref="EquipmentCode" v-model="formModel.EquipmentCode"
                                    :rules="[v => !!v || '编码不能为空']" outlined dense :label="$t('DFM_WLMX._BM')"
                                    required></v-text-field>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-select v-model="formModel.Level" :items="rootitems" clearable dense outlined
                                    :label="$t('DFM_WLMX._LX')" @change="rootChange"></v-select>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-text-field ref="SortNumber" v-model="formModel.SortNumber"
                                    :rules="[v => !!v || '排序号不能为空']" outlined dense label="排序号"
                                    required></v-text-field>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-radio-group v-model="formModel.Enabled" row class="my-0">
                                    <template #label>
                                        <div>是否启用</div>
                                    </template>
                                    <v-radio :label="$t('DFM_WLMX._GB')" :value="0"></v-radio>
                                    <v-radio :label="$t('DFM_WLMX._QY')" :value="1"></v-radio>
                                </v-radio-group>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12">
                                <v-textarea v-model="formModel.Remark" rows="2" outlined
                                    :label="$t('DFM_WLMX._MS')"></v-textarea>
                            </v-col>
                        </v-row>
                    </v-form>
                </v-container>
            </v-card-text>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSubmit">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closedialog">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { EquipmentSaveForm } from '@/api/factoryPlant/physicalModel.js';

export default {
    name: 'DataDictionaryDialog',
    props: {
        parentId: {
            type: String,
            default: ''
        },
        treeDatas: {
            type: Array,
            default: () => []
        },
        rootitems: {
            type: Array,
            default: () => []
        },
        tableItem: {
            type: Object,
            default: () => { }
        },
        hasChildren: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            // 提交表单数据
            valid: true,
            dialog: false,
            checkbox: true,
            formModel: {
                ParentId: null,
                EquipmentCode: null,
                EquipmentName: null,
                Level: null,
                Enabled: 1,
                Remark: null,
                SortNumber: 1
            },
            editformModel: {},
            options: [],
            normalizer(node) {
                return {
                    id: node.id,
                    label: node.name,
                    children: node.children
                };
            }
        };
    },
    computed: {
        ParentNameId() {
            return {
                ParentId: this.parentId
            };
        },
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal && this.tableItem.ID) {
                    this.formModel = {
                        ParentName: this.tableItem.ParentName,
                        ParentId: this.tableItem.ParentId == '0' ? null : this.tableItem.ParentId,
                        EquipmentCode: this.tableItem.EquipmentCode,
                        EquipmentName: this.tableItem.EquipmentName,
                        Level: this.tableItem.Level,
                        Enabled: this.tableItem.Enabled ? 1 : 0,
                        Remark: this.tableItem.Remark,
                        SortNumber: this.tableItem.SortNumber
                    }
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        // 初始化表单
        initFrom() {
            this.$refs.addform.reset();
            // this.formModel = {
            //     ParentId: null,
            //     EquipmentCode: null,
            //     EquipmentName: null,
            //     Level: null,
            //     Enabled: 1,
            //     Remark: null
            // }
            this.$nextTick(() => {
                this.formModel.Enabled = 1;
            });
        },
        //新增
        async addSubmit() {
            console.log(this.hasChildren);
            let fromvalidate = await this.$refs.addform.validate();
            if (fromvalidate) {
                let params = {
                    ParentId: this.ParentNameId.ParentId || '0',
                    EquipmentCode: this.formModel.EquipmentCode,
                    EquipmentName: this.formModel.EquipmentName,
                    Level: this.formModel.Level,
                    Enabled: this.formModel.Enabled,
                    Remark: this.formModel.Remark,
                    SortNumber: this.formModel.SortNumber,
                    ID: this.tableItem.ID ? this.tableItem.ID : ''
                };
                let res = await EquipmentSaveForm(params);
                if (res.success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: '添加成功', color: 'success' });
                    this.dialog = this.checkbox ? false : true;
                    this.$emit('getdata')
                    // this.$parent.$parent.GetEquipmentPageList();
                    // this.$parent.$parent.GetEquipmentTree();
                    this.initFrom();
                }
            }
        },
        //编辑
        async editSubmit(type, item) {
            if (type && type == 'Enabled') {
                let params = {
                    ParentId: item.ParentId,
                    EquipmentCode: item.EquipmentCode,
                    EquipmentName: item.EquipmentName,
                    Level: item.Level,
                    Enabled: item.Enabled ? 1 : 0,
                    Remark: item.Remark,
                    sortNumber: item.SortNumber,
                    ID: item.ID
                };
                let res = await EquipmentSaveForm(params);
                if (res.success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: '修改成功', color: 'success' });
                }
            } else {
                console.log(this.tableItem);
                let fromvalidate = await this.$refs.addform.validate();
                if (fromvalidate) {
                    let params = {
                        ParentId: this.editformModel.ParentId,
                        EquipmentCode: this.editformModel.EquipmentCode,
                        EquipmentName: this.editformModel.EquipmentName,
                        Level: this.editformModel.Level,
                        Enabled: this.editformModel.Enabled,
                        Remark: this.editformModel.Remark,
                        ID: this.tableItem.ID
                    };
                    let res = await EquipmentSaveForm(params);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '修改成功', color: 'success' });
                        this.dialog = this.checkbox ? false : true;
                        this.$parent.$parent.GetEquipmentPageList();
                        this.$parent.$parent.GetEquipmentTree();
                        this.initFrom();
                    }
                }
            }
        },
        // 跟节点选择
        rootChange(v) {
            this.formModel.Level = v;
        },
        //关闭取消
        closedialog() {
            this.dialog = false;
            this.$refs.addform.reset();
            this.$nextTick(() => {
                this.formModel.Enabled = 1 + '';
            });
        }
    }
};
</script>
