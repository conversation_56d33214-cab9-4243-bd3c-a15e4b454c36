<template>
    <div class="usemystyle Tipping precheck">
        <div class="InventorySearchBox">
            <!-- <div class="searchbox">
                <div :class="'searchtipbox status' + TippingStatus">{{ this.$t('POListTipping.Tipping') }}：{{ filiterTippingStatus(TippingStatus) }}</div>
            </div> -->
            <div class="searchbox">
                <!--  -->
                <el-button @click="startTip()" class="tablebtn" :disabled="PrepStatus == '0' ? false : true" style="margin-left: 5px; width: 120px" size="small" icon="el-icon-caret-right">
                    {{ this.$t('POListTipping.StartTipping') }}
                </el-button>
                <!--  -->
                <el-button @click="startScan()" class="tablebtn" :disabled="PrepStatus == '1' ? false : true" style="margin-left: 5px; width: 120px" size="small" icon="el-icon-full-screen">
                    {{ this.$t('POListTipping.Scan') }}
                </el-button>
                <el-button @click="TippingOver()" class="tablebtn" :disabled="PrepStatus == '2' ? false : true" style="margin-left: 5px; width: 120px" size="small">
                    {{ this.$t('POListTipping.TippingOver') }}
                </el-button>
                <el-button @click="TippingOver(true)" class="tablebtn" :disabled="PrepStatus == '1' ? false : true" style="margin-left: 5px; width: 120px" size="small">
                    {{ this.$t('POListTipping.ForcedCompletion') }}
                </el-button>
                <div class="searchtipbox" v-if="PrepStatus == '0'" style="background: #e1f5f6; color: #426777; font-weight: 600">{{ this.$t('POListTipping.TippingText') }}</div>
                <el-button style="margin-left: 5px" size="small" icon="el-icon-refresh" @click="getsearch()">{{ this.$t('Inventory.refresh') }}</el-button>
                <div class="searchboxTitle" style="font-size: 16px" v-if="Content != ''">DCS当前投料信号：{{ Content }}</div>
                <div class="searchboxTitle" style="font-size: 16px" v-if="ContentValue != ''">允许投料属性值：{{ ContentValue }}</div>
            </div>
        </div>
        <div class="tablebox">
            <el-table :data="tableList" style="width: 100%" height="560">
                <el-table-column
                    v-for="(item, index) in header"
                    :fixed="item.fixed ? item.fixed : false"
                    :key="index"
                    :align="item.align"
                    :prop="item.prop ? item.prop : item.value"
                    :label="$t(`$vuetify.dataTable.${tableId}.${item.value}`)"
                    :width="item.width"
                >
                    <template v-slot:header="scope">
                        <span v-if="item.icon">
                            <i :class="item.icon"></i>
                        </span>
                        <span v-if="!item.icon">{{ scope.column.label }}</span>
                    </template>
                    <template slot-scope="scope">
                        <i class="el-icon-document" v-if="scope.column.property == 'detail'" @click="opendetailmodel(scope.row)"></i>
                        <span v-if="scope.column.property != 'detail'">
                            <span v-if="scope.column.property == 'Quantity'">{{ scope.row.Quantity }}{{ scope.row.Unit1 }}</span>
                            <span v-else-if="scope.column.property == 'Quantity2'">{{ scope.row.Quantity2 }}{{ scope.row.Unit1 }}</span>
                            <span v-else-if="scope.column.property == 'PrepStatus'">
                                <span v-if="Number(scope.row.PrepStatus) >= 7">
                                    <i class="el-icon-check"></i>
                                </span>
                            </span>
                            <span v-else-if="scope.column.property == 'Quantity4'">{{ scope.row.Quantity4 }}{{ scope.row.Unit1 }}</span>
                            <span v-else>{{ scope.row[item.prop] }}</span>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <div class="paginationbox">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="pageOptions.page"
                    :page-sizes="pageOptions.pageSizeitems"
                    :page-size="pageOptions.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageOptions.total"
                    background
                ></el-pagination>
            </div>
        </div>
        <QRcode :ref="'QRcode' + EquipmentId" @getQRcodesRes="getQRcodesRes"></QRcode>
        <el-drawer size="55%" :wrapperClosable="false" @close="closeDraw" :title="$t('POListTipping.Tipping')" :visible.sync="detailShow" direction="rtl">
            <div class="InventorySearchBox">
                <div class="searchbox">
                    <div class="inputformbox" size="small" style="width: 300px">
                        <el-input :placeholder="$t('precheck.TraceCode')" v-model="TraceCode" @keyup.enter.native="searchInventory()">
                            <template slot="append"><i slot="suffix" class="el-icon-full-screen" @click="searchInventory()"></i></template>
                        </el-input>
                    </div>
                    <el-button class="tablebtn" icon="el-icon-refresh-left" @click="ShowQRCode()">
                        {{ $t('Consume.Scan') }}
                    </el-button>
                    <div class="preparaStatusbox" style="font-size: 16px">{{ $t('GLOBAL.Number') }}：{{ count }}</div>
                </div>
            </div>
            <el-table :data="drawertableList" style="width: 100%">
                <el-table-column
                    v-for="(item, index) in drawerheader"
                    :key="index"
                    :fixed="item.fixed ? item.fixed : false"
                    :align="item.align"
                    :prop="item.prop ? item.prop : item.value"
                    :label="$t(`$vuetify.dataTable.${tableId2}.${item.value}`)"
                    :width="item.width"
                >
                    <template v-slot:header="scope">
                        <span v-if="item.icon">
                            <i :class="item.icon"></i>
                        </span>
                        <span v-if="!item.icon">{{ scope.column.label }}</span>
                    </template>
                    <template slot-scope="scope">
                        <span v-if="scope.column.property != 'detail'">
                            <span v-if="scope.column.property == 'Material'">
                                <div>{{ scope.row.MaterialCode }}</div>
                                <div style="color: #808080">{{ scope.row.MaterialName }}</div>
                            </span>
                            <span v-else-if="scope.column.property == 'Quantity'">{{ scope.row.Quantity }}{{ scope.row.Unit1 }}</span>
                            <span v-else-if="scope.column.property == 'Precheckestatus'">
                                <div
                                    class="preparaTableStatusbox"
                                    style="color: black"
                                    :style="{ background: scope.row.Precheckestatus == '0' ? '#FFA500' : scope.row.Precheckestatus == '1' ? '#FFA500' : '#3DCD58' }"
                                >
                                    {{ scope.row.Precheckestatus == '0' ? '未检查' : scope.row.Precheckestatus == '1' ? '未投料' : '已投料' }}
                                </div>
                            </span>
                            <span v-else>{{ scope.row[item.prop] }}</span>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
        </el-drawer>
    </div>
</template>
<script>
import '@/views/Inventory/mystyle.scss';
import { POManagemenTipping, TippingDrawColumn } from '@/columns/factoryPlant/tableHeaders';
import { Message, MessageBox } from 'element-ui';
import { MygetContentValue, MygetContent, TippingCount, GetTippingSclist, GetBatchEntity, GetTippingMlistView, OverTipping, StartTipping, ScanTipping } from '@/api/Inventory/Overview.js';

export default {
    name: 'Consume',
    data() {
        return {
            QuickSearch: '',
            TippingStatus: 1,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            header: POManagemenTipping,
            tableId2: 'INV_YJC',
            tableList: [],
            tableId: 'PRO_Tipping',
            PrepStatus: '',
            Content: '',
            ContentValue: '',
            countFlag: false,
            detailShow: false,
            TraceCode: '',
            count: '',
            drawertableList: [],
            drawerheader: TippingDrawColumn,
            BatchId: '',
            RunEquipmentId: '',
            SortOrder: '',
            PoExecutionId: '',
            EquipmentId: ''
        };
    },
    mounted() {
        this.changePagination();
    },
    methods: {
        ShowQRCode() {
            this.$refs[`QRcode${this.EquipmentId}`].getQRcode();
        },
        // 获取查询结果
        getQRcodesRes(val) {
            console.log(val, 123123);
            this.TraceCode = val.text;
            this.searchInventory();
        },
        getEquipmentModal(item, Equipmentitem) {
            if (Equipmentitem) {
                this.EquipmentId = Equipmentitem.ID;
                this.BatchId = Equipmentitem.BatchId;
                this.RunEquipmentId = Equipmentitem.RunEquipmentId;
                this.PoExecutionId = Equipmentitem.ExecutionId;
                this.getContent();
            } else {
                this.BatchId = '';
                this.RunEquipmentId = '';
                this.PoExecutionId = '';
            }
            this.GetTippinglist();
            this.BatchEntity();
        },
        async getContent() {
            let params = {
                ExecutionId: this.PoExecutionId,
                Name: 'RequestFeeding'
            };
            let res = await MygetContent(params);
            let data = res.response;
            this.Content = data.Content;
        },
        async getContentValue() {
            let params = {
                EquipmentId: this.EquipmentId,
                FunctionCode: 'Tipping',
                PropertyCode: 'ButtonEnable'
            };
            let res = await MygetContentValue(params);
            if (res.response == null) {
                res.response = '';
            }
            this.ContentValue = res.response;
        },
        async TippingOver(parm) {
            let params = {
                SortOrder: this.SortOrder,
                BatchId: this.BatchId,
                IsForcedCompletion: false,
                RunEquipmentId: this.RunEquipmentId,
                PoExecutionId: this.PoExecutionId
            };
            if (parm) {
                params.IsForcedCompletion = parm;
            }
            let res = await OverTipping(params);
            if (res.success) {
                Message({
                    message: res.msg,
                    type: 'success'
                });
                this.BatchEntity();
            }
        },
        getsearch() {
            // this.MyGetTippingSclist();
            this.GetTippinglist();
            this.BatchEntity();
        },
        async BatchEntity() {
            if (this.BatchId != '') {
                let params = {
                    BatchId: this.BatchId,
                    RunEquipmentId: this.RunEquipmentId,
                    PoExecutionId: this.PoExecutionId
                };
                let res = await GetBatchEntity(params);
                if (res) {
                    this.SortOrder = res.response.SortOrder;
                    this.PrepStatus = res.response.Status;
                }
            } else {
                this.PrepStatus = '';
            }
        },
        closeDraw() {
            this.GetTippinglist();
        },
        async startScan() {
            this.reLoadScan();
            this.detailShow = true;
        },
        async searchInventory() {
            if (this.TraceCode === '' || this.TraceCode === null) {
                Message({
                    message: `${this.$t('ConsumptionHistory.NonSSCC')}`,
                    type: 'error'
                });
                return;
            }
            let params = {
                BatchId: this.BatchId,
                Tracecode: this.TraceCode,
                RunEquipmentId: this.RunEquipmentId,
                PoExecutionId: this.PoExecutionId
            };
            let res = await ScanTipping(params);
            if (res.success) {
                this.TraceCode = '';
                Message({
                    message: res.msg,
                    type: 'success'
                });
                this.reLoadScan();
                this.GetTippinglist();
                this.BatchEntity();
            }
        },
        async reLoadScan() {
            let params = {
                BatchId: this.BatchId,
                SortOrder: this.SortOrder
            };
            let res = await GetTippingSclist(params);
            this.drawertableList = res.response;
            let params2 = {
                BatchId: this.BatchId,
                SortOrder: this.SortOrder
            };
            let res2 = await TippingCount(params2);
            this.count = res2.response;
            if (this.count.split('/')[0] == this.count.split('/')[1]) {
                this.countFlag = true;
            } else {
                this.countFlag = false;
            }
        },
        async startTip() {
            let parmas = {
                SortOrder: this.SortOrder,
                BatchId: this.BatchId,
                RunEquipmentId: this.RunEquipmentId,
                PoExecutionId: this.PoExecutionId
            };
            let res = await StartTipping(parmas);
            this.GetTippinglist();
            this.BatchEntity();
            Message({
                message: res.msg,
                type: 'success'
            });
        },
        changePagination() {
            let el2 = document.getElementsByClassName(`el-select-dropdown__item`);
            for (let i = 0; i < el2.length; i++) {
                el2[i].innerHTML = el2[i].innerHTML.replace('条/页', this.$t('PAGINATION.MYPAGE'));
            }
        },
        async GetTippinglist() {
            let params = {
                BatchId: this.BatchId,
                RunEquipmentId: this.RunEquipmentId,
                pageIndex: this.pageOptions.page,
                pageSize: this.pageOptions.pageSize
            };
            let res = await GetTippingMlistView(params);
            if (res) {
                this.tableList = res.response.data;
                this.pageOptions.total = res.response.dataCount;
            }
            let el = document.getElementsByClassName(`el-pagination__total`);
            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;
            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');
            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));
        },
        handleSizeChange(val) {
            this.pageOptions.pageSize = val;
            this.GetTippinglist();
        },
        handleCurrentChange(val) {
            this.pageOptions.page = val;
            this.GetTippinglist();
        }
    }
};
</script>
<style lang="scss" scoped>
.Tipping {
    .searchtipbox {
        margin: 0 5px;
        height: 30px;
        padding: 0 2vh;
        border-radius: 5px;
        display: flex;
        margin-bottom: 0.5vh;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 14px;
    }
    .searchboxtitle {
        font-size: 1.7vh;
        color: #767777;
        padding-bottom: 5px;
        margin-left: 10px;
    }

    .el-tabs {
        height: 97%;
    }

    .subsubtabs {
        .el-tabs--border-card {
            border: 0 !important;
            box-shadow: none !important;
        }
    }

    .paginationbox {
        height: 10vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .dialogdetailbox {
        display: flex;
        align-items: center;
        width: 100%;
        margin-top: 10px;

        .dialogdetailsinglelabel {
            font-weight: 600;
            width: 47%;
            text-align: right;
        }

        .dialogdetailsinglevalue {
            width: 78%;
            margin-left: 20px;
        }
    }

    .splitdetailbox {
        padding-bottom: 10px;
        border: 1px solid #e8e8e8;
        margin-bottom: 5px;

        .splitdetailboxtitle {
            background: #f5f5f5;
            height: 3.5vh;
            display: flex;
            align-items: center;
            padding-left: 5px;
            font-size: 1.1rem;
            color: #303133;
        }

        .detailsnote {
            background-color: #fdf6ec;
            border-color: #faecd8;
            color: #e6a23c;
            padding: 8px;
            font-size: 0.9rem;
            margin: 5px 10px 0px 10px;
        }

        .splitdetailboxtitleTag {
            margin-left: 5px;
            background: #5cb85c;
            color: #fff;
            border-color: #5cb85c;
        }
    }
}

#Tipping {
    .el-input {
        width: 250px !important;
    }

    .el-select {
        width: 250px !important;
    }
}
</style>
