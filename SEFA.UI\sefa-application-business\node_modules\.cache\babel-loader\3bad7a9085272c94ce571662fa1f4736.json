{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\components\\Tipping.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\components\\Tipping.vue", "mtime": 1750254216284}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICdAL3ZpZXdzL0ludmVudG9yeS9teXN0eWxlLnNjc3MnOwppbXBvcnQgeyBQT01hbmFnZW1lblRpcHBpbmcsIFRpcHBpbmdEcmF3Q29sdW1uIH0gZnJvbSAnQC9jb2x1bW5zL2ZhY3RvcnlQbGFudC90YWJsZUhlYWRlcnMnOwppbXBvcnQgeyBNZXNzYWdlLCBNZXNzYWdlQm94IH0gZnJvbSAnZWxlbWVudC11aSc7CmltcG9ydCB7IE15Z2V0Q29udGVudFZhbHVlLCBNeWdldENvbnRlbnQsIFRpcHBpbmdDb3VudCwgR2V0VGlwcGluZ1NjbGlzdCwgR2V0QmF0Y2hFbnRpdHksIEdldFRpcHBpbmdNbGlzdFZpZXcsIE92ZXJUaXBwaW5nLCBTdGFydFRpcHBpbmcsIFNjYW5UaXBwaW5nIH0gZnJvbSAnQC9hcGkvSW52ZW50b3J5L092ZXJ2aWV3LmpzJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdDb25zdW1lJywKCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIFF1aWNrU2VhcmNoOiAnJywKICAgICAgVGlwcGluZ1N0YXR1czogMSwKICAgICAgcGFnZU9wdGlvbnM6IHsKICAgICAgICB0b3RhbDogMCwKICAgICAgICBwYWdlOiAxLAogICAgICAgIC8vIOW9k+WJjemhteeggQogICAgICAgIHBhZ2VTaXplOiAyMCwKICAgICAgICAvLyDkuIDpobXmlbDmja4KICAgICAgICBwYWdlQ291bnQ6IDEsCiAgICAgICAgLy8g6aG156CB5YiG6aG15pWwCiAgICAgICAgcGFnZVNpemVpdGVtczogWzEwLCAyMCwgNTAsIDEwMCwgNTAwXQogICAgICB9LAogICAgICBoZWFkZXI6IFBPTWFuYWdlbWVuVGlwcGluZywKICAgICAgdGFibGVJZDI6ICdJTlZfWUpDJywKICAgICAgdGFibGVMaXN0OiBbXSwKICAgICAgdGFibGVJZDogJ1BST19UaXBwaW5nJywKICAgICAgUHJlcFN0YXR1czogJycsCiAgICAgIENvbnRlbnQ6ICcnLAogICAgICBDb250ZW50VmFsdWU6ICcnLAogICAgICBjb3VudEZsYWc6IGZhbHNlLAogICAgICBkZXRhaWxTaG93OiBmYWxzZSwKICAgICAgVHJhY2VDb2RlOiAnJywKICAgICAgY291bnQ6ICcnLAogICAgICBkcmF3ZXJ0YWJsZUxpc3Q6IFtdLAogICAgICBkcmF3ZXJoZWFkZXI6IFRpcHBpbmdEcmF3Q29sdW1uLAogICAgICBCYXRjaElkOiAnJywKICAgICAgUnVuRXF1aXBtZW50SWQ6ICcnLAogICAgICBTb3J0T3JkZXI6ICcnLAogICAgICBQb0V4ZWN1dGlvbklkOiAnJywKICAgICAgRXF1aXBtZW50SWQ6ICcnCiAgICB9OwogIH0sCgogIG1vdW50ZWQoKSB7CiAgICB0aGlzLmNoYW5nZVBhZ2luYXRpb24oKTsKICB9LAoKICBtZXRob2RzOiB7CiAgICBTaG93UVJDb2RlKCkgewogICAgICB0aGlzLiRyZWZzW2BRUmNvZGUke3RoaXMuRXF1aXBtZW50SWR9YF0uZ2V0UVJjb2RlKCk7CiAgICB9LAoKICAgIC8vIOiOt+W<PERSON>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"}, {"version": 3, "mappings": "AAiIA;AACA;AACA;AACA;AAEA;EACAA,eADA;;EAEAC;IACA;MACAC,eADA;MAEAC,gBAFA;MAGAC;QACAC,QADA;QAEAC,OAFA;QAEA;QACAC,YAHA;QAGA;QACAC,YAJA;QAIA;QACAC;MALA,CAHA;MAUAC,0BAVA;MAWAC,mBAXA;MAYAC,aAZA;MAaAC,sBAbA;MAcAC,cAdA;MAeAC,WAfA;MAgBAC,gBAhBA;MAiBAC,gBAjBA;MAkBAC,iBAlBA;MAmBAC,aAnBA;MAoBAC,SApBA;MAqBAC,mBArBA;MAsBAC,+BAtBA;MAuBAC,WAvBA;MAwBAC,kBAxBA;MAyBAC,aAzBA;MA0BAC,iBA1BA;MA2BAC;IA3BA;EA6BA,CAhCA;;EAiCAC;IACA;EACA,CAnCA;;EAoCAC;IACAC;MACA;IACA,CAHA;;IAIA;IACAC;MACAC;MACA;MACA;IACA,CATA;;IAUAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA,CANA,MAMA;QACA;QACA;QACA;MACA;;MACA;MACA;IACA,CAxBA;;IAyBA;MACA;QACAC,+BADA;QAEAC;MAFA;MAIA;MACA;MACA;IACA,CAjCA;;IAkCA;MACA;QACAR,6BADA;QAEAS,uBAFA;QAGAC;MAHA;MAKA;;MACA;QACAC;MACA;;MACA;IACA,CA7CA;;IA8CA;MACA;QACAb,yBADA;QAEAF,qBAFA;QAGAgB,yBAHA;QAIAf,mCAJA;QAKAE;MALA;;MAOA;QACAc;MACA;;MACA;;MACA;QACAC;UACAC,gBADA;UAEAC;QAFA;QAIA;MACA;IACA,CAjEA;;IAkEAC;MACA;MACA;MACA;IACA,CAtEA;;IAuEA;MACA;QACA;UACArB,qBADA;UAEAC,mCAFA;UAGAE;QAHA;QAKA;;QACA;UACA;UACA;QACA;MACA,CAXA,MAWA;QACA;MACA;IACA,CAtFA;;IAuFAmB;MACA;IACA,CAzFA;;IA0FA;MACA;MACA;IACA,CA7FA;;IA8FA;MACA;QACAJ;UACAC,mDADA;UAEAC;QAFA;QAIA;MACA;;MACA;QACApB,qBADA;QAEAuB,yBAFA;QAGAtB,mCAHA;QAIAE;MAJA;MAMA;;MACA;QACA;QACAe;UACAC,gBADA;UAEAC;QAFA;QAIA;QACA;QACA;MACA;IACA,CAvHA;;IAwHA;MACA;QACApB,qBADA;QAEAE;MAFA;MAIA;MACA;MACA;QACAF,qBADA;QAEAE;MAFA;MAIA;MACA;;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA;IACA,CA1IA;;IA2IA;MACA;QACAA,yBADA;QAEAF,qBAFA;QAGAC,mCAHA;QAIAE;MAJA;MAMA;MACA;MACA;MACAe;QACAC,gBADA;QAEAC;MAFA;IAIA,CAzJA;;IA0JAI;MACA;;MACA;QACAC;MACA;IACA,CA/JA;;IAgKA;MACA;QACAzB,qBADA;QAEAC,mCAFA;QAGAyB,gCAHA;QAIA1C;MAJA;MAMA;;MACA;QACA;QACA;MACA;;MACA;MACA2C;MACA;MACAC;IACA,CAhLA;;IAiLAC;MACA;MACA;IACA,CApLA;;IAqLAC;MACA;MACA;IACA;;EAxLA;AApCA", "names": ["name", "data", "QuickSearch", "TippingStatus", "pageOptions", "total", "page", "pageSize", "pageCount", "pageSizeitems", "header", "tableId2", "tableList", "tableId", "PrepStatus", "Content", "ContentValue", "countFlag", "detailShow", "TraceCode", "count", "drawertableList", "drawerheader", "BatchId", "RunEquipmentId", "SortOrder", "PoExecutionId", "EquipmentId", "mounted", "methods", "ShowQRCode", "getQRcodesRes", "console", "getEquipmentModal", "ExecutionId", "Name", "FunctionCode", "PropertyCode", "res", "IsForcedCompletion", "params", "Message", "message", "type", "<PERSON><PERSON>ch", "closeDraw", "Tracecode", "changePagination", "el2", "pageIndex", "el", "el3", "handleSizeChange", "handleCurrentChange"], "sourceRoot": "src/views/Producting/Overview/components", "sources": ["Tipping.vue"], "sourcesContent": ["<template>\r\n    <div class=\"usemystyle Tipping precheck\">\r\n        <div class=\"InventorySearchBox\">\r\n            <!-- <div class=\"searchbox\">\r\n                <div :class=\"'searchtipbox status' + TippingStatus\">{{ this.$t('POListTipping.Tipping') }}：{{ filiterTippingStatus(TippingStatus) }}</div>\r\n            </div> -->\r\n            <div class=\"searchbox\">\r\n                <!--  -->\r\n                <el-button @click=\"startTip()\" class=\"tablebtn\" :disabled=\"PrepStatus == '0' ? false : true\" style=\"margin-left: 5px; width: 120px\" size=\"small\" icon=\"el-icon-caret-right\">\r\n                    {{ this.$t('POListTipping.StartTipping') }}\r\n                </el-button>\r\n                <!--  -->\r\n                <el-button @click=\"startScan()\" class=\"tablebtn\" :disabled=\"PrepStatus == '1' ? false : true\" style=\"margin-left: 5px; width: 120px\" size=\"small\" icon=\"el-icon-full-screen\">\r\n                    {{ this.$t('POListTipping.Scan') }}\r\n                </el-button>\r\n                <el-button @click=\"TippingOver()\" class=\"tablebtn\" :disabled=\"PrepStatus == '2' ? false : true\" style=\"margin-left: 5px; width: 120px\" size=\"small\">\r\n                    {{ this.$t('POListTipping.TippingOver') }}\r\n                </el-button>\r\n                <el-button @click=\"TippingOver(true)\" class=\"tablebtn\" :disabled=\"PrepStatus == '1' ? false : true\" style=\"margin-left: 5px; width: 120px\" size=\"small\">\r\n                    {{ this.$t('POListTipping.ForcedCompletion') }}\r\n                </el-button>\r\n                <div class=\"searchtipbox\" v-if=\"PrepStatus == '0'\" style=\"background: #e1f5f6; color: #426777; font-weight: 600\">{{ this.$t('POListTipping.TippingText') }}</div>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <div class=\"searchboxTitle\" style=\"font-size: 16px\" v-if=\"Content != ''\">DCS当前投料信号：{{ Content }}</div>\r\n                <div class=\"searchboxTitle\" style=\"font-size: 16px\" v-if=\"ContentValue != ''\">允许投料属性值：{{ ContentValue }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"tablebox\">\r\n            <el-table :data=\"tableList\" style=\"width: 100%\" height=\"560\">\r\n                <el-table-column\r\n                    v-for=\"(item, index) in header\"\r\n                    :fixed=\"item.fixed ? item.fixed : false\"\r\n                    :key=\"index\"\r\n                    :align=\"item.align\"\r\n                    :prop=\"item.prop ? item.prop : item.value\"\r\n                    :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                    :width=\"item.width\"\r\n                >\r\n                    <template v-slot:header=\"scope\">\r\n                        <span v-if=\"item.icon\">\r\n                            <i :class=\"item.icon\"></i>\r\n                        </span>\r\n                        <span v-if=\"!item.icon\">{{ scope.column.label }}</span>\r\n                    </template>\r\n                    <template slot-scope=\"scope\">\r\n                        <i class=\"el-icon-document\" v-if=\"scope.column.property == 'detail'\" @click=\"opendetailmodel(scope.row)\"></i>\r\n                        <span v-if=\"scope.column.property != 'detail'\">\r\n                            <span v-if=\"scope.column.property == 'Quantity'\">{{ scope.row.Quantity }}{{ scope.row.Unit1 }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity2'\">{{ scope.row.Quantity2 }}{{ scope.row.Unit1 }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'PrepStatus'\">\r\n                                <span v-if=\"Number(scope.row.PrepStatus) >= 7\">\r\n                                    <i class=\"el-icon-check\"></i>\r\n                                </span>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity4'\">{{ scope.row.Quantity4 }}{{ scope.row.Unit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n            <div class=\"paginationbox\">\r\n                <el-pagination\r\n                    @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageOptions.page\"\r\n                    :page-sizes=\"pageOptions.pageSizeitems\"\r\n                    :page-size=\"pageOptions.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next, jumper\"\r\n                    :total=\"pageOptions.total\"\r\n                    background\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <QRcode :ref=\"'QRcode' + EquipmentId\" @getQRcodesRes=\"getQRcodesRes\"></QRcode>\r\n        <el-drawer size=\"55%\" :wrapperClosable=\"false\" @close=\"closeDraw\" :title=\"$t('POListTipping.Tipping')\" :visible.sync=\"detailShow\" direction=\"rtl\">\r\n            <div class=\"InventorySearchBox\">\r\n                <div class=\"searchbox\">\r\n                    <div class=\"inputformbox\" size=\"small\" style=\"width: 300px\">\r\n                        <el-input :placeholder=\"$t('precheck.TraceCode')\" v-model=\"TraceCode\" @keyup.enter.native=\"searchInventory()\">\r\n                            <template slot=\"append\"><i slot=\"suffix\" class=\"el-icon-full-screen\" @click=\"searchInventory()\"></i></template>\r\n                        </el-input>\r\n                    </div>\r\n                    <el-button class=\"tablebtn\" icon=\"el-icon-refresh-left\" @click=\"ShowQRCode()\">\r\n                        {{ $t('Consume.Scan') }}\r\n                    </el-button>\r\n                    <div class=\"preparaStatusbox\" style=\"font-size: 16px\">{{ $t('GLOBAL.Number') }}：{{ count }}</div>\r\n                </div>\r\n            </div>\r\n            <el-table :data=\"drawertableList\" style=\"width: 100%\">\r\n                <el-table-column\r\n                    v-for=\"(item, index) in drawerheader\"\r\n                    :key=\"index\"\r\n                    :fixed=\"item.fixed ? item.fixed : false\"\r\n                    :align=\"item.align\"\r\n                    :prop=\"item.prop ? item.prop : item.value\"\r\n                    :label=\"$t(`$vuetify.dataTable.${tableId2}.${item.value}`)\"\r\n                    :width=\"item.width\"\r\n                >\r\n                    <template v-slot:header=\"scope\">\r\n                        <span v-if=\"item.icon\">\r\n                            <i :class=\"item.icon\"></i>\r\n                        </span>\r\n                        <span v-if=\"!item.icon\">{{ scope.column.label }}</span>\r\n                    </template>\r\n                    <template slot-scope=\"scope\">\r\n                        <span v-if=\"scope.column.property != 'detail'\">\r\n                            <span v-if=\"scope.column.property == 'Material'\">\r\n                                <div>{{ scope.row.MaterialCode }}</div>\r\n                                <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.Quantity }}{{ scope.row.Unit1 }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Precheckestatus'\">\r\n                                <div\r\n                                    class=\"preparaTableStatusbox\"\r\n                                    style=\"color: black\"\r\n                                    :style=\"{ background: scope.row.Precheckestatus == '0' ? '#FFA500' : scope.row.Precheckestatus == '1' ? '#FFA500' : '#3DCD58' }\"\r\n                                >\r\n                                    {{ scope.row.Precheckestatus == '0' ? '未检查' : scope.row.Precheckestatus == '1' ? '未投料' : '已投料' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n        </el-drawer>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { POManagemenTipping, TippingDrawColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { MygetContentValue, MygetContent, TippingCount, GetTippingSclist, GetBatchEntity, GetTippingMlistView, OverTipping, StartTipping, ScanTipping } from '@/api/Inventory/Overview.js';\r\n\r\nexport default {\r\n    name: 'Consume',\r\n    data() {\r\n        return {\r\n            QuickSearch: '',\r\n            TippingStatus: 1,\r\n            pageOptions: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            header: POManagemenTipping,\r\n            tableId2: 'INV_YJC',\r\n            tableList: [],\r\n            tableId: 'PRO_Tipping',\r\n            PrepStatus: '',\r\n            Content: '',\r\n            ContentValue: '',\r\n            countFlag: false,\r\n            detailShow: false,\r\n            TraceCode: '',\r\n            count: '',\r\n            drawertableList: [],\r\n            drawerheader: TippingDrawColumn,\r\n            BatchId: '',\r\n            RunEquipmentId: '',\r\n            SortOrder: '',\r\n            PoExecutionId: '',\r\n            EquipmentId: ''\r\n        };\r\n    },\r\n    mounted() {\r\n        this.changePagination();\r\n    },\r\n    methods: {\r\n        ShowQRCode() {\r\n            this.$refs[`QRcode${this.EquipmentId}`].getQRcode();\r\n        },\r\n        // 获取查询结果\r\n        getQRcodesRes(val) {\r\n            console.log(val, 123123);\r\n            this.TraceCode = val.text;\r\n            this.searchInventory();\r\n        },\r\n        getEquipmentModal(item, Equipmentitem) {\r\n            if (Equipmentitem) {\r\n                this.EquipmentId = Equipmentitem.ID;\r\n                this.BatchId = Equipmentitem.BatchId;\r\n                this.RunEquipmentId = Equipmentitem.RunEquipmentId;\r\n                this.PoExecutionId = Equipmentitem.ExecutionId;\r\n                this.getContent();\r\n            } else {\r\n                this.BatchId = '';\r\n                this.RunEquipmentId = '';\r\n                this.PoExecutionId = '';\r\n            }\r\n            this.GetTippinglist();\r\n            this.BatchEntity();\r\n        },\r\n        async getContent() {\r\n            let params = {\r\n                ExecutionId: this.PoExecutionId,\r\n                Name: 'RequestFeeding'\r\n            };\r\n            let res = await MygetContent(params);\r\n            let data = res.response;\r\n            this.Content = data.Content;\r\n        },\r\n        async getContentValue() {\r\n            let params = {\r\n                EquipmentId: this.EquipmentId,\r\n                FunctionCode: 'Tipping',\r\n                PropertyCode: 'ButtonEnable'\r\n            };\r\n            let res = await MygetContentValue(params);\r\n            if (res.response == null) {\r\n                res.response = '';\r\n            }\r\n            this.ContentValue = res.response;\r\n        },\r\n        async TippingOver(parm) {\r\n            let params = {\r\n                SortOrder: this.SortOrder,\r\n                BatchId: this.BatchId,\r\n                IsForcedCompletion: false,\r\n                RunEquipmentId: this.RunEquipmentId,\r\n                PoExecutionId: this.PoExecutionId\r\n            };\r\n            if (parm) {\r\n                params.IsForcedCompletion = parm;\r\n            }\r\n            let res = await OverTipping(params);\r\n            if (res.success) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n                this.BatchEntity();\r\n            }\r\n        },\r\n        getsearch() {\r\n            // this.MyGetTippingSclist();\r\n            this.GetTippinglist();\r\n            this.BatchEntity();\r\n        },\r\n        async BatchEntity() {\r\n            if (this.BatchId != '') {\r\n                let params = {\r\n                    BatchId: this.BatchId,\r\n                    RunEquipmentId: this.RunEquipmentId,\r\n                    PoExecutionId: this.PoExecutionId\r\n                };\r\n                let res = await GetBatchEntity(params);\r\n                if (res) {\r\n                    this.SortOrder = res.response.SortOrder;\r\n                    this.PrepStatus = res.response.Status;\r\n                }\r\n            } else {\r\n                this.PrepStatus = '';\r\n            }\r\n        },\r\n        closeDraw() {\r\n            this.GetTippinglist();\r\n        },\r\n        async startScan() {\r\n            this.reLoadScan();\r\n            this.detailShow = true;\r\n        },\r\n        async searchInventory() {\r\n            if (this.TraceCode === '' || this.TraceCode === null) {\r\n                Message({\r\n                    message: `${this.$t('ConsumptionHistory.NonSSCC')}`,\r\n                    type: 'error'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                Tracecode: this.TraceCode,\r\n                RunEquipmentId: this.RunEquipmentId,\r\n                PoExecutionId: this.PoExecutionId\r\n            };\r\n            let res = await ScanTipping(params);\r\n            if (res.success) {\r\n                this.TraceCode = '';\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n                this.reLoadScan();\r\n                this.GetTippinglist();\r\n                this.BatchEntity();\r\n            }\r\n        },\r\n        async reLoadScan() {\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                SortOrder: this.SortOrder\r\n            };\r\n            let res = await GetTippingSclist(params);\r\n            this.drawertableList = res.response;\r\n            let params2 = {\r\n                BatchId: this.BatchId,\r\n                SortOrder: this.SortOrder\r\n            };\r\n            let res2 = await TippingCount(params2);\r\n            this.count = res2.response;\r\n            if (this.count.split('/')[0] == this.count.split('/')[1]) {\r\n                this.countFlag = true;\r\n            } else {\r\n                this.countFlag = false;\r\n            }\r\n        },\r\n        async startTip() {\r\n            let parmas = {\r\n                SortOrder: this.SortOrder,\r\n                BatchId: this.BatchId,\r\n                RunEquipmentId: this.RunEquipmentId,\r\n                PoExecutionId: this.PoExecutionId\r\n            };\r\n            let res = await StartTipping(parmas);\r\n            this.GetTippinglist();\r\n            this.BatchEntity();\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        changePagination() {\r\n            let el2 = document.getElementsByClassName(`el-select-dropdown__item`);\r\n            for (let i = 0; i < el2.length; i++) {\r\n                el2[i].innerHTML = el2[i].innerHTML.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            }\r\n        },\r\n        async GetTippinglist() {\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                RunEquipmentId: this.RunEquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res = await GetTippingMlistView(params);\r\n            if (res) {\r\n                this.tableList = res.response.data;\r\n                this.pageOptions.total = res.response.dataCount;\r\n            }\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n        },\r\n        handleSizeChange(val) {\r\n            this.pageOptions.pageSize = val;\r\n            this.GetTippinglist();\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.pageOptions.page = val;\r\n            this.GetTippinglist();\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.Tipping {\r\n    .searchtipbox {\r\n        margin: 0 5px;\r\n        height: 30px;\r\n        padding: 0 2vh;\r\n        border-radius: 5px;\r\n        display: flex;\r\n        margin-bottom: 0.5vh;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: #fff;\r\n        font-size: 14px;\r\n    }\r\n    .searchboxtitle {\r\n        font-size: 1.7vh;\r\n        color: #767777;\r\n        padding-bottom: 5px;\r\n        margin-left: 10px;\r\n    }\r\n\r\n    .el-tabs {\r\n        height: 97%;\r\n    }\r\n\r\n    .subsubtabs {\r\n        .el-tabs--border-card {\r\n            border: 0 !important;\r\n            box-shadow: none !important;\r\n        }\r\n    }\r\n\r\n    .paginationbox {\r\n        height: 10vh;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin-top: 10px;\r\n\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 47%;\r\n            text-align: right;\r\n        }\r\n\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n\r\n    .splitdetailbox {\r\n        padding-bottom: 10px;\r\n        border: 1px solid #e8e8e8;\r\n        margin-bottom: 5px;\r\n\r\n        .splitdetailboxtitle {\r\n            background: #f5f5f5;\r\n            height: 3.5vh;\r\n            display: flex;\r\n            align-items: center;\r\n            padding-left: 5px;\r\n            font-size: 1.1rem;\r\n            color: #303133;\r\n        }\r\n\r\n        .detailsnote {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 0.9rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n\r\n        .splitdetailboxtitleTag {\r\n            margin-left: 5px;\r\n            background: #5cb85c;\r\n            color: #fff;\r\n            border-color: #5cb85c;\r\n        }\r\n    }\r\n}\r\n\r\n#Tipping {\r\n    .el-input {\r\n        width: 250px !important;\r\n    }\r\n\r\n    .el-select {\r\n        width: 250px !important;\r\n    }\r\n}\r\n</style>\r\n"]}]}