{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\plugins\\i18n.js", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\plugins\\i18n.js", "mtime": 1750217887201}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\eslint-loader\\index.js", "mtime": 1743379020994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgVnVlSTE4biBmcm9tICd2dWUtaTE4bic7ClZ1ZS51c2UoVnVlSTE4bik7CmltcG9ydCBzdG9yZSBmcm9tICcuLi9zdG9yZSc7CmltcG9ydCB6aEhhbnMgZnJvbSAnQC9sb2NhbGUvemgtSGFucy5qc29uJzsKaW1wb3J0IGVuIGZyb20gJ0AvbG9jYWxlL2VuLmpzb24nOwppbXBvcnQgemhDYW5zIGZyb20gJ0AvbG9jYWxlL2Nhbi1IYW5zLmpzb24nOwpjb25zdCB0cmFuc2xhdGlvbiA9IHsKICBjbjogemhIYW5zLAogIHpoY2FuOiB6aENhbnMsCiAgZW46IGVuCn07CmNvbnN0IGkxOG4gPSBuZXcgVnVlSTE4bih7CiAgbG9jYWxlOiBzdG9yZS5nZXR0ZXJzLmdldExvY2FsZSwKICAvLyBzZXQgbG9jYWxlCiAgbWVzc2FnZXM6IHRyYW5zbGF0aW9uIC8vIHNldCBsb2NhbGUgbWVzc2FnZXMKCn0pOwpzdG9yZS5jb21taXQoJ1NFVF9UUkFOU0xBVElPTicsIHRyYW5zbGF0aW9uKTsKZXhwb3J0IGRlZmF1bHQgaTE4bjs="}, {"version": 3, "names": ["<PERSON><PERSON>", "VueI18n", "use", "store", "zhHans", "en", "zhCans", "translation", "cn", "zhcan", "i18n", "locale", "getters", "getLocale", "messages", "commit"], "sources": ["C:/work/syngentagroup/SEFA_XZD/SEFA.UI/sefa-application-business/src/plugins/i18n.js"], "sourcesContent": ["import Vue from 'vue';\r\nimport VueI18n from 'vue-i18n';\r\nVue.use(VueI18n);\r\nimport store from '../store';\r\nimport zhHans from '@/locale/zh-Hans.json';\r\nimport en from '@/locale/en.json';\r\nimport zhCans from '@/locale/can-Hans.json';\r\n\r\nconst translation = {\r\n    cn: zhHans,\r\n    zhcan: zhCans,\r\n    en: en,\r\n\r\n};\r\nconst i18n = new VueI18n({\r\n    locale: store.getters.getLocale, // set locale\r\n    messages: translation // set locale messages\r\n});\r\n\r\nstore.commit('SET_TRANSLATION', translation);\r\n\r\nexport default i18n;"], "mappings": "AAAA,OAAOA,GAAP,MAAgB,KAAhB;AACA,OAAOC,OAAP,MAAoB,UAApB;AACAD,GAAG,CAACE,GAAJ,CAAQD,OAAR;AACA,OAAOE,KAAP,MAAkB,UAAlB;AACA,OAAOC,MAAP,MAAmB,uBAAnB;AACA,OAAOC,EAAP,MAAe,kBAAf;AACA,OAAOC,MAAP,MAAmB,wBAAnB;AAEA,MAAMC,WAAW,GAAG;EAChBC,EAAE,EAAEJ,MADY;EAEhBK,KAAK,EAAEH,MAFS;EAGhBD,EAAE,EAAEA;AAHY,CAApB;AAMA,MAAMK,IAAI,GAAG,IAAIT,OAAJ,CAAY;EACrBU,MAAM,EAAER,KAAK,CAACS,OAAN,CAAcC,SADD;EACY;EACjCC,QAAQ,EAAEP,WAFW,CAEC;;AAFD,CAAZ,CAAb;AAKAJ,KAAK,CAACY,MAAN,CAAa,iBAAb,EAAgCR,WAAhC;AAEA,eAAeG,IAAf"}]}