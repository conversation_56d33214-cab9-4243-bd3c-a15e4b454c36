{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDir\\form-dialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDir\\form-dialog.vue", "mtime": 1750232160339}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["form-dialog.vue"], "names": [], "mappings": ";AA4EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "form-dialog.vue", "sourceRoot": "src/views/SOP/sopDir", "sourcesContent": ["<template>\n  <div class=\"sop-dir-form\">\n    <el-dialog\n      :title=\"dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')\"\n      :visible.sync=\"dialogVisible\"\n      width=\"700px\"\n      :modal=\"true\"\n      :append-to-body=\"true\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      @close=\"handleClose\"\n    >\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" :rules=\"rules\" label-width=\"100px\">\n        <div class=\"form-body\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"父级目录\" prop=\"parentId\">\n                <span class=\"parent-dir\" :title=\"parentDir\">{{ parentDir || '未选择父级目录' }}</span>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item :label=\"$t('SOP.DirName')\" prop=\"dirName\">\n                <el-input v-model=\"dialogForm.dirName\" :placeholder=\"$t('SOP.EnterDirName')\"></el-input>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item :label=\"$t('SOP.DirCode')\" prop=\"dirCode\">\n                <el-input v-model=\"dialogForm.dirCode\" :placeholder=\"$t('SOP.EnterDirCode')\"></el-input>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"负责人\" prop=\"ownerUserid\">\n                <div class=\"user-selector\">\n                  <span class=\"user-text\" :title=\"selectedUser\">{{ selectedUser || '未选择负责人' }}</span>\n                  <el-button \n                    type=\"primary\" \n                    size=\"small\" \n                    @click=\"showUserDialog = true\"\n                    class=\"select-btn\"\n                    icon=\"el-icon-user\">\n                    选择\n                  </el-button>\n                </div>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button-group>\n          <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n          <el-button \n            type=\"primary\" \n            size=\"small\"\n            v-loading=\"formLoading\" \n            :disabled=\"formLoading\" \n            element-loading-spinner=\"el-icon-loading\"\n            @click=\"submit()\">\n            确 定\n          </el-button>\n        </el-button-group>\n      </div>\n    </el-dialog>\n    <select-user \n      :visible.sync=\"showUserDialog\" \n      @select-user=\"handleSelectUser\"\n      v-if=\"dialogVisible\"\n    />\n\n  </div>\n</template>\n  \n<script>\nimport {\n  getSopDirDetail,\n  saveSopDirForm,\n  getSopDirTree\n} from \"@/api/SOP/sopDir\";\nimport Treeselect from '@riophae/vue-treeselect'\nimport '@riophae/vue-treeselect/dist/vue-treeselect.css'\nimport SelectUser from './components/selectUser.vue'\n\nexport default {\n  name: 'SopDirFormDialog',\n  components: {\n    SelectUser\n  },\n  data() {\n    return {\n      dialogForm: {\n        parentId: 0,\n        dirName: '',\n        dirCode: '',\n        ownerUserid: ''\n      },\n      rules: {\n        dirName: [\n          { required: true, message: this.$t('SOP.DirNameRequired'), trigger: 'blur' },\n          { min: 2, max: 50, message: this.$t('SOP.DirNameLength'), trigger: 'blur' }\n        ],\n        dirCode: [\n          { required: true, message: this.$t('SOP.DirCodeRequired'), trigger: 'blur' },\n          { pattern: /^[A-Za-z0-9-_]+$/, message: this.$t('SOP.DirCodeFormat'), trigger: 'blur' }\n        ],\n        ownerUserid: [\n          { required: true, message: this.$t('SOP.DirOwnerRequired'), trigger: 'change' }\n        ]\n      },\n      dialogVisible: false,\n      formLoading: false,\n      currentRow: {},\n      isRootDir: false, // 是否新增根目录\n      dataList: [], // 目录树数据\n      showUserDialog: false,\n      selectedUser: '',\n      parentDir: '',\n      normalizer: node => ({\n        id: node.ID,\n        label: node.DirName,\n        children: node.Children\n      })\n    }\n  },\n  methods: {\n    async submit() {\n      try {\n        await this.$refs.dialogForm.validate()\n        this.formLoading = true\n        const res = await saveSopDirForm(this.dialogForm)\n        if (res.success) {\n          this.$message.success(res.msg || '保存成功')\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        } else {\n          this.$message.error(res.msg || '保存失败')\n        }\n      } catch (err) {\n        console.error('保存失败:', err)\n        this.$message.error('保存失败')\n      } finally {\n        this.formLoading = false\n      }\n    },\n    async loadTreeData() {\n      try {\n        const res = await getSopDirTree()\n        if (res.success) {\n          this.dataList = res.response || []\n        } else {\n          this.$message.error(res.msg || '获取目录树失败')\n        }\n      } catch (err) {\n        console.error('获取目录树失败:', err)\n        this.$message.error('获取目录树失败')\n      }\n    },\n    async show(data, type) {\n      if(type === 'add'){\n        // 判断是否为新增根目录\n        this.isRootDir = !data.id && !data.parentId && !data.parentId != '0'\n\n        this.dialogForm = {\n          parentId: this.isRootDir ? 0 : data.id, // 根目录parentId为0，否则为选中节点的id\n          dirName: '',\n          dirCode: '',\n          ownerUserid: ''\n        }\n        this.selectedUser = ''\n        this.parentDir = this.isRootDir ? '' : data.value + '-' + data.name\n        this.currentRow = data\n      }\n\n      if(type === 'show'){\n        this.dialogForm = {\n          id: data.id,\n          parentId: this.isRootDir ? 0 : data.parentId, // 根目录parentId为0，否则为选中节点的id\n          dirName: data.name,\n          dirCode: data.value,\n          ownerUserid: data.remark\n        }\n        this.selectedUser = data.extendField\n      }      \n      this.dialogVisible = true      \n    },\n    async getDialogDetail(id) {\n      try {\n        const res = await getSopDirDetail(id)\n        if (res.success) {\n          this.dialogForm = res.response || {}\n          // 如果有负责人信息，设置显示名称\n          if (this.dialogForm.ownerUserid && this.dialogForm.ownerUserName) {\n            this.selectedUser = this.dialogForm.ownerUserName\n          }\n        } else {\n          this.$message.error(res.msg || '获取详情失败')\n        }\n      } catch (err) {\n        console.error('获取详情失败:', err)\n        this.$message.error('获取详情失败')\n      }\n    },\n    handleClose() {\n      this.dialogVisible = false\n      // 添加表单重置逻辑\n      this.$nextTick(() => {\n        this.$refs.dialogForm.clearValidate()\n      })\n    },\n    \n    // 处理用户选择\n    handleSelectUser(user) {\n      try {\n        console.log('Selected user:', user) // 调试日志\n        \n        if (!user || !user.ID) {\n          throw new Error('无效的用户选择')\n        }\n\n        this.dialogForm.ownerUserid = user.LoginName\n        this.selectedUser = `${user.DepartmentName || ''} - ${user.LoginName || ''} - ${user.UserName || ''}`.trim()\n        \n        // 关闭对话框并验证表单\n        this.$nextTick(() => {\n          this.showUserDialog = false\n          this.$refs.dialogForm.validateField('ownerUserid')\n        })\n        \n      } catch (error) {\n        console.error('处理用户选择失败:', error)\n        this.$message.error('选择负责人失败: ' + (error.message || '未知错误'))\n        this.showUserDialog = false\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.sop-dir-form {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n\n    .el-dialog__header {\n      padding: 15px 20px;\n      border-bottom: 1px solid #ebeef5;\n      margin: 0;\n    }\n    \n    .el-dialog__body {\n      padding: 20px;\n    }\n\n    .el-dialog__footer {\n      padding: 10px 20px;\n      border-top: 1px solid #ebeef5;\n      background-color: #f9fafb;\n    }\n  }\n\n  .form-body {\n    padding: 10px 0;\n\n    .el-form-item {\n      margin-bottom: 18px;\n    }\n  }\n\n  .parent-dir {\n    display: block;\n    padding: 5px 12px;\n    line-height: 1.5;\n    border: 1px solid #dcdfe6;\n    border-radius: 4px;\n    background-color: #f5f7fa;\n    color: #606266;\n  }\n\n  .user-selector {\n    display: flex;\n    align-items: center;\n    gap: 10px;\n    \n    .user-text {\n      flex: 1;\n      padding: 5px 12px;\n      border: 1px solid #dcdfe6;\n      border-radius: 4px;\n      background-color: #f5f7fa;\n      color: #606266;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n    \n    .select-btn {\n      flex-shrink: 0;\n      width: 80px;\n      padding: 7px 12px;\n    }\n  }\n}\n</style>\n"]}]}