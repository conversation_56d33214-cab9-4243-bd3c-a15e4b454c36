{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750296545653}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAuJA;AAEA;AACA;AACA;AAEA;EACAA,iBADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACA;MACAC,kBAFA;MAGA;MACAC,YAJA;MAKAC,kBALA;MAMAC,iBANA;MAOAC;QACAC,oBADA;QAEAC;MAFA,CAPA;MAWA;MACAC;QACAC,kBADA;QAEAC,kBAFA;QAGAC,mBAHA;QAIAC,qBAJA;QAKAC,oBALA;QAMAC,kBANA;QAOAC;MAPA,CAZA;MAqBAC,QArBA;MAsBAC,aAtBA;MAuBAC,mBAvBA;MAwBAC;IAxBA;EA0BA,CAhCA;;EAiCA;IACA;MACA;MACA,wBAFA,CAGA;;MACA,+CAJA,CAIA;;MACA;MACA;QAAA;;QACA,6BACA,2KADA,EAEA,yKAFA;MAIA,CALA;;MAMAC;QAAA;;QACA,6BACA,8KADA,EAEA,yKAFA;MAIA,CALA;IAMA,CAlBA,CAkBA;MACAC;MACA;IACA,CArBA,SAqBA;MACA;IACA;EACA,CA1DA;;EA2DAC;IACA;IACA;MACA;;MACA;QACA;;QACA;UACA;QACA,CAFA,MAEA;UACA;QACA;MACA,CAPA,CAOA;QACAD;QACA;QACA;MACA,CAXA,SAWA;QACA;MACA;IACA,CAlBA;;IAoBA;IACAE;MACA,wBADA,CAEA;;MACA;QAAAd;QAAAC;MAAA;MACA;QACAD,OADA;QAEAC,OAFA;QAGAC,iBAHA;QAIAC,aAJA;QAIA;QACAC,oBALA;QAMAC,kBANA;QAMA;QACAC;MAPA;MASA;IACA,CAnCA;;IAqCA;IACAS;MACA;;MACAC;QACA;MACA,CAFA;IAGA,CA3CA;;IA6CA;IACAC;MACA;;MACAD;QACA;MACA,CAFA;IAGA,CAnDA;;IAqDA;IACAE;MACAC;;MACA;QACAA;;QACA;UACA;QACA;MACA;IACA,CA9DA;;IAgEA;IACA;MACAP;MACA;MACA;IACA,CArEA;;IAuEA;IACA;MACA;MACA,uCAFA,CAEA;;MACA,wCAHA,CAGA;;MACA,sCAJA,CAIA;;MACA,sCALA,CAKA;;MACA,sCANA,CAMA;;MAEAQ;MACAR,2DATA,CAWA;;MACA;IACA,CArFA;;IAuFA;IACA;MACA;QAAA;;QACA;UACAS,UADA;UAEAC,kDAFA;UAGAC,6DAHA;UAIAC,0DAJA;UAKAC,+CALA;UAMAC,sBANA;UAOAC,wBAPA;UAQAC,0BARA;UASAC,8BATA;UAUAC,oCAVA;UAWAC;QAXA;QAcAnB;QACA;;QAEA;UACA,gDADA,CAEA;;UACA;QACA,CAJA,MAIA;UACA;QACA;MACA,CAzBA,CAyBA;QACAA;QACA;MACA;IACA,CAtHA;;IAwHA;IACA;MACAA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CAjIA;;IAmIA;IACA;MACAA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CA5IA;;IA8IA;IACAoB;MACA;MACA;MACA;MACA;MACA;MACA,gCANA,CAQA;;MACA;QACA;MACA,CAXA,CAaA;;;MACA,sGAdA,CAgBA;;MACAC;QACA;UACA;YACA,gCADA,CACA;;YACA;;UACA;YACA,iCADA,CACA;;YACA;;UACA;YACA,+BADA,CACA;;YACA;;UACA;YACA,+BADA,CACA;;YACA;;UACA;YACA,+BADA,CACA;;YACA;QAfA;MAiBA,CAlBA;IAmBA,CAnLA;;IAoLAC;MACA;IACA,CAtLA;;IAwLA;IACAC;MACA;QACAnC,kBADA;QAEAC,kBAFA;QAGAC,kCAHA;QAGA;QACAC,sCAJA;QAKAC,oBALA;QAMAC,gCANA;QAMA;QACAC;MAPA;MASA;IACA,CApMA;;IAsMA;IACA8B;MACA;QACA;MACA,CAFA,EAEAC,MAFA;IAGA,CA3MA;;IA6MA;IACAC;MACA;QACA;MACA;;MACA;IACA,CAnNA;;IAuNA;MACA;;MACA;QACA;QACA1B,6BAFA,CAIA;;QACA;;QAEA;UACA;YACA;YACArB;YACA;UACA,CAJA,MAIA;YACA;YACAA;YACA;UACA,CAJA,MAIA;YACA;YACAA;YACA;UACA,CAJA,MAIA;YACA;YACAA;YACA;UACA;;UAEA;UACAqB,yCApBA,CAsBA;;UACA;YACA;UACA,CAFA;QAGA,CA1BA,MA0BA;UACAA;UACA;UACA;UACA;QACA;MACA,CAvCA,CAuCA;QACAA;QACA;QACA;QACA;MACA,CA5CA,SA4CA;QACA;MACA;IACA;;EAxQA;AA3DA", "names": ["name", "components", "SplitPane", "data", "initLoading", "treeData", "treeLoading", "selectedDir", "defaultProps", "children", "label", "searchForm", "<PERSON><PERSON><PERSON>", "dirCode", "targetId", "targetType", "grantType", "grantId", "permLevel", "total", "tableData", "tableLoading", "mainH", "window", "console", "methods", "handleNodeClick", "expandAll", "nodes", "collapseAll", "expandNodes", "node", "row", "ID", "TargetId", "TargetType", "GrantType", "GrantId", "RoleName", "PermLevel", "CreateDate", "CreateUserId", "ModifyDate", "ModifyUserId", "parsePermLevel", "permissions", "getSearchBtn", "resetForm", "getConfiguredCount", "length", "getCurrentDirName"], "sourceRoot": "src/views/SOP/sopPermission", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"root usemystyle\">\n        <div class=\"root-layout\" v-loading=\"initLoading\">\n            <split-pane\n                :min-percent=\"15\"\n                :max-percent=\"40\"\n                :default-percent=\"20\"\n                split=\"vertical\">\n                <template slot=\"pane1\">\n                    <div class=\"root-left\">\n                        <div class=\"tree-toolbar\">\n                            <el-button-group>\n                                <el-button\n                                    size=\"small\"\n                                    icon=\"el-icon-refresh\"\n                                    @click=\"getDirTree\">刷新</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"expandAll\">展开</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"collapseAll\">收起</el-button>\n                            </el-button-group>\n                        </div>\n                        <el-tree\n                            ref=\"tree\"\n                            :data=\"treeData\"\n                            :props=\"defaultProps\"\n                            highlight-current\n                            @node-click=\"handleNodeClick\"\n                            v-loading=\"treeLoading\">\n                            <span class=\"custom-tree-node\" slot-scope=\"{ node }\">\n                                <div style=\"line-height: 22px;\">\n                                    <div class=\"tree-title\">{{ node.data.name }}</div>\n                                </div>\n                            </span>\n                        </el-tree>\n                    </div>\n                </template>\n                <template slot=\"pane2\">\n                    <div class=\"root-right\">\n                        <div class=\"InventorySearchBox\">\n                            <div class=\"search-form\">\n                                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                                    <div class=\"form-content\">\n                                        <div class=\"search-area\">\n                                            <div class=\"search-row\">\n                                                <el-form-item label=\"目录名称\" prop=\"dirName\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirName\" placeholder=\"输入目录名称\" clearable size=\"small\" style=\"width: 200px;\"></el-input>\n                                                </el-form-item>\n                                                <el-form-item label=\"目录编码\" prop=\"dirCode\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirCode\" placeholder=\"输入目录编码\" clearable size=\"small\" style=\"width: 150px;\"></el-input>\n                                                </el-form-item>\n                                                <div class=\"action-buttons\">\n                                                    <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn\" size=\"small\">查询</el-button>\n                                                    <el-button icon=\"el-icon-refresh\" @click=\"resetForm\" size=\"small\">重置</el-button>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </el-form>\n                            </div>\n                        </div>\n                        <div class=\"root-main\">\n                            <!-- 数据统计信息 -->\n                            <div class=\"data-summary\" v-if=\"tableData.length > 0\">\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">当前目录：</span>\n                                    <span class=\"summary-value\">{{ getCurrentDirName() }}</span>\n                                </div>\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">总计角色：</span>\n                                    <span class=\"summary-value\">{{ total }}</span>\n                                </div>\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">已配置权限：</span>\n                                    <span class=\"summary-value\">{{ getConfiguredCount() }}</span>\n                                </div>\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">未配置权限：</span>\n                                    <span class=\"summary-value\">{{ total - getConfiguredCount() }}</span>\n                                </div>\n                            </div>\n\n                            <el-table\n                                class=\"mt-3\"\n                                :height=\"mainH\"\n                                border\n                                :data=\"tableData\"\n                                style=\"width: 100%; border-radius: 4px;\"\n                                v-loading=\"tableLoading\"\n                                :empty-text=\"'暂无数据'\"\n                                ref=\"permissionTable\">\n\n                                <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\n\n                                <el-table-column prop=\"RoleName\" :label=\"$t('SopPermission.table.RoleName')\" min-width=\"150\" show-overflow-tooltip></el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Preview\" @input=\"handlePermissionChange(scope.row, 'Preview', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Download\" @input=\"handlePermissionChange(scope.row, 'Download', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Search\" @input=\"handlePermissionChange(scope.row, 'Search', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Upload\" @input=\"handlePermissionChange(scope.row, 'Upload', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Delete\" @input=\"handlePermissionChange(scope.row, 'Delete', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <div style=\"display: flex; justify-content: center; gap: 4px;\">\n                                            <el-button size=\"mini\" type=\"primary\" @click=\"selectAll(scope.row)\">\n                                                {{ $t('SopPermission.table.SelectAll') }}\n                                            </el-button>\n                                            <el-button size=\"mini\" type=\"info\" @click=\"cancelAll(scope.row)\">\n                                                {{ $t('SopPermission.table.CancelAll') }}\n                                            </el-button>\n                                        </div>\n                                    </template>\n                                </el-table-column>\n                            </el-table>\n                        </div>\n\n                    </div>\n                </template>\n            </split-pane>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SplitPane from '../components/split-pane';\nimport { getSopPermissionList, saveSopPermissionForm } from '@/api/SOP/sopPermission';\nimport { getSopDirTree } from '@/api/SOP/sopDir';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SplitPane\n    },\n    data() {\n        return {\n            // 初始化加载状态\n            initLoading: false,\n            // 树相关数据\n            treeData: [],\n            treeLoading: false,\n            selectedDir: null,\n            defaultProps: {\n                children: 'children',\n                label: 'name'\n            },\n            // 表格相关数据\n            searchForm: {\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            tableLoading: false,\n            mainH: 0\n        };\n    },\n    async mounted() {\n        try {\n            this.initLoading = true;\n            await this.getDirTree();\n            // 页面加载时默认请求根目录权限数据\n            this.searchForm.targetId = this.treeData[0].id; // 设置默认查询根目录\n            await this.getTableData();\n            this.$nextTick(() => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            });\n            window.onresize = () => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            };\n        } catch (err) {\n            console.error('页面初始化失败:', err);\n            this.$message.error('页面初始化失败，请刷新重试');\n        } finally {\n            this.initLoading = false;\n        }\n    },\n    methods: {\n        // 获取目录树\n        async getDirTree() {\n            this.treeLoading = true;\n            try {\n                const res = await getSopDirTree();\n                if (res.success) {\n                    this.treeData = res.response || [];\n                } else {\n                    this.$message.error(res.msg || '获取目录树失败');\n                }\n            } catch (err) {\n                console.error('获取目录树失败:', err);\n                this.$message.error('获取目录树失败');\n                throw err;\n            } finally {\n                this.treeLoading = false;\n            }\n        },\n\n        // 处理树节点点击\n        handleNodeClick(data) {\n            this.selectedDir = data;\n            // 保留查询条件，更新目标目录\n            const { dirName, dirCode } = this.searchForm;\n            this.searchForm = {\n                dirName,\n                dirCode,\n                targetId: data.id,\n                targetType: 1, // 1表示目录\n                grantType: undefined,\n                grantId: undefined, // 使用目录ID或编码作为grantId\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 展开所有节点\n        expandAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, true);\n            });\n        },\n\n        // 收起所有节点\n        collapseAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, false);\n            });\n        },\n\n        // 树节点展开关闭\n        expandNodes(node, type) {\n            node.expanded = type;\n            for (let i = 0; i < node.childNodes.length; i++) {\n                node.childNodes[i].expanded = type;\n                if (node.childNodes[i].childNodes.length > 0) {\n                    this.expandNodes(node.childNodes[i], type);\n                }\n            }\n        },\n\n        // 处理权限变更\n        async handlePermissionChange(row, permission, value) {\n            console.log('权限变更:', permission, value);\n            this.$set(row, permission, value);\n            await this.updatePermLevel(row);\n        },\n\n        // 权限级别更新方法\n        async updatePermLevel(row) {\n            let permissions = [];\n            if (row.Preview) permissions.push('1');    // 预览 (1)\n            if (row.Download) permissions.push('2');   // 下载 (2)\n            if (row.Search) permissions.push('3');     // 检索 (3)\n            if (row.Upload) permissions.push('4');     // 上传 (4)\n            if (row.Delete) permissions.push('8');     // 删除 (8)\n\n            row.PermLevel = permissions.join(',');\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n\n            // 调用API保存权限变更\n            await this.savePermission(row);\n        },\n\n        // 保存权限到后端\n        async savePermission(row) {\n            try {\n                const saveData = {\n                    ID: row.ID,\n                    TargetId: row.TargetId || this.searchForm.targetId,\n                    TargetType: row.TargetType || this.searchForm.targetType || 1,\n                    GrantType: row.GrantType || this.searchForm.grantType || 1,\n                    GrantId: row.GrantId || this.searchForm.grantId,\n                    RoleName: row.RoleName,\n                    PermLevel: row.PermLevel,\n                    CreateDate: row.CreateDate,\n                    CreateUserId: row.CreateUserId,\n                    ModifyDate: new Date().toISOString(),\n                    ModifyUserId: this.$store.state.user.userInfo?.userId || 'system'\n                };\n\n                console.log('保存权限数据:', saveData);\n                const res = await saveSopPermissionForm(saveData);\n\n                if (res.success) {\n                    this.$message.success(`${row.RoleName} 权限保存成功`);\n                    // 保存成功后刷新表格数据\n                    await this.getTableData();\n                } else {\n                    this.$message.error(res.msg || '权限保存失败');\n                }\n            } catch (err) {\n                console.error('保存权限失败:', err);\n                this.$message.error('权限保存失败，请重试');\n            }\n        },\n\n        // 全选权限\n        async selectAll(row) {\n            console.log('全选权限:', row.RoleName);\n            this.$set(row, 'Preview', true);\n            this.$set(row, 'Download', true);\n            this.$set(row, 'Search', true);\n            this.$set(row, 'Upload', true);\n            this.$set(row, 'Delete', true);\n            await this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        async cancelAll(row) {\n            console.log('取消全选:', row.RoleName);\n            this.$set(row, 'Preview', false);\n            this.$set(row, 'Download', false);\n            this.$set(row, 'Search', false);\n            this.$set(row, 'Upload', false);\n            this.$set(row, 'Delete', false);\n            await this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            // 使用$set确保响应式\n            this.$set(row, 'Preview', false);\n            this.$set(row, 'Download', false);\n            this.$set(row, 'Search', false);\n            this.$set(row, 'Upload', false);\n            this.$set(row, 'Delete', false);\n\n            // 处理 null、undefined 或空字符串的情况\n            if (!permLevel || permLevel === 'null' || permLevel === '') {\n                return;\n            }\n\n            // 将权限字符串按逗号分割成数组\n            const permissions = permLevel.toString().split(',').map(p => p.trim()).filter(p => p && p !== 'null');\n\n            // 根据权限数组设置对应的权限状态\n            permissions.forEach(perm => {\n                switch (perm) {\n                    case '1':\n                        this.$set(row, 'Preview', true);     // 预览\n                        break;\n                    case '2':\n                        this.$set(row, 'Download', true);    // 下载\n                        break;\n                    case '3':\n                        this.$set(row, 'Search', true);      // 检索\n                        break;\n                    case '4':\n                        this.$set(row, 'Upload', true);      // 上传\n                        break;\n                    case '8':\n                        this.$set(row, 'Delete', true);      // 删除\n                        break;\n                }\n            });\n        },\n        getSearchBtn() {\n            this.getTableData();\n        },\n\n        // 重置查询表单\n        resetForm() {\n            this.searchForm = {\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: this.searchForm.targetId, // 保留当前选中的目录\n                targetType: this.searchForm.targetType,\n                grantType: undefined,\n                grantId: this.searchForm.grantId, // 保留当前的grantId\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 获取已配置权限的角色数量\n        getConfiguredCount() {\n            return this.tableData.filter(row => {\n                return row.Preview || row.Download || row.Search || row.Upload || row.Delete;\n            }).length;\n        },\n\n        // 获取当前目录名称\n        getCurrentDirName() {\n            if (this.selectedDir) {\n                return this.selectedDir.name || this.selectedDir.dirName || '未知目录';\n            }\n            return '根目录';\n        },\n\n\n\n        async getTableData() {\n            this.tableLoading = true;\n            try {\n                const res = await getSopPermissionList(this.searchForm);\n                console.log('API返回数据:', res);\n\n                // 处理后端返回的数据结构\n                let data = [];\n\n                if (res && res.success !== false) {\n                    if (res.response && Array.isArray(res.response.data)) {\n                        // 如果数据在 response.data 中\n                        data = res.response.data;\n                        this.total = res.response.dataCount || data.length;\n                    } else if (res.response && Array.isArray(res.response)) {\n                        // 如果数据直接在 response 中\n                        data = res.response;\n                        this.total = data.length;\n                    } else if (Array.isArray(res)) {\n                        // 如果数据直接是数组\n                        data = res;\n                        this.total = data.length;\n                    } else if (res.success && res.response) {\n                        // 处理其他可能的数据结构\n                        data = Array.isArray(res.response) ? res.response : [];\n                        this.total = data.length;\n                    }\n\n                    this.tableData = data;\n                    console.log('处理后的表格数据:', this.tableData);\n\n                    // 解析每行的权限级别\n                    this.tableData.forEach(row => {\n                        this.parsePermLevel(row.PermLevel, row);\n                    });\n                } else {\n                    console.log('API调用失败或返回错误:', res);\n                    this.$message.error(res?.msg || '获取权限数据失败');\n                    this.tableData = [];\n                    this.total = 0;\n                }\n            } catch (err) {\n                console.error('获取权限数据失败:', err);\n                this.$message.error('获取权限数据失败，请检查网络连接');\n                this.tableData = [];\n                this.total = 0;\n            } finally {\n                this.tableLoading = false;\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .InventorySearchBox {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .search-form {\n      :deep(.el-form) {\n        .el-form-item--small.el-form-item {\n          margin-bottom: 0;\n        }\n      }\n\n      .form-content {\n        padding: 4px;\n\n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .el-form-item {\n              margin: 0;\n              flex: none;\n\n              .el-form-item__label {\n                padding-right: 4px;\n                line-height: 26px;\n                font-size: 12px;\n              }\n\n              .el-form-item__content {\n                line-height: 26px;\n\n                .el-input,\n                .el-select {\n                  :deep(.el-input__inner) {\n                    height: 26px;\n                    line-height: 26px;\n                    padding: 0 8px;\n                    font-size: 12px;\n                  }\n\n                  :deep(.el-input-group__append) {\n                    padding: 0;\n                    .el-button {\n                      padding: 0 10px;\n                      height: 26px;\n                      border: none;\n                    }\n                  }\n                }\n              }\n            }\n\n            .action-buttons {\n              display: flex;\n              gap: 4px;\n              margin-left: 8px;\n\n              .el-button {\n                height: 26px;\n                padding: 0 10px;\n                font-size: 12px;\n\n                [class^=\"el-icon-\"] {\n                  margin-right: 3px;\n                  font-size: 12px;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-head {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .selected-dir-info {\n      padding: 4px;\n\n      span {\n        font-size: 14px;\n        color: #606266;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .data-summary {\n      background: #fff;\n      border-radius: 4px;\n      padding: 12px;\n      margin-bottom: 12px;\n      display: flex;\n      justify-content: space-between;\n      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n      border: 1px solid #ebeef5;\n\n      .summary-item {\n        text-align: center;\n        flex: 1;\n\n        &:first-child {\n          .summary-value {\n            color: #409eff;\n          }\n        }\n\n        .summary-label {\n          display: block;\n          font-size: 12px;\n          color: #606266;\n          margin-bottom: 4px;\n        }\n\n        .summary-value {\n          display: block;\n          font-size: 16px;\n          font-weight: bold;\n          color: #303133;\n        }\n      }\n    }\n\n    .el-table {\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n\n  .root-footer {\n    margin-top: 12px;\n    padding: 8px;\n    background: #fff;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  }\n}\n</style>"]}]}