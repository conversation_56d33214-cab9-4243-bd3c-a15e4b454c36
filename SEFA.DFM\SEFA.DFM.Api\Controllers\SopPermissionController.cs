﻿using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [AllowAnonymous]
    public class SopPermissionController : BaseApiController
    {
        /// <summary>
        /// SopPermission
        /// </summary>
        private readonly ISopPermissionServices _sopPermissionServices;
    
        public SopPermissionController(ISopPermissionServices SopPermissionServices)
        {
            _sopPermissionServices = SopPermissionServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<SopPermissionEntity>>> GetList([FromBody] SopPermissionRequestModel reqModel)
        {
            var data = await _sopPermissionServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<SopPermissionEntity>>> GetPageList([FromBody] SopPermissionRequestModel reqModel)
        {
            var data = await _sopPermissionServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<SopPermissionEntity>> GetEntity(string id)
        {
            var data = await _sopPermissionServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] SopPermissionEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                request.TargetType = 1;
                request.GrantType = 1;
                request.Deleted = 0;
                data.success = await _sopPermissionServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                request.TargetType = 1;
                request.GrantType = 1;
                request.Deleted = 0;
                data.success = await _sopPermissionServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] SopPermissionEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _sopPermissionServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _sopPermissionServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class SopPermissionRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}