{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\FullBag.vue?vue&type=style&index=0&id=53f31a7f&lang=scss&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\FullBag.vue", "mtime": 1750150388566}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouUGFydGlhbEJhZyB7DQogICAgcGFkZGluZzogMTBweDsNCiAgICBtYXJnaW46IDEwcHg7DQogICAgaGVpZ2h0OiA4OHB4Ow0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGJvcmRlcjogMXB4IHNvbGlkICNlYmVlZjU7DQogICAgLnRhYmlucHV0Ym94IHsNCiAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICB9DQogICAgLnN0YXR1c2JveCB7DQogICAgICAgIHBhZGRpbmc6IDAgMTBweDsNCiAgICAgICAgaGVpZ2h0OiAzMHB4Ow0KICAgICAgICBiYWNrZ3JvdW5kOiAjZmZhNTAwOw0KICAgIH0NCiAgICAudGFiaW5wdXRzaW5nbGVib3ggew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgICAgbWF4LXdpZHRoOiAyNTBweDsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KDQogICAgICAgIC50YWJpbnB1dHNpbmdsZWxhYmVsIHsNCiAgICAgICAgICAgIHdpZHRoOiAxMDBweDsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgfQ0KICAgICAgICAudGFiYnRuc2luZ2xlYm94IHsNCiAgICAgICAgICAgIGhlaWdodDogMzBweDsNCiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDRweDsNCiAgICAgICAgfQ0KICAgIH0NCn0NCg=="}, {"version": 3, "sources": ["FullBag.vue"], "names": [], "mappings": ";AA+IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "FullBag.vue", "sourceRoot": "src/views/Inventory/buildpalletsStart/components", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle PartialBag\">\r\n        <div class=\"tabinputbox\">\r\n            <div class=\"tabinputsinglebox\">\r\n                <el-input size=\"mini\" @change=\"getRowBySSCC\" ref=\"autoFocus\" :placeholder=\"$t('Consume.SSCC')\" v-model=\"sscc\">\r\n                    <template slot=\"append\"><i class=\"el-icon-full-screen\"></i></template>\r\n                </el-input>\r\n            </div>\r\n            <div class=\"tabinputsinglebox\" style=\"flex-direction: row; align-items: center\">\r\n                <div class=\"tabinputsinglelabel\">{{ $t('MaterialPreparationBuild.Bags') }}:</div>\r\n                <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" type=\"number\" v-model=\"Bags\"></el-input>\r\n                <!-- <div class=\"tabbtnsinglebox\" style=\"flex-direction: row; align-items: center; width: 220px\">\r\n                    <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-top\"></el-button>\r\n                    <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-bottom\"></el-button>\r\n                </div> -->\r\n            </div>\r\n            <div class=\"tabinputsinglebox\" style=\"flex-direction: row; align-items: center\">\r\n                <div class=\"tabinputsinglelabel\">{{ $t('MaterialPreparationBuild.BagWeight') }}:</div>\r\n                <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" type=\"number\" v-model=\"BagWeight\"></el-input>\r\n                <!-- <div class=\"tabbtnsinglebox\" style=\"flex-direction: row; align-items: center; width: 250px\">\r\n                    <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-top\"></el-button>\r\n                    <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-bottom\"></el-button>\r\n                </div> -->\r\n            </div>\r\n            <div class=\"tabinputsinglebox\">\r\n                <div class=\"tabbtnsinglebox\">\r\n                    <el-button\r\n                        ref=\"FullBagbtn\"\r\n                        style=\"margin-left: 5px\"\r\n                        :disabled=\"!(ssccFlag && sscc != '' && detailobj.CompleteStates != 'OK' && Bags != 0)\"\r\n                        size=\"small\"\r\n                        icon=\"el-icon-bottom\"\r\n                        class=\"tablebtn\"\r\n                        @click=\"Transfer()\"\r\n                    >\r\n                        {{ this.$t('MaterialPreparationBuild.Transfer') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { TransferFullBag } from '@/api/Inventory/MaterialPreparation.js';\r\nimport { Message, MessageBox } from 'element-ui';\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            sscc: '',\r\n            Bags: 0,\r\n            ssccFlag: false,\r\n            BagWeight: '',\r\n            detailobj: {},\r\n            SubId: '',\r\n            InQuantity: 0\r\n        };\r\n    },\r\n    mounted() {\r\n        this.detailobj = JSON.parse(this.$route.query.query);\r\n        this.BagWeight = this.detailobj.BagSize;\r\n        this.Bags = this.detailobj.BagS;\r\n        this.getbtnStatus();\r\n    },\r\n    methods: {\r\n        getbtnStatus() {\r\n            return this.ssccFlag && this.sscc != '' && this.detailobj.CompleteStates != 'OK' && this.Bags != 0;\r\n        },\r\n        getRowBySSCC() {\r\n            this.$emit('getRowBySscc', this.sscc);\r\n        },\r\n        getSSCC() {\r\n            this.$emit('getRowSSCC', this.sscc);\r\n        },\r\n        async Transfer() {\r\n            let num = Number(this.Bags) * Number(this.BagWeight);\r\n            if (this.$parent.$parent.$parent.isExpirationDate) {\r\n                Message({\r\n                    message: this.$t('MaterialPreparationBuild.OverExpirationDate'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            this.detailobj = this.$parent.$parent.$parent.detailobj;\r\n            let SelectList = this.$parent.$parent.$parent.SelectList;\r\n            if (SelectList == null || SelectList.length == 0) {\r\n                Message({\r\n                    message: this.$t('MaterialPreparationBuild.BatchPalletsEmpty'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            if (num.toFixed() > this.InQuantity) {\r\n                Message({\r\n                    message: this.$t('MaterialPreparationBuild.InQuantityNotEnough'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            if (num + Number(this.detailobj.MQuantity) > this.detailobj.MaxPvalue) {\r\n                Message({\r\n                    message: this.$t('MaterialPreparationBuild.QtyOverMax'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let data = {\r\n                ChangeUnit:this.detailobj.ChangeUnit?\"\":this.detailobj.ChangeUnit,//增加单位转换\r\n                PrintId: window.sessionStorage.getItem('PrintId'),\r\n                subID: this.SubId,\r\n                equpmentID: window.sessionStorage.getItem('room'),\r\n                bags: Number(this.Bags),\r\n                actualValue: this.InQuantity,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                bagWeight: this.BagWeight,\r\n                targetWeight: this.detailobj.MQuantityTotal,\r\n                actualWeight: this.detailobj.MQuantity == null ? 0 : Number(this.detailobj.MQuantity),\r\n                containerID: window.sessionStorage.getItem('BatchPallets'),\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId,\r\n                batchConsumeRequirementId: this.detailobj.BatchConsumeRequirementId,\r\n            };\r\n            if (window.sessionStorage.getItem('MaterialPreparation') != 'clbl') {\r\n                data.actualWeight = this.detailobj.MQuantity == null ? 0 : Number(this.detailobj.MQuantity);\r\n            } else {\r\n                data.actualWeight = this.detailobj.MQuantity == null ? 0 : Number(this.detailobj.MQuantity);\r\n            }\r\n            let res = await TransferFullBag(data);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$nextTick(_ => {\r\n                this.sscc = '';\r\n                this.$refs.autoFocus.focus();\r\n            });\r\n            this.$emit('getRefresh');\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\">\r\n.PartialBag {\r\n    padding: 10px;\r\n    margin: 10px;\r\n    height: 88px;\r\n    width: 100%;\r\n    border: 1px solid #ebeef5;\r\n    .tabinputbox {\r\n        height: 100%;\r\n        width: 100%;\r\n        display: flex;\r\n    }\r\n    .statusbox {\r\n        padding: 0 10px;\r\n        height: 30px;\r\n        background: #ffa500;\r\n    }\r\n    .tabinputsinglebox {\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        max-width: 250px;\r\n        margin-right: 10px;\r\n\r\n        .tabinputsinglelabel {\r\n            width: 100px;\r\n            font-size: 12px;\r\n        }\r\n        .tabbtnsinglebox {\r\n            height: 30px;\r\n            margin-bottom: 4px;\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}