<template>
  <el-dialog :title="dialogForm.ID ? '编辑' : '新增'" :visible.sync="dialogVisible" width="600px"
             :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
             @close="dialogVisible = false">
    <el-form ref="dialogForm" :model="dialogForm" label-width="80px">
      <el-form-item label="工段" prop="ParentId">
        <el-select transfer="true" :popper-append-to-body="false" style="width: 100%" v-model="dialogForm.ParentId" @change="selectChange" placeholder="请选择">
          <el-option v-for="(item) in SapSegments" :key="item.id" :label="item.SegmentName" :value="item.ID">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="编码" prop="SegmentCode">
        <el-input v-model="dialogForm.SegmentCode" :maxlength="20" placeholder="" />
      </el-form-item>
      <el-form-item label="名称" prop="SegmentName">
        <el-input v-model="dialogForm.SegmentName" :maxlength="20" placeholder="" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogVisible = false">取 消</el-button>
      <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small"
                 @click="submit()">确定
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { savePhase, getSapSegmentList } from '@/api/productionManagement/Formula';
import {getLabelFormatList} from "@/api/systemManagement/labelPrint";
export default {
  name: 'add',
  data() {
    return {
      dialogForm: {},
      dialogVisible: false,
      formLoading: false,
      sapEquipmentId: 0,
      SapSegments: [],
      rules: {
        ParentId: [
          { required: true, message: '请选择工段', trigger: 'change' }
        ],
        SegmentCode: [
          { required: true, message: '请输入编码', trigger: 'blur' },
        ],
        SegmentName: [
          { required: true, message: '请输入名称', trigger: 'blur' },
        ]
      }
    }
  },
  mounted() {
  },
  methods: {
    submit() {
      this.dialogForm.SapEquipmentId = this.sapEquipmentId
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          savePhase(this.dialogForm).then(res => {
            this.$message.success(res.msg)
            this.$emit('saveForm')
            this.dialogVisible = false
          })
        }
      });
    },
    show(data) {
      this.dialogForm = {}
      this.$set(this.dialogForm, 'ID', data.ID)
      this.sapEquipmentId = data.SapEquipmentId
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.getOperationData(data)
        if(data.ID){
         
          this.$set(this.dialogForm, 'ParentId', data.ParentId)
          this.$set(this.dialogForm, 'SegmentCode', data.SegmentCode)
          this.$set(this.dialogForm, 'SegmentName', data.SegmentName)
        }
      })
    },
    getOperationData(data) {
      this.SapSegments = []
      getSapSegmentList({Level: 1,SapEquipmentId: this.sapEquipmentId}).then(res => {
        this.SapSegments.push(...res.response)
        // this.dialogForm = Object.assign({},data)
      })
      // const { response } = await getSapSegmentList({
      //   Level: 1,
      //   SapEquipmentId: this.sapEquipmentId,
      // })
      // this.SapSegments = response
    },
    selectChange(){
      this.$forceUpdate()
    }
  }
}
</script>
