{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\components\\Tipping.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\components\\Tipping.vue", "mtime": 1750254216284}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Tipping.vue"], "names": [], "mappings": ";AAiIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Tipping.vue", "sourceRoot": "src/views/Producting/Overview/components", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle Tipping precheck\">\r\n        <div class=\"InventorySearchBox\">\r\n            <!-- <div class=\"searchbox\">\r\n                <div :class=\"'searchtipbox status' + TippingStatus\">{{ this.$t('POListTipping.Tipping') }}：{{ filiterTippingStatus(TippingStatus) }}</div>\r\n            </div> -->\r\n            <div class=\"searchbox\">\r\n                <!--  -->\r\n                <el-button @click=\"startTip()\" class=\"tablebtn\" :disabled=\"PrepStatus == '0' ? false : true\" style=\"margin-left: 5px; width: 120px\" size=\"small\" icon=\"el-icon-caret-right\">\r\n                    {{ this.$t('POListTipping.StartTipping') }}\r\n                </el-button>\r\n                <!--  -->\r\n                <el-button @click=\"startScan()\" class=\"tablebtn\" :disabled=\"PrepStatus == '1' ? false : true\" style=\"margin-left: 5px; width: 120px\" size=\"small\" icon=\"el-icon-full-screen\">\r\n                    {{ this.$t('POListTipping.Scan') }}\r\n                </el-button>\r\n                <el-button @click=\"TippingOver()\" class=\"tablebtn\" :disabled=\"PrepStatus == '2' ? false : true\" style=\"margin-left: 5px; width: 120px\" size=\"small\">\r\n                    {{ this.$t('POListTipping.TippingOver') }}\r\n                </el-button>\r\n                <el-button @click=\"TippingOver(true)\" class=\"tablebtn\" :disabled=\"PrepStatus == '1' ? false : true\" style=\"margin-left: 5px; width: 120px\" size=\"small\">\r\n                    {{ this.$t('POListTipping.ForcedCompletion') }}\r\n                </el-button>\r\n                <div class=\"searchtipbox\" v-if=\"PrepStatus == '0'\" style=\"background: #e1f5f6; color: #426777; font-weight: 600\">{{ this.$t('POListTipping.TippingText') }}</div>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <div class=\"searchboxTitle\" style=\"font-size: 16px\" v-if=\"Content != ''\">DCS当前投料信号：{{ Content }}</div>\r\n                <div class=\"searchboxTitle\" style=\"font-size: 16px\" v-if=\"ContentValue != ''\">允许投料属性值：{{ ContentValue }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"tablebox\">\r\n            <el-table :data=\"tableList\" style=\"width: 100%\" height=\"560\">\r\n                <el-table-column\r\n                    v-for=\"(item, index) in header\"\r\n                    :fixed=\"item.fixed ? item.fixed : false\"\r\n                    :key=\"index\"\r\n                    :align=\"item.align\"\r\n                    :prop=\"item.prop ? item.prop : item.value\"\r\n                    :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                    :width=\"item.width\"\r\n                >\r\n                    <template v-slot:header=\"scope\">\r\n                        <span v-if=\"item.icon\">\r\n                            <i :class=\"item.icon\"></i>\r\n                        </span>\r\n                        <span v-if=\"!item.icon\">{{ scope.column.label }}</span>\r\n                    </template>\r\n                    <template slot-scope=\"scope\">\r\n                        <i class=\"el-icon-document\" v-if=\"scope.column.property == 'detail'\" @click=\"opendetailmodel(scope.row)\"></i>\r\n                        <span v-if=\"scope.column.property != 'detail'\">\r\n                            <span v-if=\"scope.column.property == 'Quantity'\">{{ scope.row.Quantity }}{{ scope.row.Unit1 }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity2'\">{{ scope.row.Quantity2 }}{{ scope.row.Unit1 }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'PrepStatus'\">\r\n                                <span v-if=\"Number(scope.row.PrepStatus) >= 7\">\r\n                                    <i class=\"el-icon-check\"></i>\r\n                                </span>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity4'\">{{ scope.row.Quantity4 }}{{ scope.row.Unit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n            <div class=\"paginationbox\">\r\n                <el-pagination\r\n                    @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageOptions.page\"\r\n                    :page-sizes=\"pageOptions.pageSizeitems\"\r\n                    :page-size=\"pageOptions.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next, jumper\"\r\n                    :total=\"pageOptions.total\"\r\n                    background\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <QRcode :ref=\"'QRcode' + EquipmentId\" @getQRcodesRes=\"getQRcodesRes\"></QRcode>\r\n        <el-drawer size=\"55%\" :wrapperClosable=\"false\" @close=\"closeDraw\" :title=\"$t('POListTipping.Tipping')\" :visible.sync=\"detailShow\" direction=\"rtl\">\r\n            <div class=\"InventorySearchBox\">\r\n                <div class=\"searchbox\">\r\n                    <div class=\"inputformbox\" size=\"small\" style=\"width: 300px\">\r\n                        <el-input :placeholder=\"$t('precheck.TraceCode')\" v-model=\"TraceCode\" @keyup.enter.native=\"searchInventory()\">\r\n                            <template slot=\"append\"><i slot=\"suffix\" class=\"el-icon-full-screen\" @click=\"searchInventory()\"></i></template>\r\n                        </el-input>\r\n                    </div>\r\n                    <el-button class=\"tablebtn\" icon=\"el-icon-refresh-left\" @click=\"ShowQRCode()\">\r\n                        {{ $t('Consume.Scan') }}\r\n                    </el-button>\r\n                    <div class=\"preparaStatusbox\" style=\"font-size: 16px\">{{ $t('GLOBAL.Number') }}：{{ count }}</div>\r\n                </div>\r\n            </div>\r\n            <el-table :data=\"drawertableList\" style=\"width: 100%\">\r\n                <el-table-column\r\n                    v-for=\"(item, index) in drawerheader\"\r\n                    :key=\"index\"\r\n                    :fixed=\"item.fixed ? item.fixed : false\"\r\n                    :align=\"item.align\"\r\n                    :prop=\"item.prop ? item.prop : item.value\"\r\n                    :label=\"$t(`$vuetify.dataTable.${tableId2}.${item.value}`)\"\r\n                    :width=\"item.width\"\r\n                >\r\n                    <template v-slot:header=\"scope\">\r\n                        <span v-if=\"item.icon\">\r\n                            <i :class=\"item.icon\"></i>\r\n                        </span>\r\n                        <span v-if=\"!item.icon\">{{ scope.column.label }}</span>\r\n                    </template>\r\n                    <template slot-scope=\"scope\">\r\n                        <span v-if=\"scope.column.property != 'detail'\">\r\n                            <span v-if=\"scope.column.property == 'Material'\">\r\n                                <div>{{ scope.row.MaterialCode }}</div>\r\n                                <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.Quantity }}{{ scope.row.Unit1 }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Precheckestatus'\">\r\n                                <div\r\n                                    class=\"preparaTableStatusbox\"\r\n                                    style=\"color: black\"\r\n                                    :style=\"{ background: scope.row.Precheckestatus == '0' ? '#FFA500' : scope.row.Precheckestatus == '1' ? '#FFA500' : '#3DCD58' }\"\r\n                                >\r\n                                    {{ scope.row.Precheckestatus == '0' ? '未检查' : scope.row.Precheckestatus == '1' ? '未投料' : '已投料' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n        </el-drawer>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { POManagemenTipping, TippingDrawColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { MygetContentValue, MygetContent, TippingCount, GetTippingSclist, GetBatchEntity, GetTippingMlistView, OverTipping, StartTipping, ScanTipping } from '@/api/Inventory/Overview.js';\r\n\r\nexport default {\r\n    name: 'Consume',\r\n    data() {\r\n        return {\r\n            QuickSearch: '',\r\n            TippingStatus: 1,\r\n            pageOptions: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            header: POManagemenTipping,\r\n            tableId2: 'INV_YJC',\r\n            tableList: [],\r\n            tableId: 'PRO_Tipping',\r\n            PrepStatus: '',\r\n            Content: '',\r\n            ContentValue: '',\r\n            countFlag: false,\r\n            detailShow: false,\r\n            TraceCode: '',\r\n            count: '',\r\n            drawertableList: [],\r\n            drawerheader: TippingDrawColumn,\r\n            BatchId: '',\r\n            RunEquipmentId: '',\r\n            SortOrder: '',\r\n            PoExecutionId: '',\r\n            EquipmentId: ''\r\n        };\r\n    },\r\n    mounted() {\r\n        this.changePagination();\r\n    },\r\n    methods: {\r\n        ShowQRCode() {\r\n            this.$refs[`QRcode${this.EquipmentId}`].getQRcode();\r\n        },\r\n        // 获取查询结果\r\n        getQRcodesRes(val) {\r\n            console.log(val, 123123);\r\n            this.TraceCode = val.text;\r\n            this.searchInventory();\r\n        },\r\n        getEquipmentModal(item, Equipmentitem) {\r\n            if (Equipmentitem) {\r\n                this.EquipmentId = Equipmentitem.ID;\r\n                this.BatchId = Equipmentitem.BatchId;\r\n                this.RunEquipmentId = Equipmentitem.RunEquipmentId;\r\n                this.PoExecutionId = Equipmentitem.ExecutionId;\r\n                this.getContent();\r\n            } else {\r\n                this.BatchId = '';\r\n                this.RunEquipmentId = '';\r\n                this.PoExecutionId = '';\r\n            }\r\n            this.GetTippinglist();\r\n            this.BatchEntity();\r\n        },\r\n        async getContent() {\r\n            let params = {\r\n                ExecutionId: this.PoExecutionId,\r\n                Name: 'RequestFeeding'\r\n            };\r\n            let res = await MygetContent(params);\r\n            let data = res.response;\r\n            this.Content = data.Content;\r\n        },\r\n        async getContentValue() {\r\n            let params = {\r\n                EquipmentId: this.EquipmentId,\r\n                FunctionCode: 'Tipping',\r\n                PropertyCode: 'ButtonEnable'\r\n            };\r\n            let res = await MygetContentValue(params);\r\n            if (res.response == null) {\r\n                res.response = '';\r\n            }\r\n            this.ContentValue = res.response;\r\n        },\r\n        async TippingOver(parm) {\r\n            let params = {\r\n                SortOrder: this.SortOrder,\r\n                BatchId: this.BatchId,\r\n                IsForcedCompletion: false,\r\n                RunEquipmentId: this.RunEquipmentId,\r\n                PoExecutionId: this.PoExecutionId\r\n            };\r\n            if (parm) {\r\n                params.IsForcedCompletion = parm;\r\n            }\r\n            let res = await OverTipping(params);\r\n            if (res.success) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n                this.BatchEntity();\r\n            }\r\n        },\r\n        getsearch() {\r\n            // this.MyGetTippingSclist();\r\n            this.GetTippinglist();\r\n            this.BatchEntity();\r\n        },\r\n        async BatchEntity() {\r\n            if (this.BatchId != '') {\r\n                let params = {\r\n                    BatchId: this.BatchId,\r\n                    RunEquipmentId: this.RunEquipmentId,\r\n                    PoExecutionId: this.PoExecutionId\r\n                };\r\n                let res = await GetBatchEntity(params);\r\n                if (res) {\r\n                    this.SortOrder = res.response.SortOrder;\r\n                    this.PrepStatus = res.response.Status;\r\n                }\r\n            } else {\r\n                this.PrepStatus = '';\r\n            }\r\n        },\r\n        closeDraw() {\r\n            this.GetTippinglist();\r\n        },\r\n        async startScan() {\r\n            this.reLoadScan();\r\n            this.detailShow = true;\r\n        },\r\n        async searchInventory() {\r\n            if (this.TraceCode === '' || this.TraceCode === null) {\r\n                Message({\r\n                    message: `${this.$t('ConsumptionHistory.NonSSCC')}`,\r\n                    type: 'error'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                Tracecode: this.TraceCode,\r\n                RunEquipmentId: this.RunEquipmentId,\r\n                PoExecutionId: this.PoExecutionId\r\n            };\r\n            let res = await ScanTipping(params);\r\n            if (res.success) {\r\n                this.TraceCode = '';\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n                this.reLoadScan();\r\n                this.GetTippinglist();\r\n                this.BatchEntity();\r\n            }\r\n        },\r\n        async reLoadScan() {\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                SortOrder: this.SortOrder\r\n            };\r\n            let res = await GetTippingSclist(params);\r\n            this.drawertableList = res.response;\r\n            let params2 = {\r\n                BatchId: this.BatchId,\r\n                SortOrder: this.SortOrder\r\n            };\r\n            let res2 = await TippingCount(params2);\r\n            this.count = res2.response;\r\n            if (this.count.split('/')[0] == this.count.split('/')[1]) {\r\n                this.countFlag = true;\r\n            } else {\r\n                this.countFlag = false;\r\n            }\r\n        },\r\n        async startTip() {\r\n            let parmas = {\r\n                SortOrder: this.SortOrder,\r\n                BatchId: this.BatchId,\r\n                RunEquipmentId: this.RunEquipmentId,\r\n                PoExecutionId: this.PoExecutionId\r\n            };\r\n            let res = await StartTipping(parmas);\r\n            this.GetTippinglist();\r\n            this.BatchEntity();\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        changePagination() {\r\n            let el2 = document.getElementsByClassName(`el-select-dropdown__item`);\r\n            for (let i = 0; i < el2.length; i++) {\r\n                el2[i].innerHTML = el2[i].innerHTML.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            }\r\n        },\r\n        async GetTippinglist() {\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                RunEquipmentId: this.RunEquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res = await GetTippingMlistView(params);\r\n            if (res) {\r\n                this.tableList = res.response.data;\r\n                this.pageOptions.total = res.response.dataCount;\r\n            }\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n        },\r\n        handleSizeChange(val) {\r\n            this.pageOptions.pageSize = val;\r\n            this.GetTippinglist();\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.pageOptions.page = val;\r\n            this.GetTippinglist();\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.Tipping {\r\n    .searchtipbox {\r\n        margin: 0 5px;\r\n        height: 30px;\r\n        padding: 0 2vh;\r\n        border-radius: 5px;\r\n        display: flex;\r\n        margin-bottom: 0.5vh;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: #fff;\r\n        font-size: 14px;\r\n    }\r\n    .searchboxtitle {\r\n        font-size: 1.7vh;\r\n        color: #767777;\r\n        padding-bottom: 5px;\r\n        margin-left: 10px;\r\n    }\r\n\r\n    .el-tabs {\r\n        height: 97%;\r\n    }\r\n\r\n    .subsubtabs {\r\n        .el-tabs--border-card {\r\n            border: 0 !important;\r\n            box-shadow: none !important;\r\n        }\r\n    }\r\n\r\n    .paginationbox {\r\n        height: 10vh;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin-top: 10px;\r\n\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 47%;\r\n            text-align: right;\r\n        }\r\n\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n\r\n    .splitdetailbox {\r\n        padding-bottom: 10px;\r\n        border: 1px solid #e8e8e8;\r\n        margin-bottom: 5px;\r\n\r\n        .splitdetailboxtitle {\r\n            background: #f5f5f5;\r\n            height: 3.5vh;\r\n            display: flex;\r\n            align-items: center;\r\n            padding-left: 5px;\r\n            font-size: 1.1rem;\r\n            color: #303133;\r\n        }\r\n\r\n        .detailsnote {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 0.9rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n\r\n        .splitdetailboxtitleTag {\r\n            margin-left: 5px;\r\n            background: #5cb85c;\r\n            color: #fff;\r\n            border-color: #5cb85c;\r\n        }\r\n    }\r\n}\r\n\r\n#Tipping {\r\n    .el-input {\r\n        width: 250px !important;\r\n    }\r\n\r\n    .el-select {\r\n        width: 250px !important;\r\n    }\r\n}\r\n</style>\r\n"]}]}