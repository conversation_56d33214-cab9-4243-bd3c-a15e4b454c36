{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\productionManagement\\ResourceDefinition\\components\\drawer.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\productionManagement\\ResourceDefinition\\components\\drawer.vue", "mtime": 1750254216359}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["drawer.vue"], "names": [], "mappings": ";AAiEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "drawer.vue", "sourceRoot": "src/views/productionManagement/ResourceDefinition/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-drawer class=\"drawer\" :visible.sync=\"drawer\" :direction=\"'rtl'\" :before-close=\"handleClose\" size=\"50%\"\r\n      :with-header=\"!showSales\">\r\n      <div slot=\"title\" class=\"title-box\">\r\n        <span>{{ `${currentRow.Name} | ${currentRow.Code}` }}</span>\r\n      </div>\r\n      <section v-if=\"!showSales\">\r\n        <el-tabs v-model=\"tabIndex\" type=\"border-card\" @tab-click=\"changeTabs\">\r\n          <el-tab-pane label=\"工段\" name=\"1\"></el-tab-pane>\r\n          <el-tab-pane label=\"工序\" name=\"2\"></el-tab-pane>\r\n          <el-tab-pane label=\"工序与设备\" name=\"3\"></el-tab-pane>\r\n        </el-tabs>\r\n        <div class=\"InventorySearchBox mt5\">\r\n          <div class=\"searchbox pd5\">\r\n            <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\r\n              <el-form-item :label=\"$t('GLOBAL._SSL')\">\r\n                <el-input clearable v-model=\"searchForm.Key\"></el-input>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button icon=\"el-icon-search\" @click=\"getSearchBtn\">{{ $t('GLOBAL._CX') }}</el-button>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">{{\r\n                  $t('GLOBAL._XZ') }}\r\n                </el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n        </div>\r\n        <div class=\"table-box\">\r\n          <el-table v-loading=\"loading\" :data=\"tableData\" element-loading-text=\"拼命加载中\"\r\n            element-loading-spinner=\"el-icon-loading\" style=\"width: 100%\" height=\"83vh\">\r\n            <el-table-column v-for=\"(item, index) in tableName\" :key=\"index\" :prop=\"item.field\" :label=\"item.label\">\r\n              <template slot-scope=\"scope\">\r\n                <div v-if=\"item.field === 'SegmentName'\" class=\"combination\">\r\n                  <i v-if=\"tabIndex === '2'\" class=\"el-icon-document\" @click=\"showSalesView(scope.row)\"></i>\r\n                  <span>\r\n                   {{ scope.row.SegmentName }}\r\n                  </span>\r\n                </div>\r\n                <span v-else> {{ scope.row[item.field] }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._BJ') }}</el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </section>\r\n      <section v-else>\r\n        <SalesContainer ref=\"sales\" @show-sales=\"showSales = false\" :data=\"salesData\" @show-dialog=\"showSalesDialog\" />\r\n      </section>\r\n    </el-drawer>\r\n    <FormDialogOperation @saveForm=\"refreshTableData\" ref=\"operation\" />\r\n    <FromDialogPhase @saveForm=\"refreshTableData\" ref=\"phase\" />\r\n    <FromDialogPhaseMachine @saveForm=\"refreshTableData\" ref=\"phaseMachine\" />\r\n    <ForemDialogSales ref=\"salesDialog\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getSapSegmentList, getSapSegment, getSapSegmentEquipmentList, deleteOperation, deleteSapSegmentEquipment } from '@/api/productionManagement/Formula';\r\nimport FormDialogOperation from './form-dialog-operation.vue'\r\nimport FromDialogPhase from './form-dialog-phase.vue';\r\nimport FromDialogPhaseMachine from './form-dialog-phaseMachine'\r\nimport SalesContainer from './salesContainer.vue'\r\nimport ForemDialogSales from './form-dialog-sales.vue'\r\nexport default {\r\n  name: 'drawer',\r\n  components: {\r\n    FormDialogOperation,\r\n    FromDialogPhase,\r\n    FromDialogPhaseMachine,\r\n    SalesContainer,\r\n    ForemDialogSales\r\n  },\r\n  data() {\r\n    return {\r\n      searchForm: {},\r\n      tableData: [],\r\n      tableName: [],\r\n      loading: false,\r\n      drawer: false,\r\n      tabIndex: '1',\r\n      sapEquipmentId: 0,\r\n      hansObjOperation: this.$t('Formula.Resouce_Operation'),\r\n      hansObjPhase: this.$t('Formula.Resouce_Phase'),\r\n      hansObjPhaseMachine: this.$t('Formula.Resouce_Phase_Machine'),\r\n      parentId: 0,\r\n      currentRow: {},\r\n      showSales: false,\r\n      salesData: {}\r\n    }\r\n  },\r\n  methods: {\r\n    showSalesView(row) {\r\n      this.salesData = row\r\n      this.showSales = true\r\n      this.$nextTick(_ => {\r\n        this.$refs.sales.getTableData()\r\n      })\r\n    },\r\n    show(data) {\r\n      this.tabIndex = '1'\r\n      this.sapEquipmentId = data.ID\r\n      this.currentRow = data\r\n      this.getOperationData()\r\n      this.initTableHead(this.hansObjOperation)\r\n      this.drawer = true\r\n    },\r\n    handleClose() {\r\n      this.drawer = false\r\n    },\r\n    changeTabs() {\r\n      switch (this.tabIndex) {\r\n        case '1':\r\n          this.initTableHead(this.hansObjOperation)\r\n          this.getOperationData()\r\n          break;\r\n        case '2':\r\n          this.initTableHead(this.hansObjPhase)\r\n          this.getPhaseData()\r\n          break;\r\n        case '3':\r\n          this.initTableHead(this.hansObjPhaseMachine)\r\n          this.getPhaseMechineData()\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    showDialog(val) {\r\n      switch (this.tabIndex) {\r\n        case '1':\r\n          val.SapEquipmentId = this.sapEquipmentId\r\n          this.$refs.operation.show(val)\r\n          break;\r\n        case '2':\r\n          val.SapEquipmentId = this.sapEquipmentId\r\n          this.$refs.phase.show(val)\r\n          break;\r\n        case '3':\r\n          val.SapEquipmentId = this.sapEquipmentId\r\n          this.$refs.phaseMachine.show(val)\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    async delRow({ ID }) {\r\n      this.$confirms({\r\n        title: this.$t('GLOBAL._TS'),\r\n        message: this.$t('GLOBAL._COMFIRM'),\r\n        confirmText: this.$t('GLOBAL._QD'),\r\n        cancelText: this.$t('GLOBAL._QX')\r\n      }).then(() => {\r\n        switch (this.tabIndex) {\r\n          case '1':\r\n            deleteOperation([ID]).then(({ msg }) => {\r\n              this.$message.success(msg)\r\n              this.getOperationData()\r\n            })\r\n            break;\r\n          case '2':\r\n            deleteOperation([ID]).then(({ msg }) => {\r\n              this.$message.success(msg)\r\n              this.getPhaseData()\r\n            })\r\n            break;\r\n          case '3':\r\n            deleteSapSegmentEquipment([ID]).then(({ msg }) => {\r\n              this.$message.success(msg)\r\n              this.getPhaseMechineData()\r\n            })\r\n            break;\r\n          default:\r\n            break;\r\n        }\r\n      }).catch(err => {\r\n        console.log(err);\r\n      });\r\n\r\n    },\r\n    // emit刷新数据\r\n    refreshTableData() {\r\n      switch (this.tabIndex) {\r\n        case '1':\r\n          this.getOperationData()\r\n          break;\r\n        case '2':\r\n          this.getPhaseData()\r\n          break;\r\n        case '3':\r\n          this.getPhaseMechineData()\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    // 获取数据\r\n    async getOperationData() {\r\n      this.loading = true\r\n      const { response } = await getSapSegmentList({\r\n        Level: 1,\r\n        SapEquipmentId: this.sapEquipmentId,\r\n        ...this.searchForm\r\n      })\r\n      this.loading = false\r\n      this.tableData = response\r\n    },\r\n    async getPhaseData() {\r\n      this.loading = true\r\n      const { response } = await getSapSegmentList({\r\n        Level: 2,\r\n        SapEquipmentId: this.sapEquipmentId,\r\n        ...this.searchForm\r\n      })\r\n      this.loading = false\r\n      this.tableData = response\r\n    },\r\n    async getPhaseMechineData() {\r\n      this.loading = true\r\n      const { response } = await getSapSegmentEquipmentList({\r\n        SapEquipmentId: this.sapEquipmentId,\r\n        ...this.searchForm\r\n      })\r\n      this.loading = false\r\n      this.tableData = response\r\n    },\r\n    // 初始化表头\r\n    initTableHead(obj) {\r\n      this.tableName = []\r\n      for (let key in obj) {\r\n        this.tableName.push({ field: key, label: obj[key] })\r\n      }\r\n    },\r\n    // 搜索\r\n    getSearchBtn() {\r\n      this.refreshTableData()\r\n    },\r\n    showSalesDialog(row) {\r\n      // console.log(row);\r\n      this.$refs.salesDialog.show(row)\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.drawer {\r\n  :deep(.el-drawer__body) {\r\n    background-color: #FFFFFF;\r\n    overflow-y: hidden\r\n  }\r\n\r\n  :deep(.el-form--inline) {\r\n    height: 32px;\r\n  }\r\n\r\n  .title-box {\r\n    font-size: 18px;\r\n    color: #909399;\r\n  }\r\n\r\n  .mt5 {\r\n    margin-top: 5px;\r\n  }\r\n\r\n  .pd5 {\r\n    padding: 5px;\r\n  }\r\n\r\n  .table-box {\r\n    padding: 0 10px;\r\n\r\n    .combination {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n\r\n    i {\r\n      margin-right: 5px;\r\n      font-size: 15px !important;\r\n      color: #67c23a;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}