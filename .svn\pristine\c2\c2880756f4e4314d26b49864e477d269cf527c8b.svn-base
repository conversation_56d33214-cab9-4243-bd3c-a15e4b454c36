<template>
    <div class="usemystyle overview">
        <div class="InventorySearchBox">
            <div class="searchbox">
                <el-button style="margin-left: 5px" size="small" icon="el-icon-back" @click="back()">{{ this.$t('Overview.Back') }}</el-button>
                <div class="searchboxtitle">
                    {{ viewtitle }}
                </div>
            </div>
        </div>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
            <el-tab-pane :label="$t('Overview.Overview')" name="1">
                <div class="tablebox">
                    <el-table :data="tableList" style="width: 100%" height="700">
                        <el-table-column
                            v-for="(item, index) in header"
                            :key="index"
                            :align="item.align"
                            :prop="item.prop ? item.prop : item.value"
                            :label="$t(`$vuetify.dataTable.${tableId}.${item.value}`)"
                            :width="item.width"
                        >
                            <template slot-scope="scope">
                                <span v-if="scope.row.ProcessOrder && scope.column.property == 'Complete'">
                                    <el-progress
                                        :text-inside="true"
                                        color="#3dcd58"
                                        text-color="#000000"
                                        :stroke-width="26"
                                        :percentage="Number(((scope.row.Total / scope.row.TargetQuantity) * 100).toFixed(2))"
                                    ></el-progress>
                                </span>
                                <span v-else-if="scope.column.property == 'PlantNode'">
                                    <div>{{ scope.row.EquipmentCode }}</div>
                                    <div style="color: #808080">{{ scope.row.EquipmentName }}</div>
                                </span>
                                <span v-else-if="scope.column.property == 'Material'">
                                    <div>{{ scope.row.MaterialCode }}</div>
                                    <div style="color: #808080">{{ scope.row.MaterialName }}</div>
                                </span>
                                <span v-else-if="scope.column.property == 'BatchQty'">{{ scope.row.BatchQty }}{{ scope.row.Unit1 }}</span>
                                <span v-else-if="scope.column.property == 'Sequence'">{{ scope.row.ProcessOrder != null ? scope.row.Sequence : '' }}</span>
                                <span v-else>{{ scope.row[item.prop] }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="paginationbox">
                        <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="pageOptions.page"
                            :page-sizes="pageOptions.pageSizeitems"
                            :page-size="pageOptions.pageSize"
                            layout="total, sizes, prev, pager, next"
                            :total="pageOptions.total"
                            background
                        ></el-pagination>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane v-if="ShowPOList" :label="$t('Overview.POList')" name="2">
                <div class="InventorySearchBox">
                    <div class="searchbox">
                        <div class="datebox">
                            <div class="datepickbox">
                                <el-date-picker
                                    v-model="timepicker"
                                    type="daterange"
                                    value-format="yyyy-MM-dd"
                                    range-separator="-"
                                    :start-placeholder="$t('DFM_RL._KSRQ')"
                                    :end-placeholder="$t('DFM_RL._JSRQ')"
                                ></el-date-picker>
                            </div>
                        </div>
                        <div class="inputformbox" :style="{ width: item.width }" v-for="(item, index) in searchlist" :key="index">
                            <el-input v-if="item.type == 'input'" v-model="item.value" :myid="item.id" :placeholder="item.name"></el-input>
                            <el-select :style="{ width: item.width }" v-model="item.value" v-if="item.type == 'select'" :myid="item.id" :placeholder="item.name">
                                <el-option v-for="(it, ind) in item.option" :key="ind" :label="it.value" :value="it.key"></el-option>
                            </el-select>
                        </div>
                        <el-button style="margin-left: 5px" size="small" icon="el-icon-refresh" @click="getsearch()">{{ this.$t('Inventory.refresh') }}</el-button>
                        <el-button size="small" style="margin-left: 5px" icon="el-icon-s-help" @click="getempty()">{{ this.$t('GLOBAL._CZ') }}</el-button>
                    </div>
                </div>
                <div class="tablebox">
                    <el-table :data="AvailablePOManagemenList" style="width: 100%" height="670">
                        <el-table-column
                            v-for="(item, index) in Availableheader"
                            :key="index"
                            :align="item.align"
                            :prop="item.prop ? item.prop : item.value"
                            :label="$t(`$vuetify.dataTable.${AvailabletableId}.${item.value}`)"
                            :width="item.width"
                        >
                            <template slot-scope="scope">
                                <!-- <span v-if="scope.column.property == 'operate'">
                                    <el-button size="mini" class="operatebtn" v-if="scope.row.Status > 1 && scope.row.RunningCount == 0" @click="startOrder(scope)" icon="el-icon-video-play">
                                        {{ $t('Overview.start') }}
                                    </el-button>
                                </span> -->
                                <span v-if="scope.column.property == 'PlanStartTime'">{{ $dayjs(scope.row.PlanStartTime).format('YYYY-MM-DD HH:mm') }}</span>
                                <span v-else-if="scope.column.property == 'PlanEndTime'">{{ $dayjs(scope.row.PlanEndTime).format('YYYY-MM-DD HH:mm') }}</span>
                                <span v-else-if="scope.column.property == 'IsHavePreservative'">
                                    <i :class="scope.row[item.value] === '1' ? 'el-icon-star-on' : ''"></i>
                                </span>
                                <span v-else-if="scope.column.property == 'LineNominalSpeed'">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>
                                <span v-else>{{ scope.row[item.prop] }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="paginationbox">
                        <el-pagination
                            @size-change="handleSizeChange2"
                            @current-change="handleCurrentChange2"
                            :current-page="pageOptions2.page"
                            :page-sizes="pageOptions2.pageSizeitems"
                            :page-size="pageOptions2.pageSize"
                            layout="total, sizes, prev, pager, next"
                            :total="pageOptions2.total"
                            background
                        ></el-pagination>
                    </div>
                    <el-dialog :title="$t('Overview.StartOrder')" id="Startdialog" :visible.sync="StartModel" :width="IsPack == '0' ? '1050px' : '650px'">
                        <span slot="title" class="dialog-title">
                            <div class="dialogtitlebox">
                                {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.StartOrder') }}
                                <div class="dialogsubtitlebox" style="display: inline">{{ chooseItem.ProcessOrder }}</div>
                            </div>
                        </span>
                        <div class="splitdetailbox">
                            <div class="splitdetailboxtitle">{{ chooseItem.MaterialCode }}-{{ chooseItem.MaterialName }}</div>
                            <div class="detailsnote" v-if="runningCode != '' && !chooseItem.isResume">
                                {{ $t('Overview.Note1') }}
                                <span style="font-weight: 600">{{ runningCode }}</span>
                                {{ $t('Overview.Note2') }}
                            </div>
                            <div style="display: flex">
                                <div :style="{ width: IsPack == '0' ? '100%' : '100%' }">
                                    <div class="dialogdetailbox">
                                        <div class="dialogdetailsinglelabel" :style="{ width: '10%' }">{{ $t('Overview.ChooseEquipment') + ' *' }}</div>
                                        <div class="dialogdetailsinglevalue " :style="{ width: '87%' }">
                                            <el-select style="width: 92%" v-model="MyEquipment" @change="getMyEquipment()" filterable>
                                                <el-option v-for="(it, index) in MyEquipmentList" :key="index" :label="it.EquipmentName" :value="it.ID"></el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                    <div class="dialogdetailbox" v-for="(item, index) in Startlist" :key="index">
                                        <div class="dialogdetailsinglelabel" :style="{ width: item.type == 'BatchCode' ? '10%' : '10%' }">{{ item.label }}{{ item.require ? ' *' : '' }}</div>
                                        <div class="dialogdetailsinglevalue " :style="{ width: item.type == 'BatchCode' || item.type == 'checkBox' ? '80%' : '80%' }">
                                            <el-input style="width: 100%" v-if="item.type == 'input'" v-model="item.value"></el-input>
                                            <!-- <div class="detailsnote2" v-else-if="item.type == 'checkBox' && IsDifferent === true && IsPack === '0'">
                                                {{ $t('Overview.IsUpdateLtxt') }}
                                            </div>
                                            <div class="detailsnote3" v-else-if="item.type == 'checkBox' && IsDifferent === false && IsPack === '0'">
                                                {{ $t('Overview.TextGood') }}
                                            </div> -->
                                            <!-- <div v-else-if="item.type == 'BatchCode'" style="display: flex">
                                                <el-input v-model="item.value"></el-input>
                                                <el-input v-model="item.value2" disabled></el-input>
                                                <el-input v-model="item.value3"></el-input>
                                                <el-button
                                                    class="tablebtn"
                                                    @click="getBatchCode()"
                                                    size="mini"
                                                    style="margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff"
                                                    icon="el-icon-refresh"
                                                ></el-button>
                                            </div> -->
                                            <el-select style="width: 100%" clearable v-else-if="item.type == 'select'" v-model="item.value" filterable>
                                                <el-option v-for="it in item.option" :key="it.ID" :label="it.Number" :value="it.ID"></el-option>
                                            </el-select>
                                            <el-date-picker
                                                @change="GetDate(item.id)"
                                                v-else-if="item.type == 'date'"
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                :disabled="item.disabled"
                                                v-model="item.value"
                                                type="datetime"
                                                style="width: 100%"
                                            ></el-date-picker>
                                            <span v-else-if="item.id == 'TargetQuantity'">{{ chooseItem.TargetQuantity }}{{ chooseItem.Unit1 }}</span>
                                        </div>
                                    </div>
                                </div>
<!--                                <div v-if="IsPack == '0'" style="height: 100%; margin-top: 10px; padding: 0 10px; box-shadow: none; display: flex; flex-direction: column">-->

<!--                                    <span class="dialogsubtitlebox" style="margin-bottom: 5px">工单长文本：</span>-->
<!--                                    <el-input type="textarea" :autosize="{ minRows: 5 }" style="width: 370px !important" :disabled="isEdit == false" v-model="Text1"></el-input>-->
<!--                                    <span class="dialogsubtitlebox" style="margin: 5px 0">配方长文本：</span>-->
<!--                                    <el-input type="textarea" :autosize="{ minRows: 5 }" style="width: 370px !important" :disabled="isEdit == false" v-model="Text2"></el-input>-->
<!--                                </div>-->
                            </div>
                          <div style="padding: 18px 18px 0 18px">
                            <el-row>
                              <el-col :span="12">
                                <span class="font-M09 bold">工单长文本</span>
                              </el-col>
                              <el-col :span="12">
                                <span class="font-M09 bold">配方长文本</span>
                              </el-col>
                            </el-row>
                            <CodeDiff
                                hideHeader
                                :old-string="Text1"
                                :new-string="Text2"
                                output-format="side-by-side"
                            />
                          </div>

                        </div>
                        <span slot="footer" class="dialog-footer">
                            <el-button style="float: left" v-if="chooseItem.isResume">
                                {{ $t('Overview.bottleneck') }}
                            </el-button>
                            <el-button class="tablebtn" icon="el-icon-video-play" @click="ProducedStart()">
                                {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.Start') }}
                            </el-button>
                            <el-button @click="StartModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
                        </span>
                    </el-dialog>
                </div>
            </el-tab-pane>
            <el-tab-pane v-for="(item, index) in Equipmentlist" :key="index" :disabled="item.FunctionCodes != '' ? false : true" :name="item.ID">
                <span slot="label">
                    <i :class="item.FunctionCodes != '' ? 'el-icon-s-tools' : 'el-icon-s-grid'"></i>
                    <span>{{ item.EquipmentName }}</span>
                    <div v-if="item.FunctionCodes != ''" class="tabiconbox">
                        <span v-if="item.FunctionCodes.indexOf('POManagement') != -1">
                            <i
                                :style="{ color: item.ProductionOrderId == null ? 'red' : '#3DCD58' }"
                                :class="item.ProductionOrderId == null ? 'iconfont icon-pausecircle-fill' : 'iconfont icon-play-fill'"
                            ></i>
                        </span>
                        <!-- <i class="el-icon-s-marketing"></i> -->
                    </div>
                </span>
                <div class="subtabs">
                    <div class="activeTitle" v-if="ActiveList.length == 0">{{ $t('Overview.NoActiveProcessOrder') }}</div>
                    <div class="activeTitle" v-else>
                        <div class="activeBox">
                            <div class="activeLabel">{{ ActiveList[0].ProcessOrder }}({{ ActiveList[0].Number }})</div>
                            <div class="activeValue">{{ ActiveList[0].Material }}</div>
                        </div>
                        <div class="activeBox">
                            <div class="activeLabel">{{ ActiveList[0].TargetQuantity }}</div>
                            <div class="activeValue">{{ ActiveList[0].Unit1 }}</div>
                        </div>
                        <div class="activeBox">
                            <div class="activeLabel">{{ ActiveList[0].Speed }}</div>
                            <div class="activeValue">{{ ActiveList[0].SpeedUom }}</div>
                        </div>
                        <div class="activeBox">
                            <div class="activeLabel">{{ $t('Overview.BatchCode') }}</div>
                            <div class="activeValue">{{ ActiveList[0].BatchCode }}</div>
                        </div>
                        <div class="activeBox">
                            <div class="activeLabel">{{ $t('Overview.ExpirationDate') }}</div>
                            <div class="activeValue">{{ ActiveList[0].ExpirationDate }}</div>
                        </div>
                               <div class="activeBox" v-if="ActiveList[0].StorageTank != ''&&ActiveList[0].StorageTank != null">
                            <div class="activeLabel">{{ $t('Overview.CGBM') }}</div>
                            <div class="activeValue">{{ ActiveList[0].StorageTank }}</div>
                        </div>
                               <div class="activeBox" v-if="ActiveList[0].StorageTankOrderGc != ''&&ActiveList[0].StorageTankOrderGc != null">
                            <div class="activeLabel">{{ $t('Overview.GC') }}</div>
                            <div class="activeValue">{{ ActiveList[0].StorageTankOrderGc }}</div>
                        </div>
                    </div>
                    <div class="subtabsbox">
                        <el-tabs v-model="activeName2" type="border-card" @tab-click="handleClick2(item, index)">
                            <!-- v-if="item.functionlist.includes('PO Management') != -1" -->
                            <!-- <el-tab-pane :label="$t('Overview.POManagement')" name="1">
                                <POManagement ref="POManagement" @getNum="getNum" :EquipmentId="EquipmentId"></POManagement>
                            </el-tab-pane> -->
                            <el-tab-pane :label="$t(`Overview.${it.trim()}`)" v-for="(it, ind) in item.functionlist" :key="ind" :name="it.trim()">
                                <div>
                                    <component
                                        @loadProgress="loadProgress"
                                        :is="it.trim() == 'Tipping' ? (isTippingscan ? 'Tippingscan' : 'Tipping') : it.trim()"
                                        :ref="index + it.trim()"
                                        :BatchId="BatchId"
                                        :EquipmentName="item.EquipmentName"
                                        :ExecutionId="ExecutionId"
                                        :EquipmentId="EquipmentId"
                                        :RunEquipmentId="RunEquipmentId"
                                    ></component>
                                </div>
                            </el-tab-pane>
                            <!-- <el-tab-pane label="SampleWeighing" name="SampleWeighing">
                                <SampleWeighing :ref="index + 'SampleWeighing'" :EquipmentId="EquipmentId"></SampleWeighing>
                            </el-tab-pane> -->
                            <!-- <el-tab-pane label="ParameterDownload" name="ParameterDownload">
                                <ParameterDownload :ref="index + 'ParameterDownload'" :EquipmentId="EquipmentId"></ParameterDownload>
                            </el-tab-pane> -->
                            <!--
                            <el-tab-pane label="Performance" name="Performance">
                                <Performance :ref="index + 'Performance'" :EquipmentId="EquipmentId"></Performance>
                            </el-tab-pane> -->
                            <!-- <el-tab-pane label="Tipping" name="Tipping">
                                <Tipping :ref="index + 'Tipping'"></Tipping>
                            </el-tab-pane> -->
                            <!-- <el-tab-pane label="Logsheets" name="Logsheets">
                                <Logsheets :ref="index + 'Logsheets'"></Logsheets>
                            </el-tab-pane> -->
                        </el-tabs>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';
import { POManagemenOverview } from '@/columns/factoryPlant/tableHeaders';
import {
    GetProcessOrderViewSegments,
    GetProcessOrderView,
    GetEquipmentFunctionView,
    GetEquipmentProcessOrderView,
    getCheckTippingType,
    GetCookieOrderLtexts,
    GetBBatchListView,
    GetBatchCode,
    PoProducedStart,
    PoProducedResume,
    GetRunOrder,
    GetCookOrderLtexts,
    GetProcessOrderViewSegmentUnits,
    GetPoList
} from '@/api/Inventory/Overview.js';
import { POManagemenPoList } from '@/columns/factoryPlant/tableHeaders';
import moment from 'moment';

import { Message } from 'element-ui';
import { CodeDiff } from 'v-code-diff'
export default {
    components: {
        ParameterDownload: () => import('./components/ParameterDownload'),
        POManagement: () => import('./components/POManagement'),
        Consume: () => import('./components/Consume'),
        Produce: () => import('./components/Produce'),
        Tipping: () => import('./components/Tipping'),
        Storage: () => import('./components/Storage'),
                Performance: () => import('./components/Performance'),
        MaterialPrep: () => import('./components/MaterialPrep'),
        PerformanceEvents: () => import('./components/Performance'),
        Logsheets: () => import('./components/Logsheets'),
        Tippingscan: () => import('./components/Tippingscan'),
        SampleWeighing: () => import('./components/SampleWeighing'),
        ProcessText: () => import('./components/Processlongtext'),
      CodeDiff
    },
    data() {
        return {
            viewtitle: '',
            timepicker: [],
            AvailablePOManagemenList: [],
            Availableheader: POManagemenPoList,
            StartModel: false,
            MyEquipment: '',
            ShowPOList: false,
            MyEquipmentList: [],
            Startlist: [
                {
                    label: this.$t('Overview.StartTime'),
                    id: 'StartTime',
                    value: '',
                    disabled: true,
                    type: 'date'
                },
                {
                    label: this.$t('Overview.Batch'),
                    id: 'BatchId',
                    value: '',
                    require: true,
                    type: 'select',
                    option: []
                },
                {
                    label: this.$t('Overview.BatchCode'),
                    id: 'LotCode',
                    value: '',
                    value2: '',
                    value3: '',
                    require: true,
                    type: 'BatchCode'
                },
                {
                    label: this.$t('Overview.ProductionDate'),
                    id: 'ProductionDate',
                    require: true,
                    value: '',
                    type: 'date'
                },
                {
                    label: this.$t('Overview.ExpirationDate'),
                    id: 'ExpirationDate',
                    require: true,
                    value: '',
                    type: 'date',
                    disabled: true
                },
                {
                    label: this.$t('Overview.TargetQuantity'),
                    id: 'TargetQuantity',
                    value: ''
                },
                {
                    label: this.$t('Overview.CrewSize'),
                    id: 'CrewSize',
                    value: '',
                    type: 'input'
                },
                {
                    label: '',
                    id: 'IsUpdateLtxt',
                    value: false,
                    type: 'checkBox'
                }
            ],
            searchlist: [
                {
                    type: 'input',
                    name: this.$t('Overview.QuickSearch'),
                    id: 'QuickSearch',
                    value: ''
                },
                {
                    type: 'select',
                    option: [],
                    width: '20vh',
                    name: this.$t('DFM_JYXM.Segment'),
                    id: 'Segment',
                    value: ''
                },
                {
                    type: 'input',
                    value: '',
                    id: 'FillLineCode',
                    name: this.$t('$vuetify.dataTable.PRO_POLIST.FillLineCode')
                }
            ],
            chooseItem: {
                ProcessOrder: '',
                isResume: false,
                TargetQuantity: '',
                Unit1: '',
                MaterialCode: '',
                MaterialName: ''
            },
            tableList: [],
            Equipmentlist: [],
            AvailabletableId: 'PRO_POManagement',
            tableId: 'PRO_Overview',
            header: POManagemenOverview,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            pageOptions2: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            activeName: '1',
            activeName2: '',
            ActiveList: [],
            EquipmentGroupRowId: '',
            EquipmentId: '',
            RunEquipmentId: '',
            BatchId: '',
            ExecutionId: '',
            runningCode: '',
            isTippingscan: false,
            productionId: '',
            isEdit: false,
            IsDifferent: false,
            EquipmentCode: '',
            IsPack: '1',
            Text1: '',
            Text2: ''
        };
    },
    mounted() {
        this.viewtitle = JSON.parse(this.$route.query.query).Description;
        console.log(this.viewtitle);
        this.EquipmentGroupRowId = JSON.parse(this.$route.query.query).ID;
        // this.FiltersTableData();
        this.GetProcessOrderViewList();
        //this.GetProcessOrderView2();
        this.GetSegment();
        this.GetEquipment();
        this.changePagination();
    },
    methods: {
        async ProducedStart() {
            let flag = this.Startlist.some(item => {
                if (item.require) {
                    return item.value == '';
                }
            });
            if (this.MyEquipment == '') {
                flag = true;
            }
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'warning'
                });
                return;
            }
            let params = {
                SegmentId: this.chooseItem.SegmentId,
                ProductionOrderId: this.chooseItem.ProductionOrderId,
                PoSegmentRequirementId: this.chooseItem.ID,
                BatchId: '',
                LotCode: '',
                EquipmentId: this.MyEquipment,
                StartTime: '',
                ProductionDate: '',
                ExpirationDate: ''
            };
            if (this.chooseItem.isResume == true) {
                params.ExecutionId = this.chooseItem.ExecutionId;
            } else {
                params.ExecutionId = '';
            }
            this.Startlist.forEach(item => {
                if (item.id == 'LotCode') {
                    params[item.id] = item.value + item.value2 + item.value3;
                } else {
                    params[item.id] = item.value;
                }
            });
            params.ExpirationDate = moment(params.ExpirationDate).format('YYYY-MM-DD HH:mm:ss');
            if (params.LotCode.length > 10) {
                Message({
                    message: `${this.$t('Overview.BatchCodeLong')}`,
                    type: 'warning'
                });
                return;
            }
            let res;
            if (this.chooseItem.isResume == true) {
                res = await PoProducedResume(params);
            } else {
                res = await PoProducedStart(params);
            }
            //this.GetProcessOrderView2();
            this.loadProgress();
            this.StartModel = false;
            Message({
                message: res.msg,
                type: 'success'
            });
        },
        GetDate(id) {
            if (id == 'ProductionDate') {
                if (this.chooseItem.NeedQARelease == '1') {
                    this.Startlist[4].value = this.addDays(this.Startlist[3].value, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);
                } else {
                    this.Startlist[4].value = this.addDays(this.chooseItem.PlanStartTime, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);
                }
            }
        },
        startOrder(item) {
            // this.MyEquipment = '';
            // this.EquipmentCode = '';
            this.SegmentUnits(item);
            console.log(item.row);
            if (item.row) {
                this.IsPack = item.row.NeedQARelease;
                this.getLtext(item.row.ProductionOrderId);
                this.chooseItem = item.row;
                this.chooseItem.isResume = false;
            } else {
                this.chooseItem = item;
                this.chooseItem.isResume = true;
            }
            this.Startlist.forEach((item, index) => {
                if (index == 0) {
                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
                } else if (index == 3) {
                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
                } else if (index == 2) {
                    item.value = '';
                    item.value2 = '';
                    item.value3 = '';
                } else if (item.id == 'IsUpdateLtxt') {
                    item.value = false;
                } else {
                    item.value = '';
                }
            });
            this.GetDate('ProductionDate');
            this.getBatchList();
        },
        async SegmentUnits(item) {
            let res = await GetProcessOrderViewSegmentUnits('', item.row.Segment);
            this.MyEquipmentList = res.response;
        },
        async getBatchCode() {
            let date = moment(this.Startlist[3].value).format('YYYY-MM-DD HH:mm:ss');
            let p = {
                LineCode: this.Startlist[2].value,
                equipmentCode: this.EquipmentCode,
                productionDate: date,
                productionId: this.chooseItem.ProductionOrderId
            };
            let res = await GetBatchCode(p);
            if (res.response == null) {
                Message({
                    message: res.msg,
                    type: 'warning'
                });
            } else {
                this.Startlist[2].value = res.response.substring(0, 2);
                this.Startlist[2].value2 = res.response.substring(2, 5);
            }
        },
        async getBatchList() {
            let params = {
                PoSegmentRequirementId: this.chooseItem.ID
            };
            let res = await GetBBatchListView(params);
            this.Startlist[1].option = res.response;
            this.Startlist[1].value = this.Startlist[1].option[0].ID;
            this.StartModel = true;
        },
        addDays(date, number, interval) {
            const newDate1 = new Date(date);
            const newDate = new Date(newDate1.getFullYear(), newDate1.getMonth(), newDate1.getDate());
            this.DateAdd(interval, number, newDate);
            newDate.setDate(newDate.getDate() + 1); // 增加一天
            newDate.setSeconds(newDate.getSeconds() - 1); // 减去1秒
            return newDate;
        },
        DateAdd(interval, number, date) {
            switch (interval) {
                case 'Y': {
                    date.setFullYear(date.getFullYear() + number);
                    return date;
                }
                case 'Q': {
                    date.setMonth(date.getMonth() + number * 3);
                    return date;
                }
                case 'M': {
                    date.setMonth(date.getMonth() + number);
                    return date;
                }
                case 'W': {
                    date.setDate(date.getDate() + number * 7);
                    return date;
                }
                case 'D': {
                    date.setDate(date.getDate() + number);
                    return date;
                }
                case 'h': {
                    date.setHours(date.getHours() + number);
                    return date;
                }
                case 'm': {
                    date.setMinutes(date.getMinutes() + number);
                    return date;
                }
                case 's': {
                    date.setSeconds(date.getSeconds() + number);
                    return date;
                }
                default: {
                    date.setDate(date.getDate() + number);
                    return date;
                }
            }
        },
        async getMyEquipment() {
            this.MyGetRunOrder();
            this.MyEquipmentList.forEach(item => {
                if (item.ID == this.MyEquipment) {
                    this.EquipmentCode = item.EquipmentCode;
                }
            });
            this.getBatchCode();
        },
        async MyGetRunOrder() {
            let res = await GetRunOrder('', this.MyEquipment);
            this.runningCode = res.response;
            if (this.runningCode == null) {
                this.runningCode = '';
            }
        },
        async getLtext(id) {
            if (this.IsPack === '0') {
                this.IsDifferent = false;
                let params = {
                    id: id
                };
                let r = await GetCookOrderLtexts(params);
                if (r.response.length == 2) {
                    if (r.msg === '长文本不一致！') {
                        this.IsDifferent = true;
                    }
                    this.Text1 = r.response[0].ProcessData;
                    this.Text2 = r.response[1].ProcessData;
                }
            }
        },
        async GetSegment() {
            let params = {
                LineCode: this.viewtitle
            };
            let res = await GetProcessOrderViewSegments(params);
            // console.log(res,1432345345)
            let data = res.response;
            if (data.length != 0) {
                data.forEach((item, index) => {
                    if (index == 0) {
                        this.searchlist[1].value = item.key;
                    }
                });
                this.searchlist[1].option = data;
                this.ShowPOList = true;
                //this.GetProcessOrderView2();
            } else {
                this.ShowPOList = false;
            }
        },
        getsearch() {
            this.pageOptions2.page = 1;
            this.pageOptions2.pageSize = 20;
            this.GetProcessOrderView2();
        },
        getempty() {
            this.QuickSearch = '';
            this.timepicker = [];
            this.pageOptions2.page = 1;
            this.pageOptions2.pageSize = 20;
            this.searchlist.forEach(item => {
                item.value = '';
            });
            this.GetProcessOrderView2();
        },
        loadProgress(id) {
            this.GetEquipment();
            this.handleClick();
        },
        back() {
            this.$router.go(-1);
        },
        async GetProcessOrderView2() {
            // console.log('999999');
            if (this.timepicker == null) {
                this.timepicker = [];
            }
            let params = {
                Search: this.searchlist[0].value,
                LineCode: this.viewtitle,
                Segment: this.searchlist[1].value,
                FillLineCode: this.searchlist[2].value,
                pageIndex: this.pageOptions2.page,
                pageSize: this.pageOptions2.pageSize,
                StartTime: this.timepicker[0],
                EndTime: this.timepicker[1]
            };
            let res = await GetPoList(params);
            this.AvailablePOManagemenList = res.response.data;
            this.pageOptions2.total = res.response.dataCount;
            let el = document.getElementsByClassName(`el-pagination__total`);
            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions2.total}${this.$t('PAGINATION.TOTAL')}`;
            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');
            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));
            // this.getNumTofather();
        },
        async GetProcessOrderViewList() {
            console.log(12);
            let params = {
                EquipmentGroupRowId: this.EquipmentGroupRowId,
                pageIndex: this.pageOptions.page,
                pageSize: this.pageOptions.pageSize
            };
            let res = await GetEquipmentProcessOrderView(params);
            this.tableList = res.response.data;
            this.pageOptions.total = res.response.dataCount;
            let el = document.getElementsByClassName(`el-pagination__total`);
            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;
            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');
            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));
        },
        changePagination() {
            let el2 = document.getElementsByClassName(`el-select-dropdown__item`);
            for (let i = 0; i < el2.length; i++) {
                el2[i].innerHTML = el2[i].innerHTML.replace('条/页', this.$t('PAGINATION.MYPAGE'));
            }
        },
        async GetEquipment() {
            let params = {
                EquipmentGroupRowId: this.EquipmentGroupRowId,
                pageIndex: 1,
                pageSize: 1000
            };

            let res = await GetEquipmentFunctionView(params);
            if (res.response.length != 0) {
                res.response.forEach(item => {
                    item.functionlist = item.FunctionCodes.split(',');
                });
            }
            this.Equipmentlist = res.response;
        },
        async handleClick2(item, index) {
            if (this.activeName2 == 'Logsheets') {
                this.$refs[index + this.activeName2][0].activeName = '0';
            }
            console.log(this.ActiveList, 1);
            console.log(this.$refs[index + this.activeName2][0]);
            // this.GetEquipment();
            let params = {
                EquipmentGroupRowId: this.EquipmentGroupRowId,
                RunEquipmentId: this.EquipmentId,
                pageIndex: this.pageOptions.page,
                pageSize: this.pageOptions.pageSize
            };
            let res = await GetEquipmentProcessOrderView(params);
            console.log(res, 222444);
            if (res.response.data.length != 0) {
                let list = res.response.data;
                list.forEach(item => {
                    item.Material = item.MaterialName + '-' + item.MaterialCode;
                });
                this.ActiveList = list;
                this.BatchId = list[0].BatchId;
                this.RunEquipmentId = list[0].RunEquipmentId;
                this.ExecutionId = list[0].ExecutionId;
            } else {
                this.ActiveList = [];
                this.BatchId = '';
                this.RunEquipmentId = '';
                this.ExecutionId = '';
            }

            if (this.$refs[index + this.activeName2][0].getEquipmentModal) {
                this.$refs[index + this.activeName2][0].getEquipmentModal(item, this.ActiveList[0]);
            }
            if (this.$refs[index + this.activeName2][0].tabBeClick) {
                this.$refs[index + this.activeName2][0].tabBeClick(item);
            }
            console.log(this.ActiveList);
        },
        async handleClick(key) {
            if (key && Number(key.index) == 1) {
                this.GetProcessOrderView2();
            }
            console.log(this.Equipmentlist);
            // this.GetEquipment();
            this.EquipmentId = this.activeName;
            let params = {
                EquipmentGroupRowId: this.EquipmentGroupRowId,
                RunEquipmentId: this.EquipmentId,
                pageIndex: this.pageOptions.page,
                pageSize: this.pageOptions.pageSize
            };
            let res2 = await getCheckTippingType(JSON.stringify(this.EquipmentId));
            if (res2.response == 'true') {
                this.isTippingscan = true;
            } else {
                this.isTippingscan = false;
            }
            console.log('handleClick');
            let res = await GetEquipmentProcessOrderView(params);
            if (res.response.data.length != 0) {
                let list = res.response.data;
                list.forEach(item => {
                    item.Material = item.MaterialName + '-' + item.MaterialCode;
                });
                this.ActiveList = list;
                this.BatchId = list[0].BatchId;
                this.RunEquipmentId = list[0].RunEquipmentId;
                this.ExecutionId = list[0].ExecutionId;
            } else {
                this.ActiveList = [];
                this.BatchId = '';
                this.RunEquipmentId = '';
                this.ExecutionId = '';
            }
            if (key) {
                console.log(Number(key.index));
                let num = null;
                if (this.ShowPOList) {
                    num = Number(key.index) - 2;
                } else {
                    num = Number(key.index) - 1;
                }
                if (num >= 0) {
                    let functionlist = this.Equipmentlist[num].functionlist;
                    //console.log(this.Equipmentlist[num]);
                    console.log(functionlist);
                    this.activeName2 = functionlist[0];
                    functionlist.forEach(item => {
                        if (item == 'POManagement') {
                            this.activeName2 = 'POManagement';
                        }
                    });
                    if (this.$refs[num + this.activeName2][0].getEquipmentModal) {
                        this.$refs[num + this.activeName2][0].getEquipmentModal(this.Equipmentlist[num], this.ActiveList[0]);
                    }
                }
            }
        },
        handleSizeChange(val) {
            this.pageOptions.pageSize = val;
            this.GetProcessOrderViewList();
        },
        handleCurrentChange(val) {
            this.pageOptions.page = val;
            this.GetProcessOrderViewList();
        },
        handleSizeChange2(val) {
            this.pageOptions2.pageSize = val;
            this.GetProcessOrderView2();
        },
        handleCurrentChange2(val) {
            this.pageOptions2.page = val;
            this.GetProcessOrderView2();
        }
    }
};
</script>
<style lang="scss" scoped>
.overview {
    .searchboxtitle {
        font-size: 1.7vh;
        color: #767777;
        padding-bottom: 5px;
        margin-left: 10px;
    }

    .el-tabs {
        height: 94%;
    }
    .subtabsbox {
        .el-tabs--border-card {
            border: 0 !important;
            box-shadow: none !important;
        }
    }
    .paginationbox {
        height: 65px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .dialogdetailbox {
        display: flex;
        align-items: center;
        width: 100%;
        margin-top: 10px;
        .dialogdetailsinglelabel {
            font-weight: 600;
            width: 47%;
            text-align: right;
        }
        .dialogdetailsinglevalue {
            width: 78%;
            margin-left: 20px;
        }
    }
    .splitdetailbox {
        padding-bottom: 10px;
        border: 1px solid #e8e8e8;
        margin-bottom: 5px;
        .splitdetailboxtitle {
            background: #f5f5f5;
            height: 3.5vh;
            display: flex;
            align-items: center;
            padding-left: 5px;
            font-size: 1.1rem;
            color: #303133;
        }
        .detailsnote {
            background-color: #fdf6ec;
            border-color: #faecd8;
            color: #e6a23c;
            padding: 8px;
            font-size: 0.9rem;
            margin: 5px 10px 0px 10px;
        }
        .detailsnote2 {
            background-color: #fdf6ec;
            border-color: #faecd8;
            color: #e6a23c;
            padding: 8px;
            font-size: 1.2rem;
            margin: 5px 10px 0px 10px;
        }
        .detailsnote3 {
            background-color: #f5fdec;
            border-color: #faecd8;
            color: hsl(135, 55%, 44%);
            padding: 8px;
            font-size: 1.2rem;
            margin: 5px 10px 0px 10px;
        }
        .splitdetailboxtitleTag {
            margin-left: 5px;
            background: #5cb85c;
            color: #fff;
            border-color: #5cb85c;
        }
    }
}
//.el-dialog__body {
//    .el-input {
//        width: 250px !important;
//    }
//    .longwidthinput {
//        .el-input {
//            width: 400px !important;
//        }
//        .el-select {
//            width: 400px !important;
//        }
//    }
//    .el-select {
//        width: 250px !important;
//    }
//}
.code-diff-view{
  margin-top: 5px;
}
</style>
