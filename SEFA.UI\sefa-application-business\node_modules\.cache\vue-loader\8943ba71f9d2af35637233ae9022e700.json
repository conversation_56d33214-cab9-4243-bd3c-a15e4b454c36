{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\components\\POManagement.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\components\\POManagement.vue", "mtime": 1750254216282}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["POManagement.vue"], "names": [], "mappings": ";AA0aA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "POManagement.vue", "sourceRoot": "src/views/Producting/Overview/components", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle POManagement\">\r\n        <div class=\"subsubtabs\">\r\n            <el-tabs v-model=\"activeName\" type=\"border-card\" @tab-click=\"handleClick\">\r\n                <el-tab-pane :label=\"$t('Overview.AvailableOrders') + `(${Availablenum})`\" name=\"Available\">\r\n                    <div class=\"InventorySearchBox\">\r\n                        <div class=\"searchbox\">\r\n                            <div class=\"datebox\">\r\n                                <div class=\"datepickbox\">\r\n                                    <el-date-picker\r\n                                        v-model=\"timepicker\"\r\n                                        type=\"daterange\"\r\n                                        value-format=\"yyyy-MM-dd\"\r\n                                        range-separator=\"-\"\r\n                                        :start-placeholder=\"$t('DFM_RL._KSRQ')\"\r\n                                        :end-placeholder=\"$t('DFM_RL._JSRQ')\"\r\n                                    ></el-date-picker>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"inputformbox\" :style=\"{ width: item.width }\" v-for=\"(item, index) in searchlist\" :key=\"index\">\r\n                                <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\" :myid=\"item.id\" :placeholder=\"item.name\"></el-input>\r\n                                <el-select :style=\"{ width: item.width }\" v-model=\"item.value\" v-if=\"item.type == 'select'\" :myid=\"item.id\" :placeholder=\"item.name\">\r\n                                    <el-option v-for=\"(it, ind) in item.option\" :key=\"ind\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                                </el-select>\r\n                            </div>\r\n                            <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                            <el-button size=\"small\" style=\"margin-left: 5px\" icon=\"el-icon-s-help\" @click=\"getempty()\">{{ this.$t('GLOBAL._CZ') }}</el-button>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"tablebox\">\r\n                        <el-table border :data=\"AvailablePOManagemenList\" style=\"width: 100%\" height=\"520\">\r\n                            <el-table-column\r\n                                v-for=\"(item, index) in header\"\r\n                                :key=\"index\"\r\n                                :align=\"item.align\"\r\n                                :prop=\"item.prop ? item.prop : item.value\"\r\n                                :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                                :width=\"item.width\"\r\n                            >\r\n                                <template slot-scope=\"scope\">\r\n                                    <span v-if=\"scope.column.property == 'operate'\">\r\n                                        <el-button\r\n                                            size=\"mini\"\r\n                                            class=\"operatebtn\"\r\n                                            v-if=\"scope.row.ExecutionStatus == null || scope.row.ExecutionStatus == 3\"\r\n                                            @click=\"startOrder(scope)\"\r\n                                            icon=\"el-icon-video-play\"\r\n                                        >\r\n                                            {{ $t('Overview.start') }}\r\n                                        </el-button>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'PlanStartTime'\">{{ $dayjs(scope.row.PlanStartTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                                    <span v-else-if=\"scope.column.property == 'PlanEndTime'\">{{ $dayjs(scope.row.PlanEndTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                                    <span v-else-if=\"scope.column.property == 'Segment'\">\r\n                                        <div>{{ scope.row.SegmentCode }}</div>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'IsHavePreservative'\">\r\n                                        <i :class=\"scope.row[item.value] === '1' ? 'el-icon-star-on' : ''\"></i>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'LineNominalSpeed'\">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>\r\n                                    <span v-else>{{ scope.row[item.prop] }}</span>\r\n                                </template>\r\n                            </el-table-column>\r\n                        </el-table>\r\n                    </div>\r\n                </el-tab-pane>\r\n                <el-tab-pane :label=\"$t('Overview.ActiveOrders') + `(${Activenum})`\" name=\"Active\">\r\n                    <div class=\"InventorySearchBox\">\r\n                        <div class=\"searchbox\">\r\n                            <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                            <el-button\r\n                                class=\"tablebtn\"\r\n                                :disabled=\"tablechooselist > 0 ? false : true\"\r\n                                size=\"small\"\r\n                                @click=\"stopBtn()\"\r\n                                style=\"margin-left: 5px; width: 12vh\"\r\n                                icon=\"el-icon-circle-close\"\r\n                            >\r\n                                {{ this.$t('Overview.Stop') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <!-- <el-button class=\"tablebtn\" @click=\"holdBtn()\" :disabled=\"tablechooselist > 0 ? false : true\" size=\"small\" style=\"margin-left: 5px; width: 12vh\" icon=\"el-icon-video-pause\">\r\n                                {{ this.$t('Overview.Hold') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <el-button\r\n                                class=\"tablebtn\"\r\n                                @click=\"ResumeBtn()\"\r\n                                :disabled=\"tablechooselist > 0 ? false : true\"\r\n                                size=\"small\"\r\n                                style=\"margin-left: 5px; width: 14vh\"\r\n                                icon=\"el-icon-video-play\"\r\n                            >\r\n                                {{ this.$t('Overview.Resume') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button> -->\r\n                            <el-button class=\"tablebtn\" @click=\"updateBtn()\" :disabled=\"tablechooselist > 0 ? false : true\" size=\"small\" style=\"margin-left: 5px; width: 16vh\" icon=\"el-icon-setting\">\r\n                                {{ this.$t('Overview.UpdateOrder') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <el-button\r\n                                class=\"tablebtn\"\r\n                                @click=\"updateRemarkBtn()\"\r\n                                :disabled=\"tablechooselist > 0 ? false : true\"\r\n                                size=\"small\"\r\n                                style=\"margin-left: 5px; width: 16vh\"\r\n                                icon=\"el-icon-setting\"\r\n                            >\r\n                                {{ this.$t('Overview.UpdateRemark') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <!-- <el-button\r\n                                class=\"tablebtn\"\r\n                                @click=\"startNextBatchBtn()\"\r\n                                :disabled=\"tablechooselist > 0 && Number(selectTabelData.Number) < selectTabelData.BatchCount ? false : true\"\r\n                                size=\"small\"\r\n                                style=\"margin-left: 5px; width: 16vh\"\r\n                                icon=\"el-icon-setting\"\r\n                            >\r\n                                {{ this.$t('Overview.NextBatch') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <el-button class=\"tablebtn\" @click=\"AutoReport()\" :disabled=\"tablechooselist > 0 ? false : true\" size=\"small\" style=\"margin-left: 5px; width: 16vh\" icon=\"el-icon-setting\">\r\n                                {{ this.$t('Overview.AutoReport') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button> -->\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"tablebox\">\r\n                        <el-table border :data=\"ActivePOManagemenList\" ref=\"ActiveTable\" highlight-current-row @current-change=\"handleSelectionChange\" style=\"width: 100%\" height=\"520\">\r\n                            <!-- <el-table-column type=\"selection\" width=\"55\" fixed=\"left\"></el-table-column> -->\r\n                            <el-table-column\r\n                                v-for=\"(item, index) in Activeheader\"\r\n                                :key=\"index\"\r\n                                :align=\"item.align\"\r\n                                :prop=\"item.prop ? item.prop : item.value\"\r\n                                :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                                :width=\"item.width\"\r\n                            >\r\n                                <template slot-scope=\"scope\">\r\n                                    <span v-if=\"scope.column.property == 'ProcessOrder'\">\r\n                                        <div>{{ scope.row.ProcessOrder }}({{ scope.row.Number }})</div>\r\n                                    </span>\r\n                                    <!-- <span v-else-if=\"scope.column.property == 'Status'\">\r\n                                        {{ getExecutionhStatus(scope.row.Status) }}\r\n                                    </span> -->\r\n                                    <!-- <span v-else-if=\"scope.column.property == 'LineNominalSpeed'\">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>-->\r\n                                    <span v-else>{{ scope.row[item.prop] }}</span> \r\n                                </template>\r\n                            </el-table-column>\r\n                        </el-table>\r\n                    </div>\r\n                </el-tab-pane>\r\n\r\n                <el-tab-pane :label=\"$t('Overview.History')\" name=\"History\">\r\n                    <div class=\"InventorySearchBox\">\r\n                        <div class=\"searchbox\">\r\n                            <div class=\"datebox\">\r\n                                <div class=\"datepickbox\">\r\n                                    <el-date-picker\r\n                                        v-model=\"timepicker\"\r\n                                        type=\"daterange\"\r\n                                        value-format=\"yyyy-MM-dd\"\r\n                                        range-separator=\"-\"\r\n                                        :start-placeholder=\"$t('DFM_RL._KSRQ')\"\r\n                                        :end-placeholder=\"$t('DFM_RL._JSRQ')\"\r\n                                    ></el-date-picker>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"inputformbox\" :style=\"{ width: item.width }\" v-for=\"(item, index) in searchlist\" :key=\"index\">\r\n                                <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\" :myid=\"item.id\" :placeholder=\"item.name\"></el-input>\r\n                            </div>\r\n                            <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                            <el-button size=\"small\" style=\"margin-left: 5px\" icon=\"el-icon-s-help\" @click=\"getempty()\">{{ this.$t('GLOBAL._CZ') }}</el-button>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"tablebox\">\r\n                        <el-table border :data=\"HistroyList\" style=\"width: 100%\" height=\"520\">\r\n                            <el-table-column\r\n                                v-for=\"(item, index) in Historyheader\"\r\n                                :key=\"index\"\r\n                                :align=\"item.align\"\r\n                                :prop=\"item.prop ? item.prop : item.value\"\r\n                                :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                                :width=\"item.width\"\r\n                            >\r\n                                <template slot-scope=\"scope\">\r\n                                    <span v-if=\"scope.column.property == 'Material'\">\r\n                                        <div>{{ scope.row.MaterialCode }}</div>\r\n                                        <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'SAP'\">\r\n                                        <div>{{ scope.row.SegmentCode }}</div>\r\n                                        <div style=\"color: #808080\">{{ scope.row.SegmentName }}</div>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'Status'\">\r\n                                        {{ getExecutionhStatus(scope.row.Status) }}\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'NominalSpeed'\">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>\r\n                                    <span v-else>{{ scope.row[item.prop] }}</span>\r\n                                </template>\r\n                            </el-table-column>\r\n                        </el-table>\r\n                    </div>\r\n                </el-tab-pane>\r\n            </el-tabs>\r\n            <div class=\"paginationbox\">\r\n                <el-pagination\r\n                    @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageOptions.page\"\r\n                    :page-sizes=\"pageOptions.pageSizeitems\"\r\n                    :page-size=\"pageOptions.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next\"\r\n                    :total=\"pageOptions.total\"\r\n                    background\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Overview.StartOrder')\" id=\"Startdialog\" :visible.sync=\"StartModel\" :width=\"IsPack == '0' ? '650px' : '650px'\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">\r\n                    {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.StartOrder') }}\r\n                    <div class=\"dialogsubtitlebox\" style=\"display: inline\">{{ chooseItem.ProcessOrder }}</div>\r\n                </div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailboxtitle\">\r\n                    {{ chooseItem.MaterialCode }}-{{ chooseItem.MaterialName }}\r\n                    <el-tag class=\"splitdetailboxtitleTag\" size=\"small\" v-if=\"Activenum != 0\">{{ ActivePOManagemenList[0] ? ActivePOManagemenList[0].ProcessOrder : '' }}</el-tag>\r\n                </div>\r\n                <div class=\"detailsnote\" v-if=\"runningCode != '' && !chooseItem.isResume\">\r\n                    {{ $t('Overview.Note1') }}\r\n                    <span style=\"font-weight: 600\">{{ runningCode }}</span>\r\n                    {{ $t('Overview.Note2') }}\r\n                </div>\r\n                <div style=\"display: flex\">\r\n                    <div :style=\"{ width: IsPack == '0' ? '100%' : '100%' }\">\r\n                        <div class=\"dialogdetailbox\" v-for=\"(item, index) in Startlist\" :key=\"index\">\r\n                            <div class=\"dialogdetailsinglelabel\" :style=\"{ width: item.type == 'BatchCode' ? '20%' : '20%' }\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                            <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: item.type == 'BatchCode' || item.type == 'checkBox' ? '400px' : '77%' }\">\r\n                                <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                               \r\n                                <div v-else-if=\"item.type == 'BatchCode'\" style=\"display: flex\">\r\n                                    <el-input v-model=\"item.value\"></el-input>\r\n                                    <el-input v-model=\"item.value2\" disabled></el-input>\r\n                                    <el-input v-model=\"item.value3\"></el-input>\r\n                                    <el-button\r\n                                        class=\"tablebtn\"\r\n                                        @click=\"getBatchCode()\"\r\n                                        size=\"mini\"\r\n                                        style=\"margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff\"\r\n                                        icon=\"el-icon-refresh\"\r\n                                    ></el-button>\r\n                                </div>\r\n                                <el-select clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                                    <el-option v-for=\"it in item.option\" :key=\"it.ID\" :label=\"it.Number\" :value=\"it.ID\"></el-option>\r\n                                </el-select>\r\n                                <el-date-picker\r\n                                    @change=\"GetDate(item.id)\"\r\n                                    v-else-if=\"item.type == 'date'\"\r\n                                    value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                                    :disabled=\"item.disabled\"\r\n                                    v-model=\"item.value\"\r\n                                    type=\"datetime\"\r\n                                ></el-date-picker>\r\n                                <span v-else-if=\"item.id == 'TargetQuantity'\">{{ chooseItem.TargetQuantity }}{{ chooseItem.Unit1 }}</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button style=\"float: left\" v-if=\"chooseItem.isResume\">\r\n                    {{ $t('Overview.bottleneck') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" :disabled=\"IsDifferent\" icon=\"el-icon-video-play\" @click=\"ProducedStart()\">\r\n                    {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.Start') }}\r\n                </el-button>\r\n                <el-button @click=\"StartModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog id=\"Stopdialog\" :visible.sync=\"stopModel\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">\r\n                    {{ $t('Overview.StopNote') }}\r\n                </div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailboxtitle\">\r\n                    {{ selectTabelData != {} ? selectTabelData.MaterialName + '-' + selectTabelData.MaterialCode : '' }}\r\n                    <el-tag class=\"splitdetailboxtitleTag\" size=\"small\">{{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</el-tag>\r\n                </div>\r\n                <div class=\"dialogdetailbox\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ $t('Overview.EndTime') }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-date-picker disabled v-model=\"EndTime\" type=\"datetime\"></el-date-picker>\r\n                    </div>\r\n                </div>\r\n                <div v-if=\"isComplete == true && selectTabelData.NeedQARelease == '1'\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Completelist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <el-select @change=\"getData2(item)\" v-model=\"item.value\" clearable filterable :disabled=\"item.id == 'ProduceStatus'\" v-if=\"item.type == 'select'\">\r\n                                <el-option v-for=\"(it, ind) in item.options\" :key=\"ind\" :label=\"it.label\" :value=\"it.key\"></el-option>\r\n                            </el-select>\r\n                            <span v-else>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <div style=\"float: left\">\r\n                    <el-checkbox v-model=\"isComplete\">{{ $t('Overview.CompletePO') }}</el-checkbox>\r\n                </div>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-circle-close\" @click=\"StopProduced()\">\r\n                    {{ $t('Overview.Stop') }}\r\n                </el-button>\r\n                <el-button @click=\"stopModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog id=\"Holddialog\" :visible.sync=\"HoldModel\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">\r\n                    {{ $t('Overview.HoldNote') }}\r\n                </div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailboxtitle\">\r\n                    {{ selectTabelData != {} ? selectTabelData.MaterialName + '-' + selectTabelData.MaterialCode : '' }}\r\n                    <el-tag class=\"splitdetailboxtitleTag\" size=\"small\">{{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</el-tag>\r\n                </div>\r\n                <div class=\"dialogdetailbox\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ $t('Overview.EndTime') }} *</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-date-picker disabled v-model=\"EndTime\" type=\"datetime\"></el-date-picker>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <div style=\"float: left\">\r\n                    <el-checkbox v-model=\"isComplete\">{{ $t('Overview.CompletePO') }}</el-checkbox>\r\n                </div>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-video-pause\" @click=\"HoldProduced()\">\r\n                    {{ $t('Overview.Hold') }}\r\n                </el-button>\r\n                <el-button @click=\"HoldModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog id=\"Updatedialog\" :visible.sync=\"UpdateModel\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">{{ $t('Overview.UpdateNote') }} {{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Updatelist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <span>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Updateinputlist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: item.type == 'BatchCode' ? '20%' : '20%' }\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: item.type == 'BatchCode' ? '400px' : '77%' }\">\r\n                            <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n\r\n                            <div v-else-if=\"item.type == 'BatchCode'\" style=\"display: flex\">\r\n                                <el-input v-model=\"item.value\"></el-input>\r\n                                <el-input v-model=\"item.value2\" disabled></el-input>\r\n                                <el-input v-model=\"item.value3\"></el-input>\r\n                                <el-button\r\n                                    :disabled=\"Updateinputlist[0].value == ''\"\r\n                                    class=\"tablebtn\"\r\n                                    @click=\"getBatchCode2()\"\r\n                                    size=\"mini\"\r\n                                    style=\"margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff\"\r\n                                    icon=\"el-icon-refresh\"\r\n                                ></el-button>\r\n                            </div>\r\n                            <el-select clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                                <el-option v-for=\"it in item.option\" :key=\"it.ID\" :label=\"it.BatchCode\" :value=\"it.ID\"></el-option>\r\n                            </el-select>\r\n                            <el-date-picker v-else-if=\"item.type == 'date'\" :type=\"item.datetype\" v-model=\"item.value\"></el-date-picker>\r\n                            <!-- <el-date-picker @change=\"GetDate2(item.id)\" v-else-if=\"item.type == 'date'\" :type=\"item.datetype\" v-model=\"item.value\"></el-date-picker> -->\r\n                            <span v-else>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-setting\" @click=\"UpdateProduced()\">\r\n                    {{ $t('Overview.UpdateOrder') }}\r\n                </el-button>\r\n                <el-button @click=\"UpdateModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog id=\"Updatedialog2\" :visible.sync=\"UpdateRemark\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">{{ $t('Overview.UpdateNote') }} {{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Updatelist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <span>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" key=\"remark\">\r\n                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: '20%' }\">{{ $t('Overview.Comments') }}</div>\r\n                        <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: '77%' }\">\r\n                            <el-input v-model=\"Remark\"></el-input>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-setting\" @click=\"UpdateOrderRemark()\">\r\n                    {{ $t('Overview.UpdateRemark') }}\r\n                </el-button>\r\n                <el-button @click=\"UpdateRemark = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { POManagemenAvailable, POManagemenActive, POManagemenHistory } from '@/columns/factoryPlant/tableHeaders';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { GetDataTreeList, GetDataItemList } from '@/api/factoryPlant/process.js';\r\nimport {\r\n    GetProcessOrderView,\r\n    GetProcessOrderView2,\r\n    GetBBatchListView,\r\n    GetBatchCode,\r\n    PoProducedStart,\r\n    PoProducedStop,\r\n    PoProducedHold,\r\n    PoProducedResume,\r\n    PoProducedUpdatePo,\r\n    AutoReport,\r\n    UpdateOrderRemark,\r\n    PoExecutionHistroy,\r\n    GetRunOrder,\r\n    GetEquipmentProcessOrderView,\r\n    StartNextBatch\r\n} from '@/api/Inventory/Overview.js';\r\nimport moment from 'moment';\r\nimport { ConsoleLogger } from '@microsoft/signalr/dist/esm/Utils';\r\n\r\nexport default {\r\n    name: 'POManagement',\r\n\r\n    data() {\r\n        return {\r\n            Completelist: [\r\n                {\r\n                    label: this.$t('POList.PlanQty'),\r\n                    id: 'PlanQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.ActualQty'),\r\n                    id: 'ActualQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.ProduceStatus'),\r\n                    id: 'ProduceStatus',\r\n                    require: true,\r\n                    type: 'select',\r\n                    options: [\r\n                        {\r\n                            key: 'NotComplete',\r\n                            label: this.$t('POList.NotComplete')\r\n                        },\r\n                        {\r\n                            key: 'OverComplete',\r\n                            label: this.$t('POList.OverComplete')\r\n                        },\r\n                        {\r\n                            key: 'CompleteAtOnce',\r\n                            label: this.$t('POList.CompleteAtOnce')\r\n                        }\r\n                    ],\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.Reason'),\r\n                    id: 'Reason',\r\n                    type: 'select',\r\n                    require: true,\r\n                    options: [],\r\n                    value: ''\r\n                }\r\n            ],\r\n            viewtitle: '',\r\n            timepicker: [moment(new Date()).format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')],\r\n            tableId: 'PRO_POManagement',\r\n            runningCode: '',\r\n            header: POManagemenAvailable,\r\n            Activeheader: POManagemenActive,\r\n            Historyheader: POManagemenHistory,\r\n            ActivePOManagemenList: [],\r\n            Activenum: 0,\r\n            AvailablePOManagemenList: [],\r\n            Availablenum: 0,\r\n            HistroyList: [],\r\n            Histroynum: 0,\r\n            searchlist: [\r\n                {\r\n                    type: 'input',\r\n                    name: this.$t('Overview.QuickSearch'),\r\n                    id: 'QuickSearch',\r\n                    value: ''\r\n                }\r\n            ],\r\n            pageOptions: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            activeName: 'Available',\r\n            StartModel: false,\r\n            Startlist: [\r\n                {\r\n                    label: this.$t('Overview.StartTime'),\r\n                    id: 'StartTime',\r\n                    value: '',\r\n                    disabled: true,\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.Batch'),\r\n                    id: 'BatchId',\r\n                    value: '',\r\n                    require: true,\r\n                    type: 'select',\r\n                    option: []\r\n                },\r\n                {\r\n                    label: this.$t('Overview.BatchCode'),\r\n                    id: 'LotCode',\r\n                    value: '',\r\n                    value2: '',\r\n                    value3: '',\r\n                    require: true,\r\n                    type: 'BatchCode'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ExpirationDate'),\r\n                    id: 'ExpirationDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date',\r\n                    disabled: true\r\n                },\r\n                {\r\n                    label: this.$t('Overview.TargetQuantity'),\r\n                    id: 'TargetQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Overview.CrewSize'),\r\n                    id: 'CrewSize',\r\n                    value: '',\r\n                    type: 'input'\r\n                },\r\n                {\r\n                    label: '',\r\n                    id: 'IsUpdateLtxt',\r\n                    value: false,\r\n                    type: 'checkBox'\r\n                }\r\n            ],\r\n            chooseItem: {\r\n                ProcessOrder: '',\r\n                isResume: false,\r\n                TargetQuantity: '',\r\n                Unit1: '',\r\n                MaterialCode: '',\r\n                MaterialName: ''\r\n            },\r\n            tablechooselist: 0,\r\n            selectTabelData: {},\r\n            stopModel: false,\r\n            EndTime: new Date(),\r\n            isComplete: false,\r\n            HoldModel: false,\r\n            UpdateModel: false,\r\n            UpdateRemark: false,\r\n            Updatelist: [\r\n                {\r\n                    label: this.$t('Overview.Material'),\r\n                    value: '',\r\n                    id: 'Material'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.TargetQuantity'),\r\n                    value: '',\r\n                    id: 'TargetQuantity'\r\n                }\r\n            ],\r\n            Updateinputlist: [\r\n                {\r\n                    label: this.$t('Overview.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    value: '',\r\n                    require: true,\r\n                    type: 'date',\r\n                    datetype: 'datetime'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.BatchCode'),\r\n                    id: 'LotCode',\r\n                    value: '',\r\n                    value2: '',\r\n                    value3: '',\r\n                    require: true,\r\n                    type: 'BatchCode'\r\n                },\r\n                // {\r\n                //     label: this.$t('Overview.DefaultBatchCode'),\r\n                //     id: 'DefaultBatchCode',\r\n                //     value: ''\r\n                // },\r\n\r\n                {\r\n                    label: this.$t('Overview.ExpirationDate'),\r\n                    id: 'ExpirationDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date',\r\n                    datetype: 'datetime'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.Comments'),\r\n                    id: 'Comments',\r\n                    require: false,\r\n                    value: '',\r\n                    type: 'input'\r\n                }\r\n            ],\r\n            Remark: '',\r\n            ExecutionhStatus: [],\r\n            EquipmentId: '',\r\n            EquipmentCode: '',\r\n            IsPack: '1',\r\n            Text1: '',\r\n            Text2: '',\r\n            productionId: '',\r\n            isEdit: false,\r\n            IsDifferent: false,\r\n            ReasonList: []\r\n        };\r\n    },\r\n    methods: {\r\n        getEquipmentModal(item, Equipmentitem) {\r\n            this.productionId = item.ProductionOrderId;\r\n            this.EquipmentId = item.ID;\r\n            this.EquipmentCode = item.EquipmentCode;\r\n            // if (Equipmentitem) {\r\n            // } else {\r\n            //     // this.EquipmentId = '';\r\n            //     // this.EquipmentCode = '';\r\n            // }\r\n            this.getStatus();\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        },\r\n        async getStatus() {\r\n            this.ReasonList = [];\r\n            this.Completelist[2].options.forEach(async item => {\r\n                let p = {\r\n                    ItemCode: item.key\r\n                };\r\n                const res = await GetDataTreeList(p);\r\n                res.response.data.forEach(item1 => {\r\n                    this.ReasonList.push(item1);\r\n                });\r\n            });\r\n        },\r\n        getReasonName(key1, key2) {\r\n            if (key1 === null || key1 === '' || key2 === null || key2 === '') {\r\n                return '';\r\n            }\r\n            let name = key2;\r\n            //console.log(this._i18n.locale);\r\n            if (this._i18n.locale == 'en') {\r\n                return name;\r\n            }\r\n            this.ReasonList.forEach(item => {\r\n                if (item.ItemCode === key1 && item.ItemValue === key2) {\r\n                    name = item.ItemName;\r\n                }\r\n            });\r\n            //console.log(name);\r\n            return name;\r\n        },\r\n        calculateDate(days) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + days);\r\n            return this.formatDate(date);\r\n        },\r\n        async getStauts() {\r\n            let params2 = {\r\n                ItemCode: 'ProductionExecutionStatus'\r\n            };\r\n            const res2 = await GetDataItemList(params2);\r\n            let data2 = res2.response;\r\n            this.ExecutionhStatus = data2;\r\n        },\r\n        getExecutionhStatus(val) {\r\n            if (val) {\r\n                let name = '';\r\n                this.ExecutionhStatus.forEach(item => {\r\n                    if (item.ItemValue == val) {\r\n                        name = item.ItemName;\r\n                    }\r\n                });\r\n                return name;\r\n            }\r\n        },\r\n        getsearch() {\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.getStauts();\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        },\r\n        getempty() {\r\n            this.QuickSearch = '';\r\n            this.timepicker = [];\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.searchlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        },\r\n        async getPoExecutionHistroy() {\r\n            if (this.timepicker == null) {\r\n                this.timepicker = [];\r\n            }\r\n            let params = {\r\n                Key: this.searchlist[0].value,\r\n                RunEquipmentId: this.EquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize,\r\n                StartTime: this.timepicker[0],\r\n                EndTime: this.timepicker[1] == undefined ? '' : this.timepicker[1] + ' 23:59:59'\r\n            };\r\n            let res = await PoExecutionHistroy(params);\r\n            this.HistroyList = res.response.data;\r\n            this.Histroynum = res.response.dataCount;\r\n            if (this.activeName == 'History') {\r\n                this.pageOptions.total = this.Histroynum;\r\n            }\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n        },\r\n        async getBatchList() {\r\n            let params = {\r\n                PoSegmentRequirementId: this.chooseItem.ID\r\n            };\r\n            let res = await GetBBatchListView(params);\r\n            this.Startlist[1].option = res.response;\r\n            this.Startlist[1].value = this.Startlist[1].option[0].ID;\r\n            this.StartModel = true;\r\n        },\r\n        async getData2(item) {\r\n            if (item.id == 'ProduceStatus') {\r\n                if (item.value != '') {\r\n                    let res = await this.$getNewDataDictionary(item.value);\r\n                    //let res = this.ReasonList.find(x=>x.ItemCode = item.value)\r\n                    // console.log(res);\r\n                    let data = res;\r\n                    if (data.length > 0) {\r\n                        data.forEach(item1 => {\r\n                            item1.key = item1.ItemValue;\r\n                            // console.log(this._i18n.locale);\r\n                            item1.label = this._i18n.locale === 'en' ? item1.ItemValue : item1.ItemName;\r\n                        });\r\n                    }\r\n                    this.Completelist[3].options = data;\r\n                    this.Completelist[3].value = '';\r\n                }\r\n            }\r\n        },\r\n        async getLtext(id) {\r\n            if (this.IsPack === '0') {\r\n                this.IsDifferent = false;\r\n                let params = {\r\n                    id: id\r\n                };\r\n                // let r = await GetCookOrderLtexts(params);\r\n                // if (r.response.length == 2) {\r\n                //     if (r.msg === '长文本不一致！') {\r\n                //         this.IsDifferent = true;\r\n                //     }\r\n                //     this.Text1 = r.response[0].ProcessData;\r\n                //     this.Text2 = r.response[1].ProcessData;\r\n                // }\r\n            }\r\n        },\r\n        async getBatchCode() {\r\n            let date = moment(this.Startlist[3].value).format('YYYY-MM-DD HH:mm:ss');\r\n            let p = {\r\n                LineCode: this.Startlist[2].value,\r\n                equipmentCode: this.EquipmentCode,\r\n                productionDate: date,\r\n                productionId: this.chooseItem.ProductionOrderId\r\n            };\r\n            let res = await GetBatchCode(p);\r\n            if (res.response == null) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'warning'\r\n                });\r\n            } else {\r\n                this.Startlist[2].value = res.response.substring(0, 2);\r\n                this.Startlist[2].value2 = res.response.substring(2, 5);\r\n            }\r\n        },\r\n        async getBatchCode2() {\r\n            let date = moment(this.Updateinputlist[0].value).format('YYYY-MM-DD HH:mm:ss');\r\n            let p = {\r\n                LineCode: this.Updateinputlist[1].value,\r\n                equipmentCode: this.EquipmentCode,\r\n                productionDate: date,\r\n                productionId: this.selectTabelData.ProductionOrderId\r\n            };\r\n            let res = await GetBatchCode(p);\r\n            if (res.response == null) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'warning'\r\n                });\r\n            } else {\r\n                this.Updateinputlist[1].value = res.response.substring(0, 2);\r\n                this.Updateinputlist[1].value2 = res.response.substring(2, 5);\r\n            }\r\n        },\r\n        async GetProcessOrderView() {\r\n            if (this.timepicker == null) {\r\n                this.timepicker = [];\r\n            }\r\n            let params = {\r\n                Search: this.searchlist[0].value,\r\n                EquipmentId: this.EquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize,\r\n                StartTime: this.timepicker[0],\r\n                EndTime: this.timepicker[1] == undefined ? '' : this.timepicker[1] + ' 23:59:59'\r\n            };\r\n            let res = await GetProcessOrderView(params);\r\n            this.AvailablePOManagemenList = res.response.data;\r\n            this.Availablenum = res.response.dataCount;\r\n            let params2 = {\r\n                Search: this.searchlist[0].value,\r\n                EquipmentId: this.EquipmentId,\r\n                ExecutionStatus: ['1', '5'],\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res2 = await GetProcessOrderView2(params2);\r\n            this.ActivePOManagemenList = res2.response.data;\r\n            this.Activenum = res2.response.dataCount;\r\n            if (this.activeName == 'Active') {\r\n                this.pageOptions.total = this.Activenum;\r\n            } else if (this.activeName == 'Available') {\r\n                this.pageOptions.total = this.Availablenum;\r\n            }\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            // this.getNumTofather();\r\n        },\r\n        // getNumTofather() {\r\n        //     this.$emit('getNum', this.ActivePOManagemenList);\r\n        // },\r\n        changePagination() {\r\n            let el2 = document.getElementsByClassName(`el-select-dropdown__item`);\r\n            for (let i = 0; i < el2.length; i++) {\r\n                el2[i].innerHTML = el2[i].innerHTML.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            }\r\n        },\r\n        async AutoReport() {\r\n            let res = await AutoReport('', 'reportType=Consume&&equipmentCode=' + this.selectTabelData.EquipmentId);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        async UpdateProduced() {\r\n            let flag = this.Updateinputlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                ExecutionId: this.selectTabelData.ExecutionId,\r\n                LotCode: '',\r\n                ProductionDate: '',\r\n                ExpirationDate: ''\r\n            };\r\n            this.Updateinputlist.forEach(item => {\r\n                if (item.id == 'LotCode') {\r\n                    params[item.id] = item.value + item.value2 + item.value3;\r\n                } else {\r\n                    params[item.id] = item.value;\r\n                }\r\n            });\r\n            params.ExpirationDate = moment(params.ExpirationDate).format('YYYY-MM-DD HH:mm:ss');\r\n            if (params.LotCode.length > 10) {\r\n                Message({\r\n                    message: `${this.$t('Overview.BatchCodeLong')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let res = await PoProducedUpdatePo(params);\r\n            this.GetProcessOrderView();\r\n            this.UpdateModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n\r\n        async UpdateOrderRemark() {\r\n            let params = {\r\n                ID: this.selectTabelData.ProductionOrderId,\r\n                Remark: this.Remark\r\n            };\r\n            let res = await UpdateOrderRemark(params);\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n            this.UpdateRemark = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        async HoldProduced() {\r\n            if (this.EndTime == null || this.EndTime == '') {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                ExecutionId: this.selectTabelData.ExecutionId,\r\n                EndTime: this.EndTime,\r\n                IsComplete: this.isComplete\r\n            };\r\n            let res = await PoProducedHold(params);\r\n            this.GetProcessOrderView();\r\n            this.HoldModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        ResumeBtn() {\r\n            this.startOrder(this.selectTabelData);\r\n        },\r\n        async StopProduced() {\r\n            if (this.EndTime == null || this.EndTime == '') {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            if (this.isComplete == true && this.selectTabelData.NeedQARelease == '1') {\r\n                let flag = this.Completelist.some(item => {\r\n                    if (item.require) {\r\n                        return item.value == '' || item.value == null;\r\n                    }\r\n                });\r\n                if (flag) {\r\n                    Message({\r\n                        message: `${this.$t('Inventory.ToOver')}`,\r\n                        type: 'warning'\r\n                    });\r\n                    return;\r\n                }\r\n            }\r\n            let params = {\r\n                ExecutionId: this.selectTabelData.ExecutionId,\r\n                EndTime: this.EndTime,\r\n                IsComplete: this.isComplete,\r\n                ProduceStatus: this.Completelist[2].value,\r\n                Reason: this.Completelist[3].value\r\n            };\r\n            let res = await PoProducedStop(params);\r\n            this.GetProcessOrderView();\r\n            this.$emit('loadProgress', this.EquipmentId);\r\n            this.stopModel = false;\r\n            if (res.msg.includes('需要取样')) {\r\n                MessageBox.alert(`${res.msg}`, '提示!', {\r\n                    confirmButtonText: '确定',\r\n                    callback: action => {}\r\n                });\r\n            } else {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            }\r\n        },\r\n        stopBtn() {\r\n            this.selectTabelData.Material = this.selectTabelData.MaterialName + '-' + this.selectTabelData.MaterialCode;\r\n            this.selectTabelData.PlanQuantity = this.selectTabelData.TargetQuantity + this.selectTabelData.Unit1;\r\n            this.selectTabelData.ActualQuantity = this.selectTabelData.ActualQty + this.selectTabelData.Unit1;\r\n            this.Completelist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            //console.log(this.Completelist[4]);\r\n            this.Completelist.forEach(item => {\r\n                for (let k in this.selectTabelData) {\r\n                    if (item.id == k) {\r\n                        item.value = this.selectTabelData[k];\r\n                    }\r\n                }\r\n            });\r\n            this.Completelist[3].options = [];\r\n            let PlanQuantity = Number(this.selectTabelData.TargetQuantity);\r\n            let ActualQuantity = Number(this.selectTabelData.ActualQty);\r\n            if (PlanQuantity > ActualQuantity) {\r\n                this.Completelist[2].value = 'NotComplete';\r\n            } else if (PlanQuantity == ActualQuantity) {\r\n                this.Completelist[2].value = 'CompleteAtOnce';\r\n            } else {\r\n                this.Completelist[2].value = 'OverComplete';\r\n            }\r\n            this.ReasonList.forEach(x => {\r\n                if (x.ItemCode == this.Completelist[2].value) {\r\n                    let res = this.Completelist[3].options.find(x1 => x1.ItemCode === this.Completelist[3].value && x1.ItemValue === x.ItemValue);\r\n                    if (res === null || res === undefined || typeof res === 'undefined') {\r\n                        x.key = x.ItemValue;\r\n                        x.label = this._i18n.locale === 'en' ? x.ItemValue : x.ItemName;\r\n                        this.Completelist[3].options.push(x);\r\n                    }\r\n                }\r\n            });\r\n            this.EndTime = new Date();\r\n            this.stopModel = true;\r\n        },\r\n        holdBtn() {\r\n            this.EndTime = new Date();\r\n            this.HoldModel = true;\r\n        },\r\n        updateBtn() {\r\n            this.Updateinputlist.forEach(item => {\r\n                item.value = '';\r\n                if (item.id == 'LotCode') {\r\n                    item.value2 = '';\r\n                    item.value3 = '';\r\n                }\r\n            });\r\n            this.Updatelist.forEach(item => {\r\n                item.value = this.selectTabelData[item.id];\r\n            });\r\n            this.Updatelist[1].value += this.selectTabelData.Unit1;\r\n            this.Updateinputlist[0].value = this.selectTabelData.StartTime;\r\n            //this.Updateinputlist[1].value2 = this.selectTabelData.BatchCode;\r\n            this.Updateinputlist[2].value = this.selectTabelData.ExpirationDate;\r\n            this.getBatchCode2();\r\n            this.UpdateModel = true;\r\n        },\r\n        updateRemarkBtn() {\r\n            this.Updatelist.forEach(item => {\r\n                item.value = this.selectTabelData[item.id];\r\n            });\r\n            this.Updatelist[1].value += this.selectTabelData.Unit1;\r\n            this.Remark = this.selectTabelData.Remark;\r\n            this.UpdateRemark = true;\r\n        },\r\n        async startNextBatchBtn() {\r\n            let params = {\r\n                ExecutionId: this.selectTabelData.ExecutionId,\r\n                Number: this.selectTabelData.Number,\r\n                EquipmentId: this.selectTabelData.EquipmentId\r\n            };\r\n            let res = await StartNextBatch(params);\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n            if (res.msg.includes('需要取样')) {\r\n                MessageBox.alert(`${res.msg}`, '提示!', {\r\n                    confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                    callback: action => {}\r\n                });\r\n            } else {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            }\r\n            // Message({\r\n            //     message: res.msg,\r\n            //     type: 'success'\r\n            // });\r\n        },\r\n        handleClick(key) {\r\n            this.QuickSearch = '';\r\n            this.timepicker = [];\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.$refs.ActiveTable.setCurrentRow(null);\r\n            this.searchlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            if (this.activeName == 'Active') {\r\n                this.pageOptions.total = this.Activenum;\r\n            } else if (this.activeName == 'Available') {\r\n                this.pageOptions.total = this.Availablenum;\r\n            } else {\r\n                this.pageOptions.total = this.Histroynum;\r\n            }\r\n        },\r\n        GetDate(id) {\r\n            if (id == 'ProductionDate') {\r\n                if (this.chooseItem.NeedQARelease == '1') {\r\n                    this.Startlist[4].value = this.addDays(this.Startlist[3].value, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);\r\n                } else {\r\n                    this.Startlist[4].value = this.addDays(this.chooseItem.PlanStartTime, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);\r\n                }\r\n            }\r\n        },\r\n        GetDate2(id) {\r\n            if (id == 'ProductionDate') {\r\n                if (this.selectTabelData.NeedQARelease == '1') {\r\n                    this.Updateinputlist[2].value = this.addDays(this.Updateinputlist[0].value, this.selectTabelData.Mhdhb, this.selectTabelData.Iprkz);\r\n                } else {\r\n                    this.Updateinputlist[2].value = this.addDays(this.selectTabelData.PlanStartTime, this.selectTabelData.Mhdhb, this.selectTabelData.Iprkz);\r\n                }\r\n            }\r\n        },\r\n        addDays(date, number, interval) {\r\n            const newDate1 = new Date(date);\r\n            const newDate = new Date(newDate1.getFullYear(), newDate1.getMonth(), newDate1.getDate());\r\n            this.DateAdd(interval, number, newDate);\r\n            newDate.setDate(newDate.getDate() + 1); // 增加一天\r\n            newDate.setSeconds(newDate.getSeconds() - 1); // 减去1秒\r\n            return newDate;\r\n        },\r\n        DateAdd(interval, number, date) {\r\n            switch (interval) {\r\n                case 'Y': {\r\n                    date.setFullYear(date.getFullYear() + number);\r\n                    return date;\r\n                }\r\n                case 'Q': {\r\n                    date.setMonth(date.getMonth() + number * 3);\r\n                    return date;\r\n                }\r\n                case 'M': {\r\n                    date.setMonth(date.getMonth() + number);\r\n                    return date;\r\n                }\r\n                case 'W': {\r\n                    date.setDate(date.getDate() + number * 7);\r\n                    return date;\r\n                }\r\n                case 'D': {\r\n                    date.setDate(date.getDate() + number);\r\n                    return date;\r\n                }\r\n                case 'h': {\r\n                    date.setHours(date.getHours() + number);\r\n                    return date;\r\n                }\r\n                case 'm': {\r\n                    date.setMinutes(date.getMinutes() + number);\r\n                    return date;\r\n                }\r\n                case 's': {\r\n                    date.setSeconds(date.getSeconds() + number);\r\n                    return date;\r\n                }\r\n                default: {\r\n                    date.setDate(date.getDate() + number);\r\n                    return date;\r\n                }\r\n            }\r\n        },\r\n        startOrder(item) {\r\n            if (item.row) {\r\n                this.IsPack = item.row.NeedQARelease;\r\n                //this.getLtext(item.row.ProductionOrderId);\r\n                this.chooseItem = item.row;\r\n                this.chooseItem.isResume = false;\r\n            } else {\r\n                this.chooseItem = item;\r\n                this.chooseItem.isResume = true;\r\n            }\r\n            this.MyGetRunOrder();\r\n            this.Startlist.forEach((item, index) => {\r\n                if (index == 0) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 3) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 2) {\r\n                    item.value = '';\r\n                    item.value2 = '';\r\n                    item.value3 = '';\r\n                } else if (item.id == 'IsUpdateLtxt') {\r\n                    item.value = false;\r\n                } else {\r\n                    item.value = '';\r\n                }\r\n            });\r\n            this.GetDate('ProductionDate');\r\n            this.getBatchList();\r\n            //this.getBatchCode();\r\n        },\r\n        async MyGetRunOrder() {\r\n            let res = await GetRunOrder('', this.EquipmentId);\r\n            this.runningCode = res.response;\r\n            if (this.runningCode == null) {\r\n                this.runningCode = '';\r\n            }\r\n        },\r\n        async ProducedStart() {\r\n            if (this.IsDifferent == true) {\r\n                this.$message.warning('工艺长文本对比不通过,不允许启动工单！');\r\n                return;\r\n            }\r\n            let flag = this.Startlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                SegmentId: this.chooseItem.SegmentId,\r\n                ProductionOrderId: this.chooseItem.ProductionOrderId,\r\n                PoSegmentRequirementId: this.chooseItem.ID,\r\n                BatchId: '',\r\n                LotCode: '',\r\n                EquipmentId: this.EquipmentId,\r\n                StartTime: '',\r\n                ProductionDate: '',\r\n                ExpirationDate: ''\r\n            };\r\n            if (this.chooseItem.isResume == true) {\r\n                params.ExecutionId = this.chooseItem.ExecutionId;\r\n            } else {\r\n                params.ExecutionId = '';\r\n            }\r\n            this.Startlist.forEach(item => {\r\n                if (item.id == 'LotCode') {\r\n                    params[item.id] = item.value + item.value2 + item.value3;\r\n                } else {\r\n                    params[item.id] = item.value;\r\n                }\r\n            });\r\n            //params.ExpirationDate = moment(params.ExpirationDate).format('YYYY-MM-DD HH:mm:ss');\r\n            // if (params.LotCode.length > 10) {\r\n            //     Message({\r\n            //         message: `${this.$t('Overview.BatchCodeLong')}`,\r\n            //         type: 'warning'\r\n            //     });\r\n            //     return;\r\n            // }\r\n            let res;\r\n            if (this.chooseItem.isResume == true) {\r\n                res = await PoProducedResume(params);\r\n            } else {\r\n                res = await PoProducedStart(params);\r\n            }\r\n            this.GetProcessOrderView();\r\n            this.$emit('loadProgress', this.EquipmentId);\r\n            this.StartModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        handleSelectionChange(val) {\r\n            if (val != null) {\r\n                this.selectTabelData = val;\r\n                this.selectTabelData.Material = this.selectTabelData.MaterialName + '-' + this.selectTabelData.MaterialCode;\r\n                this.tablechooselist = 1;\r\n            } else {\r\n                this.selectTabelData = {};\r\n                this.tablechooselist = 0;\r\n            }\r\n        },\r\n        handleSizeChange(val) {\r\n            this.pageOptions.pageSize = val;\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.pageOptions.page = val;\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.POManagement {\r\n    .searchboxtitle {\r\n        font-size: 1.7vh;\r\n        color: #767777;\r\n        padding-bottom: 5px;\r\n        margin-left: 10px;\r\n    }\r\n    .el-tabs {\r\n        height: 97%;\r\n    }\r\n    .subsubtabs {\r\n        .el-tabs--border-card {\r\n            border: 0 !important;\r\n            box-shadow: none !important;\r\n        }\r\n    }\r\n    .paginationbox {\r\n        height: 10vh;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin-top: 10px;\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 47%;\r\n            text-align: right;\r\n        }\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n    .splitdetailbox {\r\n        padding-bottom: 10px;\r\n        border: 1px solid #e8e8e8;\r\n        margin-bottom: 5px;\r\n        .splitdetailboxtitle {\r\n            background: #f5f5f5;\r\n            height: 3.5vh;\r\n            display: flex;\r\n            align-items: center;\r\n            padding-left: 5px;\r\n            font-size: 1.1rem;\r\n            color: #303133;\r\n        }\r\n        .detailsnote {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 0.9rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .detailsnote2 {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 1.2rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .detailsnote3 {\r\n            background-color: #f5fdec;\r\n            border-color: #faecd8;\r\n            color: hsl(135, 55%, 44%);\r\n            padding: 8px;\r\n            font-size: 1.2rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .splitdetailboxtitleTag {\r\n            margin-left: 5px;\r\n            background: #5cb85c;\r\n            color: #fff;\r\n            border-color: #5cb85c;\r\n        }\r\n    }\r\n}\r\n.el-dialog__body {\r\n    .el-input {\r\n        width: 250px !important;\r\n    }\r\n    .longwidthinput {\r\n        .el-input {\r\n            width: 400px !important;\r\n        }\r\n        .el-select {\r\n            width: 400px !important;\r\n        }\r\n    }\r\n    .el-select {\r\n        width: 250px !important;\r\n    }\r\n}\r\n</style>\r\n"]}]}