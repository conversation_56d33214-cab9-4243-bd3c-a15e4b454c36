<template>
  <el-dialog :title="dialogForm.ID ? '编辑' : '新增'" :visible.sync="dialogVisible" width="600px"
    :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
    @close="dialogVisible = false">
    <el-form :rules="rules" ref="dialogForm" :model="dialogForm" label-width="80px">
      <el-form-item label="工段编码" prop="SegmentCode">
        <el-input v-model="dialogForm.SegmentCode" :maxlength="20" placeholder="" />
      </el-form-item>
      <el-form-item label="工段名称" prop="SegmentName">
        <el-input v-model="dialogForm.SegmentName" :maxlength="20" placeholder="" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogVisible = false">取 消</el-button>
      <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small"
        @click="submit()">确定
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { saveOperation } from '@/api/productionManagement/Formula';

export default {
  name: 'add',
  data() {
    return {
      dialogForm: {},
      dialogVisible: false,
      formLoading: false,
      rules: {
        SegmentCode: [
          { required: true, message: '请输入工段编码', trigger: 'blur' },
        ],
        SegmentName: [
          { required: true, message: '请输入工段名称', trigger: 'blur' },
        ]
      }
    }
  },
  mounted() {
  },
  methods: {
    submit() {
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          saveOperation(this.dialogForm).then(res => {
            this.$message.success(res.msg)
            this.$emit('saveForm')
            this.dialogVisible = false
          })
        }
      });
    },
    show(data) {
      console.log(data);
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.dialogForm = {
          ...data
        }
        this.$refs.dialogForm.resetFields()
      })
    }
  }
}
</script>
