{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\factoryPlant\\physicalModelNew\\components\\physicalModelDialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\factoryPlant\\physicalModelNew\\components\\physicalModelDialog.vue", "mtime": 1750254216245}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAkEA;AAEA;EACAA,4BADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,WADA;MAEAC;IAFA,CALA;IASAE;MACAH,WADA;MAEAC;IAFA,CATA;IAaAG;MACAJ,YADA;MAEAC;IAFA,CAbA;IAiBAI;MACAL,YADA;MAEAC;IAFA;EAjBA,CAFA;;EAwBAK;IACA;MACA;MACAC,WAFA;MAGAC,aAHA;MAIAC,cAJA;MAKAC;QACAC,cADA;QAEAC,mBAFA;QAGAC,mBAHA;QAIAC,WAJA;QAKAC,UALA;QAMAC,YANA;QAOAC;MAPA,CALA;MAcAC,iBAdA;MAeAC,WAfA;;MAgBAC;QACA;UACAC,WADA;UAEAC,gBAFA;UAGAC;QAHA;MAKA;;IAtBA;EAwBA,CAjDA;;EAkDAC;IACAC;MACA;QACAd;MADA;IAGA;;EALA,CAlDA;EAyDAe;IACAlB;MACAmB;QACA;UACA;YACAC,qCADA;YAEAjB,yEAFA;YAGAC,2CAHA;YAIAC,2CAJA;YAKAC,2BALA;YAMAC,uCANA;YAOAC,6BAPA;YAQAC;UARA;QAUA;MACA,CAdA;;MAeAY,UAfA;MAgBAC;IAhBA;EADA,CAzDA;EA6EAC;IACA;IACAC;MACA,2BADA,CAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MACA;QACA;MACA,CAFA;IAGA,CAfA;;IAgBA;IACA;MACAC;MACA;;MACA;QACA;UACAtB,2CADA;UAEAC,2CAFA;UAGAC,2CAHA;UAIAC,2BAJA;UAKAC,+BALA;UAMAC,6BANA;UAOAC,qCAPA;UAQAiB;QARA;QAUA;;QACA;UACA;YAAAC;YAAAC;UAAA;UACA;UACA,sBAHA,CAIA;UACA;;UACA;QACA;MACA;IACA,CAzCA;;IA0CA;IACA;MACA;QACA;UACAzB,uBADA;UAEAC,iCAFA;UAGAC,iCAHA;UAIAC,iBAJA;UAKAC,6BALA;UAMAC,mBANA;UAOAqB,2BAPA;UAQAH;QARA;QAUA;;QACA;UACA;YAAAC;YAAAC;UAAA;QACA;MACA,CAfA,MAeA;QACAH;QACA;;QACA;UACA;YACAtB,qCADA;YAEAC,+CAFA;YAGAC,+CAHA;YAIAC,+BAJA;YAKAC,mCALA;YAMAC,iCANA;YAOAkB;UAPA;UASA;;UACA;YACA;cAAAC;cAAAC;YAAA;YACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA,CAlFA;;IAmFA;IACAE;MACA;IACA,CAtFA;;IAuFA;IACAC;MACA;MACA;MACA;QACA;MACA,CAFA;IAGA;;EA9FA;AA7EA", "names": ["name", "props", "parentId", "type", "default", "treeDatas", "rootitems", "tableItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "valid", "dialog", "checkbox", "formModel", "ParentId", "EquipmentCode", "EquipmentName", "Level", "Enabled", "Remark", "SortNumber", "editformModel", "options", "normalizer", "id", "label", "children", "computed", "ParentNameId", "watch", "handler", "ParentName", "deep", "immediate", "methods", "initFrom", "console", "ID", "text", "color", "sortNumber", "rootChange", "closedialog"], "sourceRoot": "src/views/factoryPlant/physicalModelNew/components", "sources": ["physicalModelDialog.vue"], "sourcesContent": ["<!-- add 新增 -->\r\n<!-- delete 批量删除 -->\r\n<!-- edit 修改 -->\r\n<template>\r\n    <v-dialog v-model=\"dialog\" persistent max-width=\"720px\">\r\n        <!-- 新增 -->\r\n        <v-card ref=\"form\">\r\n            <v-card-title class=\"d-flex text-h6 justify-space-between primary lighten-2\">\r\n                {{ $t('DFM_WLMX._XZMX') }}\r\n                <v-icon @click=\"closedialog\">mdi-close</v-icon>\r\n            </v-card-title>\r\n            <!-- 表单内容 -->\r\n            <v-card-text class=\"mt-7\">\r\n                <v-container>\r\n                    <v-form ref=\"addform\" v-model=\"valid\">\r\n                        <v-row>\r\n                            <v-col class=\"py-0 px-3\" cols=\"12\" sm=\"6\" md=\"6\">\r\n                                <treeselect v-model=\"ParentNameId.ParentId\" no-results-text=\"暂无数据\" :multiple=\"false\"\r\n                                    :placeholder=\"$t('DFM_WLMX._SJCD')\" :options=\"treeDatas\" :normalizer=\"normalizer\" />\r\n                            </v-col>\r\n                            <v-col class=\"py-0 px-3\" cols=\"12\" sm=\"6\" md=\"6\">\r\n                                <v-text-field ref=\"EquipmentName\" v-model=\"formModel.EquipmentName\"\r\n                                    :rules=\"[v => !!v || '名称不能为空']\" outlined dense :label=\"$t('DFM_WLMX._MC')\"\r\n                                    required></v-text-field>\r\n                            </v-col>\r\n                            <v-col class=\"py-0 px-3\" cols=\"12\" sm=\"6\" md=\"6\">\r\n                                <v-text-field ref=\"EquipmentCode\" v-model=\"formModel.EquipmentCode\"\r\n                                    :rules=\"[v => !!v || '编码不能为空']\" outlined dense :label=\"$t('DFM_WLMX._BM')\"\r\n                                    required></v-text-field>\r\n                            </v-col>\r\n                            <v-col class=\"py-0 px-3\" cols=\"12\" sm=\"6\" md=\"6\">\r\n                                <v-select v-model=\"formModel.Level\" :items=\"rootitems\" clearable dense outlined\r\n                                    :label=\"$t('DFM_WLMX._LX')\" @change=\"rootChange\"></v-select>\r\n                            </v-col>\r\n                            <v-col class=\"py-0 px-3\" cols=\"12\" sm=\"6\" md=\"6\">\r\n                                <v-text-field ref=\"SortNumber\" v-model=\"formModel.SortNumber\"\r\n                                    :rules=\"[v => !!v || '排序号不能为空']\" outlined dense label=\"排序号\"\r\n                                    required></v-text-field>\r\n                            </v-col>\r\n                            <v-col class=\"py-0 px-3\" cols=\"12\" sm=\"6\" md=\"6\">\r\n                                <v-radio-group v-model=\"formModel.Enabled\" row class=\"my-0\">\r\n                                    <template #label>\r\n                                        <div>是否启用</div>\r\n                                    </template>\r\n                                    <v-radio :label=\"$t('DFM_WLMX._GB')\" :value=\"0\"></v-radio>\r\n                                    <v-radio :label=\"$t('DFM_WLMX._QY')\" :value=\"1\"></v-radio>\r\n                                </v-radio-group>\r\n                            </v-col>\r\n                            <v-col class=\"py-0 px-3\" cols=\"12\">\r\n                                <v-textarea v-model=\"formModel.Remark\" rows=\"2\" outlined\r\n                                    :label=\"$t('DFM_WLMX._MS')\"></v-textarea>\r\n                            </v-col>\r\n                        </v-row>\r\n                    </v-form>\r\n                </v-container>\r\n            </v-card-text>\r\n            <v-card-actions class=\"lighten-3\">\r\n                <v-checkbox v-model=\"checkbox\" :label=\"$t('GLOBAL._QDBGBTC')\"></v-checkbox>\r\n                <v-spacer></v-spacer>\r\n                <v-btn color=\"primary\" @click=\"addSubmit\">{{ $t('GLOBAL._QD') }}</v-btn>\r\n                <v-btn @click=\"closedialog\">{{ $t('GLOBAL._GB') }}</v-btn>\r\n            </v-card-actions>\r\n        </v-card>\r\n    </v-dialog>\r\n</template>\r\n<script>\r\nimport { EquipmentSaveForm } from '@/api/factoryPlant/physicalModel.js';\r\n\r\nexport default {\r\n    name: 'DataDictionaryDialog',\r\n    props: {\r\n        parentId: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        treeDatas: {\r\n            type: Array,\r\n            default: () => []\r\n        },\r\n        rootitems: {\r\n            type: Array,\r\n            default: () => []\r\n        },\r\n        tableItem: {\r\n            type: Object,\r\n            default: () => { }\r\n        },\r\n        hasChildren: {\r\n            type: Object,\r\n            default: () => { }\r\n        }\r\n    },\r\n    data() {\r\n        return {\r\n            // 提交表单数据\r\n            valid: true,\r\n            dialog: false,\r\n            checkbox: true,\r\n            formModel: {\r\n                ParentId: null,\r\n                EquipmentCode: null,\r\n                EquipmentName: null,\r\n                Level: null,\r\n                Enabled: 1,\r\n                Remark: null,\r\n                SortNumber: 1\r\n            },\r\n            editformModel: {},\r\n            options: [],\r\n            normalizer(node) {\r\n                return {\r\n                    id: node.id,\r\n                    label: node.name,\r\n                    children: node.children\r\n                };\r\n            }\r\n        };\r\n    },\r\n    computed: {\r\n        ParentNameId() {\r\n            return {\r\n                ParentId: this.parentId\r\n            };\r\n        },\r\n    },\r\n    watch: {\r\n        dialog: {\r\n            handler(curVal) {\r\n                if (curVal && this.tableItem.ID) {\r\n                    this.formModel = {\r\n                        ParentName: this.tableItem.ParentName,\r\n                        ParentId: this.tableItem.ParentId == '0' ? null : this.tableItem.ParentId,\r\n                        EquipmentCode: this.tableItem.EquipmentCode,\r\n                        EquipmentName: this.tableItem.EquipmentName,\r\n                        Level: this.tableItem.Level,\r\n                        Enabled: this.tableItem.Enabled ? 1 : 0,\r\n                        Remark: this.tableItem.Remark,\r\n                        SortNumber: this.tableItem.SortNumber\r\n                    }\r\n                }\r\n            },\r\n            deep: true,\r\n            immediate: true\r\n        }\r\n    },\r\n    methods: {\r\n        // 初始化表单\r\n        initFrom() {\r\n            this.$refs.addform.reset();\r\n            // this.formModel = {\r\n            //     ParentId: null,\r\n            //     EquipmentCode: null,\r\n            //     EquipmentName: null,\r\n            //     Level: null,\r\n            //     Enabled: 1,\r\n            //     Remark: null\r\n            // }\r\n            this.$nextTick(() => {\r\n                this.formModel.Enabled = 1;\r\n            });\r\n        },\r\n        //新增\r\n        async addSubmit() {\r\n            console.log(this.hasChildren);\r\n            let fromvalidate = await this.$refs.addform.validate();\r\n            if (fromvalidate) {\r\n                let params = {\r\n                    ParentId: this.ParentNameId.ParentId || '0',\r\n                    EquipmentCode: this.formModel.EquipmentCode,\r\n                    EquipmentName: this.formModel.EquipmentName,\r\n                    Level: this.formModel.Level,\r\n                    Enabled: this.formModel.Enabled,\r\n                    Remark: this.formModel.Remark,\r\n                    SortNumber: this.formModel.SortNumber,\r\n                    ID: this.tableItem.ID ? this.tableItem.ID : ''\r\n                };\r\n                let res = await EquipmentSaveForm(params);\r\n                if (res.success) {\r\n                    this.$store.commit('SHOW_SNACKBAR', { text: '添加成功', color: 'success' });\r\n                    this.dialog = this.checkbox ? false : true;\r\n                    this.$emit('getdata')\r\n                    // this.$parent.$parent.GetEquipmentPageList();\r\n                    // this.$parent.$parent.GetEquipmentTree();\r\n                    this.initFrom();\r\n                }\r\n            }\r\n        },\r\n        //编辑\r\n        async editSubmit(type, item) {\r\n            if (type && type == 'Enabled') {\r\n                let params = {\r\n                    ParentId: item.ParentId,\r\n                    EquipmentCode: item.EquipmentCode,\r\n                    EquipmentName: item.EquipmentName,\r\n                    Level: item.Level,\r\n                    Enabled: item.Enabled ? 1 : 0,\r\n                    Remark: item.Remark,\r\n                    sortNumber: item.SortNumber,\r\n                    ID: item.ID\r\n                };\r\n                let res = await EquipmentSaveForm(params);\r\n                if (res.success) {\r\n                    this.$store.commit('SHOW_SNACKBAR', { text: '修改成功', color: 'success' });\r\n                }\r\n            } else {\r\n                console.log(this.tableItem);\r\n                let fromvalidate = await this.$refs.addform.validate();\r\n                if (fromvalidate) {\r\n                    let params = {\r\n                        ParentId: this.editformModel.ParentId,\r\n                        EquipmentCode: this.editformModel.EquipmentCode,\r\n                        EquipmentName: this.editformModel.EquipmentName,\r\n                        Level: this.editformModel.Level,\r\n                        Enabled: this.editformModel.Enabled,\r\n                        Remark: this.editformModel.Remark,\r\n                        ID: this.tableItem.ID\r\n                    };\r\n                    let res = await EquipmentSaveForm(params);\r\n                    if (res.success) {\r\n                        this.$store.commit('SHOW_SNACKBAR', { text: '修改成功', color: 'success' });\r\n                        this.dialog = this.checkbox ? false : true;\r\n                        this.$parent.$parent.GetEquipmentPageList();\r\n                        this.$parent.$parent.GetEquipmentTree();\r\n                        this.initFrom();\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        // 跟节点选择\r\n        rootChange(v) {\r\n            this.formModel.Level = v;\r\n        },\r\n        //关闭取消\r\n        closedialog() {\r\n            this.dialog = false;\r\n            this.$refs.addform.reset();\r\n            this.$nextTick(() => {\r\n                this.formModel.Enabled = 1 + '';\r\n            });\r\n        }\r\n    }\r\n};\r\n</script>\r\n"]}]}