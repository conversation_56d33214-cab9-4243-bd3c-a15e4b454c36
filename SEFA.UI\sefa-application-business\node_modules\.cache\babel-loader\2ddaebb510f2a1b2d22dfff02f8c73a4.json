{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\WLPOlist\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\WLPOlist\\index.vue", "mtime": 1750254216308}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA+eA;AACA;AACA,SACAA,eADA,EAEAC,wBAFA,EAGAC,wBAHA,EAIAC,kBAJA,EAKAC,+BALA,EAMAC,mBANA,EAOAC,YAPA,EAQAC,cARA,EASAC,YATA,EAUAC,YAVA,EAWAC,WAXA,EAYAC,wBAZA,EAaAC,uBAbA,EAcAC,WAdA,EAeAC,iBAfA,EAgBAC,mBAhBA,QAiBA,4BAjBA;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;EACAC;IACAC,6CADA;IAEAC,6CAFA;IAGAC,yCAHA;IAIAC,iDAJA;IAKAC,6CALA;IAMAC,6CANA;IAOAC,6DAPA;IAQAC;EARA,CADA;;EAWAC;IACA;MACAC,wBADA;MAEAC,mBAFA;MAGAC,UAHA;MAIAC,cAJA;MAKAC,iBALA;MAMAC,eANA;MAOAC,eACA;QACAC,4BADA;QAEAC,cAFA;QAGAC,aAHA;QAIAC,SAJA;QAKAC;MALA,CADA,EAQA;QACAJ,iCADA;QAEAC,aAFA;QAGAC,aAHA;QAIAC,SAJA;QAKAC;MALA,CARA,CAPA;MAuBAC,YACA;QACAC,aADA;QAEAN,0CAFA;QAGAC,iBAHA;QAIAE,SAJA;QAKA;QACAC,cANA;QAOAG;MAPA,CADA,EAUA;QACAP,yCADA;QAEAC,oBAFA;QAGAC,aAHA;QAIAC,SAJA;QAKAC;MALA,CAVA,EAiBA;QACAJ,oCADA;QAEAC,aAFA;QAGAC,aAHA;QAIAC,SAJA;QAKAK,UALA;QAMAC,UANA;QAOAL;MAPA,CAjBA,EA0BA;QACAJ,iCADA;QAEAC,YAFA;QAGAS,cAHA;QAIAP,SAJA;QAKAC;MALA,CA1BA,EAiCA;QACAJ,wDADA;QAEAC,gBAFA;QAGAC,aAHA;QAIAC,SAJA;QAKAC;MALA,CAjCA,CAvBA;MAgEAO,cAhEA;MAiEAC,4CAjEA;MAkEAC,qBAlEA;MAmEAC,qBAnEA;MAoEAC,eApEA;MAqEAC;QACAC,QADA;QAEAC,OAFA;QAEA;QACAC,YAHA;QAGA;QACAC,YAJA;QAIA;QACAC;MALA,CArEA;MA4EAC,aACA;QACAlB,cADA;QAEAmB,8BAFA;QAGAtB,eAHA;QAIAE,SAJA;QAKAqB;MALA,CADA,EAQA;QACApB,aADA;QAEAD,SAFA;QAGAF,yBAHA;QAIAsB;MAJA,CARA,EAcA;QACAnB,aADA;QAEAD,SAFA;QAGAF,cAHA;QAIAsB;MAJA,CAdA,EAoBA;QACAnB,aADA;QAEAD,SAFA;QAGAF,uBAHA;QAIAsB;MAJA,CApBA,EA0BA;QACAnB,aADA;QAEAD,SAFA;QAGAF,aAHA;QAIAsB;MAJA,CA1BA,EAgCA;QACAnB,aADA;QAEAD,SAFA;QAGAF,iBAHA;QAIAsB;MAJA,CAhCA,CA5EA;MAmHAE,aAnHA;MAoHAC,qBApHA;MAqHAC,iBArHA;MAsHAC,aAtHA;MAuHAC,mBAvHA;MAwHAC,UAxHA;MAyHAC,cACA;QACA/B,sCADA;QAEAG,SAFA;QAGAF;MAHA,CADA,EAMA;QACAD,gCADA;QAEAG,SAFA;QAGAF;MAHA,CANA,EAWA;QACAD,kCADA;QAEAG,SAFA;QAGAF;MAHA,CAXA,CAzHA;MA0IA+B,mBACA;QACAhC,wCADA;QAEAC,oBAFA;QAGAE,SAHA;QAIAC,YAJA;QAKA6B;MALA,CADA,EAQA;QACAjC,wCADA;QAEAC,oBAFA;QAGAE,SAHA;QAIAC,YAJA;QAKA8B,aALA;QAMAD;MANA,CARA,EAgBA;QACAjC,+BADA;QAEAC,eAFA;QAGAE,SAHA;QAIA+B,aAJA;QAKA9B;MALA,CAhBA,EAuBA;QACAJ,kCADA;QAEAC,cAFA;QAGAE,SAHA;QAIAC,cAJA;QAKAoB,UALA;QAMAtB;MANA,CAvBA,EA+BA;QACAF,kCADA;QAEAC,cAFA;QAGAE,SAHA;QAIAC,mBAJA;QAKAF;MALA,CA/BA,EAsCA;QACAF,oCADA;QAEAC,gBAFA;QAGAE,YAHA;QAIAC;MAJA,CAtCA,CA1IA;MAuLA+B,iBAvLA;MAwLAC,uBAxLA;MAyLAC,aAzLA;MA0LAC,gBA1LA;MA2LAC,WACA;QACAvC,0CADA;QAEAG,SAFA;QAGAF;MAHA,CADA,EAMA;QACAD,+BADA;QAEAG,SAFA;QAGAF;MAHA,CANA,EAWA;QACAD,iCADA;QAEAG,SAFA;QAGAF;MAHA,CAXA,EAgBA;QACAD,wCADA;QAEAG,SAFA;QAGAF;MAHA,CAhBA,EAqBA;QACAD,sCADA;QAEAG,SAFA;QAGAF;MAHA,CArBA,EA0BA;QACAD,oCADA;QAEAG,SAFA;QAGAF;MAHA,CA1BA,EA+BA;QACAG,aADA;QAEAJ,8BAFA;QAGAG,SAHA;QAIAF;MAJA,CA/BA,CA3LA;MAiOAuC,gBACA;QACAxC,+BADA;QAEAC,YAFA;QAGAE,SAHA;QAIAC,cAJA;QAKAG;MALA,CADA,EAQA;QACAP,4BADA;QAEAC,SAFA;QAGAE,SAHA;QAIAC;MAJA,CARA,CAjOA;MAgPAqC,cAhPA;MAiPAC,cAjPA;MAkPAC,aAlPA;MAmPAC,mBAnPA;MAoPAC,kBApPA;MAqPAC,WArPA;MAsPAC,qBAtPA;MAuPAC,YAvPA;MAwPAC,oBAxPA;MAyPAC,gBAzPA;MA0PAC,aA1PA;MA2PAC,WA3PA;MA4PAC,mBA5PA;MA6PAC,cA7PA;MA8PAC,oBA9PA;MA+PAC,cACA;QACAxD,sCADA;QAEAC,oBAFA;QAGAE;MAHA,CADA,EAMA;QACAH,kCADA;QAEAC,cAFA;QAGAE;MAHA,CANA,EAWA;QACAH,kCADA;QAEAC,cAFA;QAGAE;MAHA,CAXA,EAgBA;QACAH,6BADA;QAEAC,WAFA;QAGAE;MAHA,CAhBA,EAqBA;QACAH,8BADA;QAEAC,UAFA;QAGAE;MAHA,CArBA,EA0BA;QACAH,kCADA;QAEAC,sBAFA;QAGAE;MAHA,CA1BA,CA/PA;MA+RAsD,mBACA;QACAzD,kCADA;QAEAG,SAFA;QAGAF,cAHA;QAIAC;MAJA,CADA,CA/RA;MAuSAwD,aACA;QACA1D,kCADA;QAEAG,SAFA;QAGAF;MAHA,CADA,EAMA;QACAD,oCADA;QAEAG,SAFA;QAGAF;MAHA,CANA,EAWA;QACAD,kCADA;QAEAG,SAFA;QAGAF;MAHA,CAXA,EAgBA;QACAD,sCADA;QAEAG,SAFA;QAGAF;MAHA,CAhBA,EAqBA;QACAD,mCADA;QAEAG,SAFA;QAGAF;MAHA,CArBA,EA0BA;QACAD,6BADA;QAEAG,SAFA;QAGAF;MAHA,CA1BA,CAvSA;MAuUA0D,eACA;QACA3D,iCADA;QAEAC,cAFA;QAGAE;MAHA,CADA,EAMA;QACAH,gCADA;QAEAC,kBAFA;QAGAE;MAHA,CANA,EAWA;QACAH,kCADA;QAEAC,oBAFA;QAGAE;MAHA,CAXA,EAgBA;QACAH,sCADA;QAEAC,mBAFA;QAGAC,aAHA;QAIAE,cAJA;QAKAG,UACA;UACAqD,kBADA;UAEA5D;QAFA,CADA,EAKA;UACA4D,mBADA;UAEA5D;QAFA,CALA,EASA;UACA4D,qBADA;UAEA5D;QAFA,CATA,CALA;QAmBAG;MAnBA,CAhBA,EAqCA;QACAH,+BADA;QAEAC,YAFA;QAGAG,cAHA;QAIAF,aAJA;QAKAK,WALA;QAMAJ;MANA,CArCA;IAvUA;EAsXA,CAlYA;;EAmYA0D;IACA;IACA;IACA;IACA;EACA,CAxYA;;EAyYAC;IACA;MACAC;QACAC,6CADA;QAEAC,4CAFA;QAGA7D;MAHA,GAIA8D,IAJA,CAIA;QACA;UACAC,qBADA;UAEAC;QAFA;QAIA;QACA;QACA;QACAC;UACAC,gBADA;UAEAlE;QAFA;MAIA,CAhBA;IAiBA,CAnBA;;IAoBA;MACA;QACAiE;UACAC,yCADA;UAEAlE;QAFA;QAIA;MACA;;MACA;QACAmE,wBADA;QAEAC;MAFA;MAIA;MACA;;MACA;QACAH;UACAC,0CADA;UAEAlE;QAFA;QAIA;MACA,CANA,MAMA;QACA;QACA;MACA;IACA,CA5CA;;IA6CA;MACA;MACA;IACA,CAhDA;;IAiDAqE;MACA;MACA;MACA;IACA,CArDA;;IAsDAC;MACA;IACA,CAxDA;;IAyDAC;MACA;MACA;IACA,CA5DA;;IA6DA;MACA;MACAC;QACAC;QACAA;QACAA;QACAA;MACA,CALA;MAMA;IACA,CAtEA;;IAuEA;MACA;QACAR;UACAC,sDADA;UAEAlE;QAFA;QAIA;MACA;;MACA;QACA;UACA;QACA;MACA,CAJA;;MAKA;QACAiE;UACAC,yCADA;UAEAlE;QAFA;QAIA;MACA;;MACA;QACA0E,8CADA;QAEAN,8CAFA;QAGApB,4BAHA;QAIA2B,wCAJA;QAKAC,wCALA;QAMAT,uCANA;QAOAU,oCAPA;QAQAC,0BARA;QASAC,uCATA;QAUAC;MAVA;MAYA;MACAf;QACAC,gBADA;QAEAlE;MAFA;MAIA;MACA;IACA,CA9GA;;IA+GA;MACA;MACA;MACA;QACAmE;MADA;MAGA;MACA;MACAK;QACAC;QACAA;;QACA;UACA;QACA;MACA,CANA;MAOA;;MACA;QACA;MACA;;MACA;QACA;UACA;YACA;cACAA;YACA,CAFA,MAEA;cACAA;YACA;UACA;QACA,CARA;MASA;;MACA;QACA;UACAA;QACA,CAFA,MAEA;UACAA;QACA;MACA,CANA;MAOA;MACA;MACA;MACA;IACA,CAxJA;;IAyJAQ;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA;IACA,CA/JA;;IAgKAC;MACA;QACAT;MACA,CAFA;MAGA;IACA,CArKA;;IAsKAU;MACA;MACA;QACA;UACAV;QACA,CAFA,MAEA;UACAA;UACAA;QACA,CAHA,MAGA;UACAA;QACA;MACA,CATA;MAUA;MACA,uBAbA,CAcA;IACA,CArLA;;IAsLA;MACA;QACA;UACA;QACA;MACA,CAJA;;MAKA;QACAR;UACAC,yCADA;UAEAlE;QAFA;QAIA;MACA;;MACA;MACA;QACAoF;MACA,CAFA;MAGAA;MACAA;MACA;MACA;MACA;MACAnB;QACAC,gBADA;QAEAlE;MAFA;IAIA,CAhNA;;IAiNA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAqF;MACA;QACA;MACA;IACA,CA9NA;;IA+NAC;MACA;MACA;MACA;IACA,CAnOA;;IAoOA;MACA;QACA;MACA;;MACA;QACArB;UACAC,mDADA;UAEAlE;QAFA;QAIA;MACA;;MACA;QACAiE;UACAC,qDADA;UAEAlE;QAFA;QAIA;MACA;;MACA;QACAgD,qBADA;QAEAuC;MAFA;MAIA;MACA;MACA;;MACA;QACAnG;MACA;;MACA;QACAA;MACA;;MACA;QACAA;MACA;;MACA;;MACA;QACA;UACA;YACAqF;UACA;QACA,CAJA;MAKA;;MACA;MACA,mDA3CA,CA4CA;;MAEA;MAEA;MACA;IACA,CAtRA;;IAuRA;MACA;;MACA;QACA;;QACA;UACAW;QACA;;QACA;UACAA;QACA;;QACA;;QACA;UACA;YACA;cACAX;YACA;UACA,CAJA;QAKA;MACA;IACA,CA1SA;;IA2SA;MACA;;MACA;QACAR;UACAC,yCADA;UAEAlE;QAFA;QAIA;MACA;;MACA;QACAiE;UACAC,qCADA;UAEAlE;QAFA;QAIA;MACA;;MACA;QACAiE;UACAC,sCADA;UAEAlE;QAFA;MAIA;;MACA;QACAgD,qBADA;QAEAuC,oBAFA;QAGAX,wCAHA;QAIAY;MAJA;MAMA;MACA;MACA;MACA;MACA;MACAvB;QACAC,gBADA;QAEAlE;MAFA;IAIA,CAhVA;;IAiVA;MACA;MACA;MACA;QACAyF;MADA;MAGA;MACA;IACA,CAzVA;;IA0VA;MACA;MACA;MACA;IACA,CA9VA;;IA+VA;MACA;QACA;UACA;QACA;MACA,CAJA;;MAKA;QACAxB;UACAC,yCADA;UAEAlE;QAFA;QAIA;MACA;;MACA;QACA0F,sBADA;QAEAC,wFAFA;QAGAC;MAHA;;MAKA;QACA3B;UACAC,+CADA;UAEAlE;QAFA;QAIA;MACA;;MACA;MACA;MACA;MACAiE;QACAC,gBADA;QAEAlE;MAFA;IAIA,CA/XA;;IAgYA;MACA;MACA;MACA;MACA;QACA6F,iCADA;QAEAC,uBAFA;QAGAC,oBAHA;QAIAC;MAJA;MAMA;;MACA;QACA/B;UACAC,gBADA;UAEAlE;QAFA;MAIA,CALA,MAKA;QACA;QACA;MACA;IACA,CApZA;;IAqZA;MACA;MACA;IACA,CAxZA;;IAyZAiG;MACA;MACA;IACA,CA5ZA;;IA6ZAC;MACA;MACA;IACA,CAhaA;;IAiaA;MACA;QACAjC;UACAC,gDADA;UAEAlE;QAFA;QAIA;MACA;;MACA;QACA;MACA,CAFA;MAGA;QACA0C,qBADA;QAEAyD;MAFA;MAIA;MACAlC;QACAC,gBADA;QAEAlE;MAFA;MAIA;MACA;IACA,CAvbA;;IAwbA;MACA2D;QACAC,6CADA;QAEAC,4CAFA;QAGA7D;MAHA,GAIA8D,IAJA,CAIA;QACA;;QACA;UACAsC;YACA;UACA,CAFA;QAGA,CAJA,MAIA;UACAA;QACA;;QACA;QACA;QACA;QACAnC;UACAC,gBADA;UAEAlE;QAFA;MAIA,CApBA;IAqBA,CA9cA;;IA+cA;MACA2D;QACAC,6CADA;QAEAC,4CAFA;QAGA7D;MAHA,GAIA8D,IAJA,CAIA;QACA;;QACA;UACAsC;YACA;UACA,CAFA;QAGA,CAJA,MAIA;UACAA;QACA;;QACA;QACA;QACA;QACAnC;UACAC,gBADA;UAEAlE;QAFA;MAIA,CApBA;IAqBA,CAreA;;IAseA;MACA2D;QACAC,6CADA;QAEAC,4CAFA;QAGA7D;MAHA,GAIA8D,IAJA,CAIA;QACA;UACAuC;QADA;;QAGA;UACA;YACA7C,QADA;YAEA8C;UAFA;UAIA;UACA;UACA;UACArC;YACAC,gBADA;YAEAlE;UAFA;QAIA,CAZA,MAYA;UACA;YACAwD,QADA;YAEA8C;UAFA;UAIA;UACA;UACA;UACArC;YACAC,gBADA;YAEAlE;UAFA;QAIA;MACA,CAjCA;IAkCA,CAzgBA;;IA0gBA;MACA;QACA;UACA,uDADA,CAEA;UACA;;UACA;;UACA;YACAZ;cACAmH,4BADA,CAEA;;cACAA;YACA,CAJA;UAKA;;UACA;UACAC;UACA;QACA;MACA;IACA,CA7hBA;;IA8hBA;MACA;QACA;UACA;QACA;MACA,CAJA;;MAKA;QACAvC;UACAC,yCADA;UAEAlE;QAFA;QAIA;MACA;;MACA;QACA+D,qBADA;QAEAC,SAFA;QAGAyC,yCAHA;QAIAC;MAJA;MAMA;MACA;MACA;MACA;MACAzC;QACAC,gBADA;QAEAlE;MAFA;IAIA,CAzjBA;;IA0jBA2G;MACA;MACA;MACA;MACA;QACAlC;MACA,CAFA,EAJA,CAOA;;MACA;QACA;UACA;YACAA;UACA;QACA;MACA,CANA;MAOA;MACA;MACA;;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA,CAFA,MAEA;QACA;MACA,CAxBA,CAyBA;;;MACA;QACA;UACA;;UACA;YACAmC;YACAA;YACA;UACA;QACA;MACA,CATA;MAUAJ;MACA;IACA,CAhmBA;;IAimBAK;MACA;IACA,CAnmBA;;IAomBAC;MACAC;QACA;MACA,CAFA,EAEA,GAFA;IAGA,CAxmBA;;IAymBA;MACA;MACA;MACA;MACA;MACA;MACAA;QACA;MACA,CAFA,EAEA,GAFA;;MAGA;QACA;UACA;YACAtC;UACA;QACA,CAJA;MAKA;;MACA;QACAd;UACAC,6CADA;UAEAC,4CAFA;UAGAmD,wBAHA;UAIAhH;QAJA,GAMA8D,IANA,CAMA;UACA;YACAmD,eADA;YAEAZ;UAFA;UAIA;UACApC;YACAC,iBADA;YAEAlE;UAFA;QAIA,CAhBA,EAiBAkH,KAjBA,CAiBA,cAjBA;MAkBA;IACA,CA7oBA;;IA8oBAC;MACA;MACA;MACAC,sCAHA,CAGA;;MACA;MACA;MACA;MACAC;MACAC;MACA;IACA,CAxpBA;;IAypBAC;MACA;;MACA;QACAC;MACA;;MACA;IACA,CA/pBA;;IAgqBAC;MACA;QACA;QACA;UACA;YACAtG;UACA;QACA,CAJA;QAKA;MACA;IACA,CA1qBA;;IA2qBAuG;MACA;QACA;MACA;;MACA,gBAJA,CAKA;;MACA;QACA;MACA;;MACA;QACA;UACAvG;QACA;MACA,CAJA,EATA,CAcA;;MACA;IACA,CA3rBA;;IA4rBAwG;MACA;QACA;QACA;UACA;YACAC;UACA;QACA,CAJA;QAKA;MACA;IACA,CAtsBA;;IAwsBAC;MACA;MACA;MACA;IACA,CA5sBA;;IA6sBAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACArD;MACA,CAFA;MAGA;IACA,CAxtBA;;IAytBA;MACA;QACAsD;MADA;MAGA;MACA;MACA;MACA3I;QACAqF;QACAA;MACA,CAHA;MAKA;MACA;QACA;UACAsD;QADA;QAGA;QACA,6BALA,CAMA;;QACAvB;QACAhC;UACA;QACA,CAFA;MAGA,CAXA;MAaA;MACA;MACA;IACA,CAtvBA;;IAuvBA;MACA;QACA;MACA;;MACA;QACAwD,kBADA;QAEAC,YAFA;QAGAC,uBAHA;QAIAC,qBAJA;QAKA9F,oCALA;QAMA+F,6BANA;QAOAC,gFAPA;QAQAC,gBARA;QASAC,qBATA;QAUAC,gCAVA;QAWAzH,mCAXA;QAYA0H,iBAZA;QAaAC,WAbA;QAcAC;MAdA;;MAgBA;QACA;UACA;YACAC;UACA;QACA,CAJA;MAKA;;MACA;;MACA;QACA;MACA,CA9BA,CA+BA;MACA;MACA;MACA;MACA;;;MACA;IACA,CA5xBA;;IA6xBAC;MACA;MACA;IACA,CAhyBA;;IAiyBAC;MACA;MACA;IACA;;EApyBA;AAzYA", "names": ["GetListViewList", "GetProductionUpdateShift", "GetProductionShiftSelect", "GetLastProcessData", "UpdateMaterialProcessDataStatus", "toSendOrderInfoToSS", "GettoRelease", "UpdatePoStatus", "RebuildBatch", "BindPoRecipe", "OperationPo", "MyGetEquipmentsByOrderId", "MySendLabelPrintToColos", "MyGetQrCode", "getDataDictionary", "GetSegmentBatchList", "components", "execute", "consume", "batch", "parameter", "formula", "produce", "processlongtext", "property", "data", "ThroatOutputModal", "PlanStartTime", "Source", "SourceList", "StartModel", "WCSModel", "WCSinputlist", "label", "id", "require", "value", "type", "Startlist", "notShow", "options", "value2", "value3", "disabled", "showFrom", "timepicker", "tableheader", "tableId", "QuickSearch", "pageOptions", "total", "page", "pageSize", "pageCount", "pageSizeitems", "searchlist", "name", "option", "tableList", "activeName", "detailShow", "<PERSON><PERSON><PERSON>", "ProduceModel", "BtnObj", "Producelist", "Produceinputlist", "datetype", "disable", "selectprinter", "selectprinterOption", "detailArr", "EditModel", "Editlist", "Editinputlist", "StatusList", "ReasonList", "ShiftList", "selectTabelData", "tablechooselist", "ShiftID", "ShiftListModal", "checkRow", "CompleteModel", "SSCCModel", "SSCCValue", "BatchId", "ConsumeModel", "ConsumeObj", "SegmentBatchList", "Consumelist", "Consumeinputlist", "detailList", "Completelist", "key", "mounted", "methods", "MessageBox", "confirmButtonText", "cancelButtonText", "then", "Id", "Status", "Message", "message", "EquipmentId", "ProductionDate", "ToThroatOutput", "ShowQRCode", "getQRcodesRes", "res", "item", "ExpirationDate", "Location", "Quantity", "ExecutionId", "UnitId", "IsPrint", "PrintId", "checkSelectable", "toWCSUpDate", "toC<PERSON>s", "obj", "GetData", "ScanOpen", "Sscc", "IsNotCheckRunOrder", "ProductionOrderId", "poId", "batchCode", "count", "LineCode", "equipmentCode", "productionDate", "productionId", "handleSelectionChange", "addNew", "ProIDS", "arr", "ID", "body", "item1", "console", "ProduceStatus", "Reason", "toComplete", "x", "editShow", "tabChange", "setTimeout", "closeOnClickModal", "IsReminded", "catch", "getDay", "today", "tMonth", "tDate", "doHandleMonth", "m", "getStatusName", "getReasonName", "getStatusColor", "color", "<PERSON><PERSON>ch", "<PERSON><PERSON><PERSON>y", "ItemCode", "NeedQARelease", "Resource", "MaterialDescription", "ProductionOrderNo", "StartTime", "EndTime", "FillLineCode", "Key", "pageIndex", "orderByFileds", "Formula", "SegmentCode", "params", "handleSizeChange", "handleCurrentChange"], "sourceRoot": "src/views/Producting/WLPOlist", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"usemystyle PoList\">\r\n        <div class=\"InventorySearchBox\">\r\n            <div class=\"searchbox\">\r\n                <div class=\"datebox\">\r\n                    <div class=\"datepickbox\">\r\n                        <el-date-picker\r\n                            v-model=\"timepicker\"\r\n                            type=\"daterange\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            range-separator=\"-\"\r\n                            :start-placeholder=\"$t('DFM_RL._KSRQ')\"\r\n                            :end-placeholder=\"$t('DFM_RL._JSRQ')\"\r\n                        ></el-date-picker>\r\n                    </div>\r\n                </div>\r\n                <div class=\"inputformbox\" v-for=\"(item, index) in searchlist\" :key=\"index\">\r\n                    <v-text-field class=\"vueinput\" type=\"text\" v-if=\"item.type == 'input'\" v-model.trim=\"item.value\" dense outlined :label=\"item.name\" :placeholder=\"item.name\" />\r\n                    <el-select multiple filterable clearable v-model=\"item.value\" v-if=\"item.type == 'select'\" :myid=\"item.id\" :placeholder=\"item.name\">\r\n                        <el-option v-for=\"(it, ind) in item.option\" :key=\"ind\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                    </el-select>\r\n                    <el-checkbox v-if=\"item.type == 'checkbox'\" :myid=\"item.id\" v-model=\"item.value\">{{ item.name }}</el-checkbox>\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <el-input class=\"quickSearchinput\" :placeholder=\"$t('BatchPallets.QuickSearch')\" v-model=\"QuickSearch\">\r\n                    <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n                </el-input>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <el-button size=\"small\" style=\"margin-left: 5px\" icon=\"el-icon-s-help\" @click=\"getempty()\">{{ this.$t('GLOBAL._CZ') }}</el-button>\r\n                <el-button\r\n                    class=\"tablebtn\"\r\n                    v-has=\"'PRO_PREP_SHIFT'\"\r\n                    size=\"small\"\r\n                    :disabled=\"tablechooselist > 0 ? false : true\"\r\n                    style=\"width: 160px; margin-left: 5px\"\r\n                    icon=\"el-icon-plus\"\r\n                    @click=\"addNew()\"\r\n                >\r\n                    {{ this.$t('POList.AddMaterialPreShift') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" size=\"small\" :disabled=\"tablechooselist > 0 ? false : true\" style=\"width: 160px; margin-left: 5px\" icon=\"el-icon-plus\" @click=\"toRebuildBatch(true)\">\r\n                    {{ this.$t('POList.constructingbatches') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                </el-button>\r\n                <!-- <el-button class=\"tablebtn\" size=\"small\" :disabled=\"tablechooselist > 0 ? false : true\" style=\"width: 160px; margin-left: 5px\" icon=\"el-icon-plus\" @click=\"toBindPoRecipe(true)\">\r\n                    {{ this.$t('POList.BindPoRecipe') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                </el-button> -->\r\n                <!-- <el-button class=\"tablebtn\" size=\"small\" style=\"width: 160px; margin-left: 5px\" icon=\"el-icon-plus\" @click=\"ToThroatOutput()\">\r\n                    {{ this.$t('POList.ThroatOutput') }}\r\n                </el-button> -->\r\n            </div>\r\n        </div>\r\n        <div class=\"tablebox\">\r\n            <el-table border :data=\"tableList\" @selection-change=\"handleSelectionChange\" style=\"width: 100%\" height=\"700\">\r\n                <el-table-column type=\"selection\" width=\"55\" fixed=\"left\" :selectable=\"checkSelectable\"></el-table-column>\r\n                <el-table-column\r\n                    v-for=\"(item, index) in tableheader\"\r\n                    :fixed=\"item.fixed ? item.fixed : false\"\r\n                    :key=\"index\"\r\n                    sortable\r\n                    :align=\"item.align\"\r\n                    :prop=\"item.prop ? item.prop : item.value\"\r\n                    :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                    :width=\"item.width\"\r\n                >\r\n                    <template v-slot:header=\"scope\">\r\n                        <span v-if=\"item.icon\">\r\n                            <i :class=\"item.icon\"></i>\r\n                        </span>\r\n                        <span v-if=\"!item.icon\">{{ scope.column.label }}</span>\r\n                    </template>\r\n                    <template slot-scope=\"scope\">\r\n                        <i class=\"el-icon-document\" v-if=\"scope.column.property == 'detail'\" @click=\"detaildrawShow(scope.row)\"></i>\r\n                        <span v-else>\r\n                            <span v-if=\"scope.column.property == 'PlanStartTime'\">{{ $dayjs(scope.row.PlanStartTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'PlanEndTime'\">{{ $dayjs(scope.row.PlanEndTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                            <!-- <span v-else-if=\"scope.column.property == 'SapDate'\">{{ scope.row.SapDate == null ? '' : $dayjs(scope.row.SapDate).format('YYYY-MM-DD') }}</span> -->\r\n                          \r\n                            <span v-else-if=\"scope.column.property == 'Sequence'\">\r\n                                <div style=\"color: #808080;font-weight: 900;font-size: larger\">{{ scope.row.Sequence }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'PoStatus'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: `${getStatusColor(scope.row.PoStatus)}` }\">\r\n                                    {{ getStatusName(scope.row.PoStatus) }}\r\n                                </div>\r\n                            </span>\r\n                            <!-- <span style=\"display: flex\" v-else-if=\"scope.column.property == 'Execute'\">\r\n                                <div class=\"statusbox Execute1\">\r\n                                    {{ scope.row.RunningCount }}\r\n                                </div>\r\n                                <div class=\"statusbox Execute2\">\r\n                                    {{ scope.row.StopedCount }}\r\n                                </div>\r\n                                <div class=\"statusbox Execute3\">\r\n                                    {{ scope.row.ExecutionCount }}\r\n                                </div>\r\n                            </span> -->\r\n                            <!-- <span v-else-if=\"scope.column.property == 'Bezei'\">\r\n                                <div v-for=\"(each, index1) in scope.row[scope.column.property].split(';')\" :key=\"index1\" class=\"text-left\">{{ each }}</div>\r\n                            </span> -->\r\n                            <!-- <span v-else-if=\"scope.column.property == 'Count'\">\r\n                                {{ scope.row.Sequence + '/' + scope.row.Count }}\r\n                            </span> -->\r\n                            <!-- <span v-else-if=\"scope.column.property == 'IsHavePreservative'\">\r\n                                <i :class=\"scope.row[item.value] === '1' ? 'el-icon-star-on' : ''\"></i>\r\n                            </span> -->\r\n                            <span v-else-if=\"scope.column.property == 'QaStatus'\">\r\n                                <!-- <div class=\"statusbox\" :style=\"{ background: `${(scope.row.QaStatus=='通过'?'green':'yellow')}`}\">\r\n                                    {{ scope.row.QaStatus }}\r\n                                </div> -->\r\n                                <div class=\"qAstatusbox\" :style=\"{ background: scope.row.QaStatus === '通过' ? '#3DCD58' : '#FFA500' }\">{{ scope.row.QaStatus }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'operate'\" style=\"display: flex; justify-content: space-evenly\">\r\n                                <el-button class=\"operatebtn\" size=\"mini\" style=\"width: 70px\" icon=\"el-icon-full-screen\" @click=\"ScanOpen(scope.row)\">\r\n                                    {{ $t('Consume.Scan') }}\r\n                                </el-button>\r\n                                <el-button class=\"operatebtn\" size=\"mini\" style=\"width: 70px\" icon=\"el-icon-printer\" @click=\"toColos(scope.row)\">\r\n                                    {{ $t('Inventory.Print') }}\r\n                                </el-button>\r\n                                <el-button\r\n                                    class=\"operatebtn\"\r\n                                    size=\"mini\"\r\n                                    style=\"width: 70px\"\r\n                                    :disabled=\"scope.row.ExecutionId == '' || scope.row.ExecutionId == null\"\r\n                                    icon=\"el-icon-video-play\"\r\n                                    @click=\"ProduceOpen(scope.row)\"\r\n                                >\r\n                                    {{ $t('Consume.Produce') }}\r\n                                </el-button>\r\n                                <!-- <i class=\"el-icon-zoom-in\" @click=\"ScanOpen(scope.row)\" style=\"margin-right: 12px\"></i>\r\n                                <i class=\"el-icon-printer\" @click=\"toColos(scope.row)\"></i> -->\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'ActualQty'\">{{ scope.row[item.value] }}{{ scope.row.Unit }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'PlanQty'\">{{ scope.row[item.value] }}{{ scope.row.Unit }}</span>\r\n                            <!-- <span v-else-if=\"scope.column.property == 'ActualQty'\">{{ scope.row[item.value] }}{{ scope.row.Unit }}</span> -->\r\n                            <span v-else-if=\"scope.column.property == 'ProduceStatus'\">{{ scope.row[item.value] === null ? '' : $t(`POList.${scope.row[item.value]}`) }}</span>\r\n                            <!-- <span v-else-if=\"scope.column.property == 'Reason'\">{{ getReasonName(scope.row.ProduceStatus, scope.row.Reason) }}</span> -->\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n            <div class=\"paginationbox\">\r\n                <el-pagination\r\n                    @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageOptions.page\"\r\n                    :page-sizes=\"pageOptions.pageSizeitems\"\r\n                    :page-size=\"pageOptions.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next, jumper\"\r\n                    :total=\"pageOptions.total\"\r\n                    background\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Consume.Produce')\" id=\"Producedialog\" :visible.sync=\"ProduceModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Producelist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <span>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Produceinputlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input :disabled=\"item.disable\" onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'inputNumber'\" v-model=\"item.value\">\r\n                            <template slot=\"append\">{{ BtnObj.Unit1 }}</template>\r\n                        </el-input>\r\n                        <el-input :disabled=\"item.disable\" v-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                        <el-select :disabled=\"item.disable\" clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                            <el-option v-for=\"it in item.option\" :key=\"it.value\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                        </el-select>\r\n                        <el-date-picker :disabled=\"item.disable\" v-else-if=\"item.type == 'date'\" @change=\"GetDate(item.id)\" :type=\"item.datetype\" v-model=\"item.value\"></el-date-picker>\r\n                        <el-switch v-else-if=\"item.type == 'switch'\" v-model=\"item.value\" active-color=\"#3dcd58\" inactive-color=\"#ff4949\"></el-switch>\r\n                    </div>\r\n                </div>\r\n                <div class=\"dialogdetailbox\" v-if=\"Produceinputlist[5].value == true\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ $t('Consume.selectprinter') }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-select clearable v-model=\"selectprinter\" filterable>\r\n                            <el-option v-for=\"it in selectprinterOption\" :key=\"it.value\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                        </el-select>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-video-play\" @click=\"SaveProduce()\">\r\n                    {{ $t('Consume.Produce') }}\r\n                </el-button>\r\n                <el-button @click=\"ProduceModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-drawer size=\"80%\" :wrapperClosable=\"false\" :visible.sync=\"detailShow\" direction=\"rtl\">\r\n            <div slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"drawerTitlelabel\">\r\n                    <span style=\"font-size: 1.5rem; color: #494949; margin-right: 5px\">{{ detailobj.ProductionOrderNo }}</span>\r\n                    <span>{{ detailobj.Resource }}</span>\r\n                    |\r\n                    <span>{{ detailobj.MaterialCode }}</span>\r\n                    -\r\n                    <span>{{ detailobj.MaterialDescription }}</span>\r\n                    |\r\n                    <span>{{ detailobj.PlanQty }}</span>\r\n                    |\r\n                    <span style=\"width: 11vh; padding: 4px; margin-right: 0; display: inline-block; text-align: center\" class=\"statusbox\" :style=\"{ background: getStatusColor(detailobj.PoStatus) }\">\r\n                        {{ getStatusName(detailobj.PoStatus) }}\r\n                    </span>\r\n                    |\r\n                    <span style=\"color: #494949\">{{ detailobj.PlanStartTime }}</span>\r\n                    -\r\n                    <span style=\"color: #494949\">{{ detailobj.PlanEndTime }}</span>\r\n                </div>\r\n                <!-- <div class=\"drawEditBox\">\r\n                    <i class=\"el-icon-edit-outline\" @click=\"editShow()\"></i>\r\n                </div> -->\r\n            </div>\r\n            <div class=\"InventorySearchBox\" style=\"margin-bottom: 0px\">\r\n                <!-- <div class=\"InventorySearchBox\" style=\"margin-bottom: 0px\" v-if=\"Number(detailobj.PoStatus) <= 3\"> -->\r\n                <div class=\"searchbox\">\r\n                    <!-- <el-button size=\"small\" v-if=\"detailobj.PoStatus == '0'\" @click=\"toRelease()\" class=\"tablebtn\" style=\"margin-left: 5px\">\r\n                        {{ $t('POList.Release') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toComplete()\" v-if=\"detailobj.PoStatus == '1' || detailobj.PoStatus == '2' || detailobj.PoStatus == '3'\" class=\"tablebtn\" style=\"margin-left: 5px\">\r\n                        {{ $t('POList.Complete') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toRevokeRelease()\" v-if=\"detailobj.PoStatus == '1'\" class=\"tablebtn\" style=\"margin-left: 5px\">\r\n                        {{ $t('POList.RevokeRelease') }}\r\n                    </el-button> -->\r\n\r\n                    <el-button\r\n                        size=\"small\"\r\n                        @click=\"toRelease()\"\r\n                        v-if=\"(detailobj.PoStatus == '1' && detailobj.NeedQARelease == '0') || (detailobj.PoStatus == '7' && detailobj.NeedQARelease == '1')\"\r\n                        class=\"tablebtn\"\r\n                        style=\"margin-left: 5px\"\r\n                    >\r\n                        {{ $t('POList.Release') }}\r\n                    </el-button>\r\n                    <el-button\r\n                        size=\"small\"\r\n                        @click=\"toComplete()\"\r\n                        v-if=\"detailobj.PoStatus == '2' || detailobj.PoStatus == '5' || detailobj.PoStatus == '6'\"\r\n                        class=\"tablebtn\"\r\n                        style=\"margin-left: 5px; width: 170px\"\r\n                    >\r\n                        {{ $t('POList.Complete') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"Reopen()\" v-if=\"detailobj.PoStatus == '3'\" class=\"tablebtn\" style=\"margin-left: 5px\">\r\n                        {{ $t('POList.Reopen') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toWCSUpDate()\" v-if=\"detailobj.SendWcs == '1'\" class=\"tablebtn\" style=\"margin-left: 5px; width: 170px\">\r\n                        {{ $t('POList.WCSUpDate') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toRebuildBatch()\" v-if=\"detailobj.PoStatus == '2'\" class=\"tablebtn\" style=\"margin-left: 5px; width: 170px\">\r\n                        {{ $t('POList.constructingbatches') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toBindPoRecipe()\" class=\"tablebtn\" style=\"margin-left: 5px; width: 170px\">\r\n                        {{ $t('POList.BindPoRecipe') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n            <el-tabs v-model=\"activeName\" @tab-click=\"tabChange\" type=\"border-card\">\r\n                <el-tab-pane :label=\"this.$t('POList.Execute')\" name=\"Execute\">\r\n                    <execute :ProductionOrderNo=\"detailobj.ID\" ref=\"execute\"></execute>\r\n                </el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.batch')\" name=\"batch\">\r\n                    <batch :ProductionOrderNo=\"detailobj.ID\" ref=\"batch\"></batch>\r\n                </el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.consume')\" name=\"consume\"><consume :ProductionOrderNo=\"detailobj.ID\" ref=\"consume\"></consume></el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.produce')\" name=\"produce\"><produce :ProductionOrderNo=\"detailobj.ID\" ref=\"produce\"></produce></el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.parameter')\" name=\"parameter\"><parameter :ProductionOrderNo=\"detailobj.ID\" ref=\"parameter\"></parameter></el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.formula')\" name=\"formula\"><formula :ProductionOrderNo=\"detailobj.ID\" ref=\"formula\"></formula></el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.Processlongtext')\" name=\"Processlongtext\">\r\n                    <processlongtext :ProductionOrderNo=\"detailobj.ID\" :NeedQARelease=\"detailobj.NeedQARelease\" :PoStatus=\"detailobj.PoStatus\" ref=\"Processlongtext\"></processlongtext>\r\n                </el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.property')\" name=\"property\">\r\n                    <property :ProductionOrderNo=\"detailobj.ID\" ref=\"property\"></property>\r\n                </el-tab-pane>\r\n            </el-tabs>\r\n        </el-drawer>\r\n        <el-dialog :title=\"$t('POList.WCSUpDate')\" id=\"Editdialog\" :visible.sync=\"WCSModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in WCSinputlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500\">{{ item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'number'\" v-model=\"item.value\">\r\n                            <template slot=\"append\">{{ detailobj.Unit }}</template>\r\n                        </el-input>\r\n                        <el-input v-else-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                        <span v-else>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-check-outline\" @click=\"WCSSave()\">\r\n                    {{ $t('GLOBAL._QD') }}\r\n                </el-button>\r\n                <el-button @click=\"WCSModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n\r\n        <el-dialog :title=\"$t('POList.EditTitle')\" id=\"Editdialog\" :visible.sync=\"EditModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Editlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'input'\" v-model=\"item.value\">\r\n                            <template slot=\"append\">{{ $t('POList.KGHOUR') }}</template>\r\n                        </el-input>\r\n                        <span v-else>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Editinputlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500\">{{ item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'input'\" v-model=\"item.value\">\r\n                            <template slot=\"append\">{{ detailobj.Unit }}</template>\r\n                        </el-input>\r\n                        <el-select clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                            <el-option v-for=\"(it, ind) in item.options\" :key=\"ind\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                        </el-select>\r\n                        <el-input v-else-if=\"item.type == 'textArea'\" type=\"textarea\" autosize v-model=\"item.value\"></el-input>\r\n                        <span v-else>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-edit-outline\">\r\n                    {{ $t('POList.Edit') }}\r\n                </el-button>\r\n                <el-button @click=\"EditModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n\r\n        <el-dialog :title=\"$t('POList.AddMaterialPreShift')\" :visible.sync=\"ShiftListModal\">\r\n            <div class=\"dialogdetailbox\">\r\n                <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500\">{{ $t('POList.MaterialPreShift') }}*</div>\r\n                <div class=\"dialogdetailsinglevalue\">\r\n                    <el-select v-model=\"ShiftID\">\r\n                        <el-option v-for=\"it in ShiftList\" :key=\"it.ID\" :label=\"it.Name\" :value=\"it.ID\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"ShiftListModal = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-edit-outline\" @click=\"ShiftSave()\">\r\n                    {{ $t('GLOBAL._QR') }}\r\n                </el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog :title=\"$t('POList.Complete')\" :visible.sync=\"CompleteModel\" width=\"650px\">\r\n            <div class=\"dialogdetailbox\" v-for=\"(item, index) in Completelist\" :key=\"index\">\r\n                <div class=\"dialogdetailsinglelabel\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                <div class=\"dialogdetailsinglevalue\">\r\n                    <el-select @change=\"getData2(item)\" v-model=\"item.value\" clearable filterable v-if=\"item.type == 'select'\">\r\n                        <el-option v-for=\"(it, ind) in item.options\" :key=\"ind\" :label=\"it.label\" :value=\"it.key\"></el-option>\r\n                    </el-select>\r\n                    <span v-else>{{ item.value }}</span>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"CompleteModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-edit-outline\" @click=\"CompleteSave()\">\r\n                    {{ $t('GLOBAL._QR') }}\r\n                </el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog :title=\"$t('Inventory.Print')\" id=\"Startdialog\" :visible.sync=\"StartModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Startlist\" :key=\"index\" v-show=\"!item.notShow\">\r\n                    <div class=\"dialogdetailsinglelabel\" :style=\"{ width: item.type == 'BatchCode' ? '20%' : '20%' }\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                    <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: item.type == 'BatchCode' ? '400px' : '77%' }\">\r\n                        <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\" :disabled=\"item.disabled\"></el-input>\r\n                        <div v-else-if=\"item.type == 'BatchCode'\" style=\"display: flex\">\r\n                            <el-input @change=\"getQrCode()\" v-model=\"item.value\"></el-input>\r\n                            <el-input v-model=\"item.value2\" disabled></el-input>\r\n                            <el-input @change=\"getQrCode()\" v-model=\"item.value3\"></el-input>\r\n                            <el-button class=\"tablebtn\" @click=\"getBatchCode()\" size=\"mini\" style=\"margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff\" icon=\"el-icon-refresh\"></el-button>\r\n                        </div>\r\n                        <el-select @change=\"GetData()\" clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                            <el-option v-for=\"(it, ind) in item.options\" :key=\"ind\" :label=\"it.value\" :value=\"it.key\"></el-option>\r\n                        </el-select>\r\n                        <el-date-picker\r\n                            @change=\"GetData()\"\r\n                            v-else-if=\"item.type == 'date'\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                            :disabled=\"item.disabled\"\r\n                            v-model=\"item.value\"\r\n                            type=\"datetime\"\r\n                        ></el-date-picker>\r\n                        <span v-else>{{ chooseItem.TargetQuantity }}{{ chooseItem.Unit1 }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-video-play\" @click=\"SendColos()\">\r\n                    {{ $t('Overview.SendColos') }}\r\n                </el-button>\r\n                <el-button @click=\"StartModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog :title=\"$t('Consume.Scan')\" id=\"SSCCdialog\" :visible.sync=\"SSCCModel\" width=\"650px\">\r\n            <div style=\"display: flex\">\r\n                <div :style=\"'100%'\">\r\n                    <div class=\"dialogdetailbox\">\r\n                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: '20%' }\">{{ $t('Consume.SSCC') + ' *' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: '77%' }\">\r\n                            <el-input v-model=\"SSCCValue\" @keyup.enter.native=\"SearchSscc()\">\r\n                                <template slot=\"append\"><i slot=\"suffix\" class=\"el-icon-full-screen\" @click=\"SearchSscc()\"></i></template>\r\n                            </el-input>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"dialogdetailbox\">\r\n                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: '20%' }\">{{ $t('Overview.Batch') + ' *' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: '77%' }\">\r\n                            <el-select clearable v-model=\"BatchId\" filterable>\r\n                                <el-option v-for=\"it in SegmentBatchList\" :key=\"it.key\" :label=\"it.value\" :value=\"it.key\"></el-option>\r\n                            </el-select>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-refresh-left\" @click=\"ShowQRCode()\">\r\n                    {{ $t('Consume.Scan') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-zoom-in\" @click=\"SearchSscc()\">\r\n                    {{ $t('Consume.Search') }}\r\n                </el-button>\r\n                <el-button @click=\"SSCCModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <QRcode ref=\"QRcode\" @getQRcodesRes=\"getQRcodesRes\"></QRcode>\r\n        <el-dialog id=\"Consumedialog\" :visible.sync=\"ConsumeModel\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">{{ $t('Consume.Consume') }}</div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Consumelist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <span>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Consumeinputlist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <el-input type=\"number\" v-if=\"item.id == 'Quantity'\" v-model=\"item.value\">\r\n                                <template slot=\"append\">{{ ConsumeObj.ChangeUnit }}</template>\r\n                            </el-input>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-folder-checked\" @click=\"ConsumeSave()\">\r\n                    {{ $t('GLOBAL._BC') }}\r\n                </el-button>\r\n                <el-button @click=\"ConsumeModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog :title=\"$t('POList.ThroatOutput')\" :visible.sync=\"ThroatOutputModal\" width=\"500px\">\r\n            <div class=\"dialogdetailbox\" style=\"margin-top: 15px\">\r\n                <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500; width: 45%\">{{ $t('PackagingWorkOrder.Order_Sort.PlanStartTime') }}*</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: 100%\">\r\n                    <el-date-picker v-model=\"PlanStartTime\" type=\"datetime\" value-format=\"yyyy-MM-dd HH:mm:ss\" placeholder=\"\"></el-date-picker>\r\n                </div>\r\n            </div>\r\n            <div class=\"dialogdetailbox\">\r\n                <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500; width: 45%\">{{ $t('POList.SourceStroage') }}*</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: 100%\">\r\n                    <el-select v-model=\"Source\" filterable>\r\n                        <el-option v-for=\"it in SourceList\" :key=\"it.key\" :label=\"it.value\" :value=\"it.key\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"ThroatOutputModal = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-edit-outline\" @click=\"ThroatOutputSave()\">\r\n                    {{ $t('GLOBAL._QR') }}\r\n                </el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { WLPOlist } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetListViewList,\r\n    GetProductionUpdateShift,\r\n    GetProductionShiftSelect,\r\n    GetLastProcessData,\r\n    UpdateMaterialProcessDataStatus,\r\n    toSendOrderInfoToSS,\r\n    GettoRelease,\r\n    UpdatePoStatus,\r\n    RebuildBatch,\r\n    BindPoRecipe,\r\n    OperationPo,\r\n    MyGetEquipmentsByOrderId,\r\n    MySendLabelPrintToColos,\r\n    MyGetQrCode,\r\n    getDataDictionary,\r\n    GetSegmentBatchList\r\n} from '@/api/Producting/POlist.js';\r\nimport { GetPrinit2 } from '@/api/Inventory/common.js';\r\nimport { GetProduceOpen, GetBPEquipmentsSelect, GetBatchCode, ConsumeScanSSCC, ConsumeViewSave, getConsumeViewEntity, ProduceLocation, ProduceSave } from '@/api/Inventory/Overview.js';\r\nimport { GetDataTreeList } from '@/api/factoryPlant/process.js';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport moment from 'moment';\r\nimport { GetMSelectListClass, GetUnitList } from '@/api/Inventory/Inventory.js';\r\nexport default {\r\n    components: {\r\n        execute: () => import('./components/Execute'),\r\n        consume: () => import('./components/consume'),\r\n        batch: () => import('./components/batch'),\r\n        parameter: () => import('./components/parameter'),\r\n        formula: () => import('./components/formula'),\r\n        produce: () => import('./components/produce'),\r\n        processlongtext: () => import('./components/Processlongtext'),\r\n        property: () => import('./components/property')\r\n    },\r\n    data() {\r\n        return {\r\n            ThroatOutputModal: false,\r\n            PlanStartTime: null,\r\n            Source: '',\r\n            SourceList: [],\r\n            StartModel: false,\r\n            WCSModel: false,\r\n            WCSinputlist: [\r\n                {\r\n                    label: this.$t('POList.Num'),\r\n                    id: 'quantity',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'number'\r\n                },\r\n                {\r\n                    label: this.$t('TRACE_WGJC._PCH'),\r\n                    id: 'lotCode',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'input'\r\n                }\r\n            ],\r\n            Startlist: [\r\n                {\r\n                    notShow: true,\r\n                    label: this.$t('ANDON_BJZY.EquipmentName'),\r\n                    id: 'EquipmentId',\r\n                    value: '',\r\n                    // require: true,\r\n                    type: 'select',\r\n                    options: []\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.BatchCode'),\r\n                    id: 'LotCode',\r\n                    require: true,\r\n                    value: '',\r\n                    value2: '',\r\n                    value3: '',\r\n                    type: 'BatchCode'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.QrCode'),\r\n                    id: 'QrCode',\r\n                    disabled: true,\r\n                    value: '',\r\n                    type: 'input'\r\n                },\r\n                {\r\n                    label: this.$t('$vuetify.dataTable.INV_TPQD.PrintCount'),\r\n                    id: 'PrintCount',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'input'\r\n                }\r\n            ],\r\n            showFrom: true,\r\n            timepicker: [this.getDay(0), this.getDay(3)],\r\n            tableheader: WLPOlist,\r\n            tableId: 'PRO_POLIST',\r\n            QuickSearch: '',\r\n            pageOptions: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            searchlist: [\r\n                {\r\n                    type: 'select',\r\n                    name: this.$t('POList.Status'),\r\n                    id: 'Available',\r\n                    value: [],\r\n                    option: []\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'MaterialDescription',\r\n                    name: this.$t('POList.Material')\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'Resource',\r\n                    name: this.$t('POList.Source')\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'ProductionOrderNo',\r\n                    name: this.$t('POList.ProcessOrder')\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'Formula',\r\n                    name: this.$t('$vuetify.dataTable.PRO_POLIST.Formula')\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'SegmentCode',\r\n                    name: this.$t('POList.SegmentCode')\r\n                }\r\n            ],\r\n            tableList: [],\r\n            activeName: 'Execute',\r\n            detailShow: false,\r\n            detailobj: {},\r\n            ProduceModel: false,\r\n            BtnObj: {},\r\n            Producelist: [\r\n                {\r\n                    label: this.$t('Consume.ProcessOrder'),\r\n                    value: '',\r\n                    id: 'ProductionOrderNo'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Source'),\r\n                    value: '',\r\n                    id: 'Source'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Material'),\r\n                    value: '',\r\n                    id: 'MaterialDescription'\r\n                }\r\n            ],\r\n            Produceinputlist: [\r\n                {\r\n                    label: this.$t('Consume.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    value: '',\r\n                    type: 'date',\r\n                    datetype: 'datetime'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.ExpirationDate'),\r\n                    id: 'ExpirationDate',\r\n                    value: '',\r\n                    type: 'date',\r\n                    disable: true,\r\n                    datetype: 'datetime'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Batch'),\r\n                    id: 'BatchCode',\r\n                    value: '',\r\n                    disable: true,\r\n                    type: 'input'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Location'),\r\n                    id: 'Location',\r\n                    value: '',\r\n                    type: 'select',\r\n                    option: [],\r\n                    require: true\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Quantity'),\r\n                    id: 'Quantity',\r\n                    value: '',\r\n                    type: 'inputNumber',\r\n                    require: true\r\n                },\r\n                {\r\n                    label: this.$t('Consume.printlabel'),\r\n                    id: 'printlabel',\r\n                    value: false,\r\n                    type: 'switch'\r\n                }\r\n            ],\r\n            selectprinter: '',\r\n            selectprinterOption: [],\r\n            detailArr: [],\r\n            EditModel: false,\r\n            Editlist: [\r\n                {\r\n                    label: this.$t('POList.ProductionOrderNo'),\r\n                    value: '',\r\n                    id: 'ProductionOrderNo'\r\n                },\r\n                {\r\n                    label: this.$t('POList.Source'),\r\n                    value: '',\r\n                    id: 'Source'\r\n                },\r\n                {\r\n                    label: this.$t('POList.Material'),\r\n                    value: '',\r\n                    id: 'Material'\r\n                },\r\n                {\r\n                    label: this.$t('POList.MaterialVersion'),\r\n                    value: '',\r\n                    id: 'MaterialVersionNumber'\r\n                },\r\n                {\r\n                    label: this.$t('POList.PlanStartTime'),\r\n                    value: '',\r\n                    id: 'PlanStartTime'\r\n                },\r\n                {\r\n                    label: this.$t('POList.PlanEndTime'),\r\n                    value: '',\r\n                    id: 'PlanEndTime'\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    label: this.$t('POList.Speed'),\r\n                    value: '',\r\n                    id: 'Speed'\r\n                }\r\n            ],\r\n            Editinputlist: [\r\n                {\r\n                    label: this.$t('POList.Status'),\r\n                    id: 'Status',\r\n                    value: '',\r\n                    type: 'select',\r\n                    options: []\r\n                },\r\n                {\r\n                    label: this.$t('POList.Num'),\r\n                    id: 'Num',\r\n                    value: '',\r\n                    type: 'input'\r\n                }\r\n            ],\r\n            StatusList: [],\r\n            ReasonList: [],\r\n            ShiftList: [],\r\n            selectTabelData: [],\r\n            tablechooselist: 0,\r\n            ShiftID: '',\r\n            ShiftListModal: false,\r\n            checkRow: {},\r\n            CompleteModel: false,\r\n            SSCCModel: false,\r\n            SSCCValue: '',\r\n            BatchId: '',\r\n            ConsumeModel: false,\r\n            ConsumeObj: {},\r\n            SegmentBatchList: [],\r\n            Consumelist: [\r\n                {\r\n                    label: this.$t('Consume.ProcessOrder'),\r\n                    id: 'ProcessOrderNo',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Location'),\r\n                    id: 'Location',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Material'),\r\n                    id: 'Material',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Lot'),\r\n                    id: 'Batch',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.SSCC'),\r\n                    id: 'Sscc',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Quantity'),\r\n                    id: 'QuantitywithUnit',\r\n                    value: ''\r\n                }\r\n            ],\r\n            Consumeinputlist: [\r\n                {\r\n                    label: this.$t('Consume.Quantity'),\r\n                    value: '',\r\n                    id: 'Quantity',\r\n                    require: true\r\n                }\r\n            ],\r\n            detailList: [\r\n                {\r\n                    label: this.$t('Consume.Material'),\r\n                    value: '',\r\n                    id: 'Material'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.StorageBin'),\r\n                    value: '',\r\n                    id: 'StorageBin'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Required'),\r\n                    value: '',\r\n                    id: 'Quantity2'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Consumptions'),\r\n                    value: '',\r\n                    id: 'Quantity1'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Remaining'),\r\n                    value: '',\r\n                    id: 'Remaining'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Uom'),\r\n                    value: '',\r\n                    id: 'Unit1'\r\n                }\r\n            ],\r\n            Completelist: [\r\n                {\r\n                    label: this.$t('POList.Material'),\r\n                    id: 'Material',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.PlanQty'),\r\n                    id: 'PlanQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.ActualQty'),\r\n                    id: 'ActualQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.ProduceStatus'),\r\n                    id: 'ProduceStatus',\r\n                    require: true,\r\n                    type: 'select',\r\n                    options: [\r\n                        {\r\n                            key: 'NotComplete',\r\n                            label: this.$t('POList.NotComplete')\r\n                        },\r\n                        {\r\n                            key: 'OverComplete',\r\n                            label: this.$t('POList.OverComplete')\r\n                        },\r\n                        {\r\n                            key: 'CompleteAtOnce',\r\n                            label: this.$t('POList.CompleteAtOnce')\r\n                        }\r\n                    ],\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.Reason'),\r\n                    id: 'Reason',\r\n                    type: 'select',\r\n                    require: true,\r\n                    options: [],\r\n                    value: ''\r\n                }\r\n            ]\r\n        };\r\n    },\r\n    mounted() {\r\n        this.getSourceList();\r\n        this.getStatus();\r\n        this.GetShiftSelect();\r\n        this.getprintList();\r\n    },\r\n    methods: {\r\n        async Reopen() {\r\n            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_Reopen')}`, '', {\r\n                confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                type: 'warning'\r\n            }).then(async () => {\r\n                let params = {\r\n                    Id: this.detailobj.ID,\r\n                    Status: 6\r\n                };\r\n                let res = await UpdatePoStatus(params);\r\n                this.detailShow = false;\r\n                this.getPageList();\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            });\r\n        },\r\n        async ThroatOutputSave() {\r\n            if (this.Source == '' || this.PlanStartTime == '') {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                EquipmentId: this.Source,\r\n                ProductionDate: this.PlanStartTime\r\n            };\r\n            let res = await GetProduceOpen(params);\r\n            let data = res.response;\r\n            if (data == null) {\r\n                Message({\r\n                    message: `${this.$t('POList.NotFoundPO')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            } else {\r\n                this.ProduceOpen(data);\r\n                this.ThroatOutputModal = false;\r\n            }\r\n        },\r\n        async getSourceList() {\r\n            let res = await GetBPEquipmentsSelect();\r\n            this.SourceList = res.response;\r\n        },\r\n        ToThroatOutput() {\r\n            this.PlanStartTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n            this.Source = '';\r\n            this.ThroatOutputModal = true;\r\n        },\r\n        ShowQRCode() {\r\n            this.$refs.QRcode.getQRcode();\r\n        },\r\n        getQRcodesRes(val) {\r\n            this.SSCCValue = val.text;\r\n            this.SearchSscc();\r\n        },\r\n        async getprintList() {\r\n            let res = await GetPrinit2();\r\n            res.response.forEach(item => {\r\n                item.value = item.ID;\r\n                item.label = item.Code;\r\n                item.ItemName = item.Code;\r\n                item.ItemValue = item.ID;\r\n            });\r\n            this.selectprinterOption = res.response;\r\n        },\r\n        async SaveProduce() {\r\n            if (this.Produceinputlist[5].value === true && this.selectprinter === '') {\r\n                Message({\r\n                    message: `${this.$t('Inventory.PleaseSelectPrinter')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let flag = this.Produceinputlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                ExpirationDate: this.Produceinputlist[1].value,\r\n                ProductionDate: this.Produceinputlist[0].value,\r\n                BatchId: this.BtnObj.BatchId,\r\n                Location: this.Produceinputlist[3].value,\r\n                Quantity: this.Produceinputlist[4].value,\r\n                EquipmentId: this.BtnObj.RunEquipmentId,\r\n                ExecutionId: this.BtnObj.ExecutionId,\r\n                UnitId: this.BtnObj.UnitId,\r\n                IsPrint: this.Produceinputlist[5].value,\r\n                PrintId: this.selectprinter\r\n            };\r\n            let res = await ProduceSave(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.getPageList();\r\n            this.ProduceModel = false;\r\n        },\r\n        async ProduceOpen(row) {\r\n            this.Produceinputlist[3].value = '';\r\n            this.BtnObj = row;\r\n            let params = {\r\n                EquipmentId: row.RunEquipmentId\r\n            };\r\n            this.selectprinter = '';\r\n            let res = await ProduceLocation(params);\r\n            res.response.forEach(item => {\r\n                item.label = item.LocationName;\r\n                item.value = item.LocationId;\r\n                if (item.IsDefault == '1') {\r\n                    this.Produceinputlist[3].value = item.LocationId;\r\n                }\r\n            });\r\n            this.Produceinputlist[3].option = res.response;\r\n            if (this.selectprinterOption.length != 0) {\r\n                this.selectprinter = this.selectprinterOption[0].value;\r\n            }\r\n            for (let k in row) {\r\n                this.Producelist.forEach(item => {\r\n                    if (item.id == k) {\r\n                        if (k == 'MaterialDescription') {\r\n                            item.value = row.MaterialDescription + '-' + row.MaterialCode;\r\n                        } else {\r\n                            item.value = row[k];\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n            this.Produceinputlist.forEach(item => {\r\n                if (item.id == 'printlabel') {\r\n                    item.value = false;\r\n                } else if (item.id != 'Location') {\r\n                    item.value = '';\r\n                }\r\n            });\r\n            this.Produceinputlist[0].value = row.ProductionDate;\r\n            this.Produceinputlist[1].value = row.ExpirationDate;\r\n            this.Produceinputlist[2].value = row.BatchCode;\r\n            this.ProduceModel = true;\r\n        },\r\n        checkSelectable(row) {\r\n            if (row.PoStatus == '1' || row.PoStatus == '2') {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        },\r\n        toWCSUpDate() {\r\n            this.WCSinputlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            this.WCSModel = true;\r\n        },\r\n        toColos(row) {\r\n            this.checkRow = row;\r\n            this.Startlist.forEach((item, index) => {\r\n                if (index == 1) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 2) {\r\n                    item.value2 = '';\r\n                    item.value3 = '';\r\n                } else {\r\n                    item.value = '';\r\n                }\r\n            });\r\n            this.getBatchCode();\r\n            this.StartModel = true;\r\n            // this.getEquipmentList();\r\n        },\r\n        async WCSSave() {\r\n            let flag = this.WCSinputlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let obj = {};\r\n            this.WCSinputlist.forEach(item => {\r\n                obj[item.id] = item.value;\r\n            });\r\n            obj.actionType = 3;\r\n            obj.productionOrderId = this.detailobj.ID;\r\n            let res = await toSendOrderInfoToSS(obj);\r\n            this.getPageList();\r\n            this.WCSModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        // async getEquipmentList() {\r\n        //     let id = JSON.stringify(this.checkRow.ID);\r\n        //     let res = await MyGetEquipmentsByOrderId(id);\r\n        //     res.response.forEach(item => {\r\n        //         item.key = item.key + '|' + item.value;\r\n        //     });\r\n        //     this.Startlist[0].options = res.response;\r\n        //     this.StartModel = true;\r\n        // },\r\n        GetData() {\r\n            if (this.Startlist[1].value != '') {\r\n                this.getBatchCode();\r\n            }\r\n        },\r\n        ScanOpen(row) {\r\n            this.SSCCValue = '';\r\n            this.SSCCModel = true;\r\n            this.GetSegmentBatchList(row);\r\n        },\r\n        async SearchSscc(row) {\r\n            if (row) {\r\n                this.SSCCValue = row.Sscc;\r\n            }\r\n            if (this.SSCCValue === '' || this.SSCCValue === null) {\r\n                Message({\r\n                    message: `${this.$t('ConsumptionHistory.NonSSCC')}`,\r\n                    type: 'error'\r\n                });\r\n                return;\r\n            }\r\n            if (this.BatchId === '' || this.BatchId === null) {\r\n                Message({\r\n                    message: `${this.$t('ConsumptionHistory.NoBatchId')}`,\r\n                    type: 'error'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                Sscc: this.SSCCValue\r\n            };\r\n            let res = await ConsumeScanSSCC(params);\r\n            let data = res.response;\r\n            this.Id = data.ID;\r\n            if (data.Location2) {\r\n                data.Location = data.Location1Code + '-' + data.Location2;\r\n            }\r\n            if (data.MaterialCode) {\r\n                data.Material = data.MaterialName + '-' + data.MaterialCode;\r\n            }\r\n            if (data.Quantity) {\r\n                data.QuantitywithUnit = data.Quantity + ' ' + data.Unit1;\r\n            }\r\n            this.ConsumeObj = data;\r\n            for (let k in data) {\r\n                this.Consumelist.forEach(item => {\r\n                    if (item.id == k) {\r\n                        item.value = data[k];\r\n                    }\r\n                });\r\n            }\r\n            let v = this.ConsumeObj.ChangeUnit.toLowerCase() === 'g' ? 1000 : 1;\r\n            this.Consumeinputlist[0].value = data.Quantity * v;\r\n            //this.Consumeinputlist[0].value = data.Quantity;\r\n\r\n            await this.GetConsumeViewEntity(data.ID);\r\n\r\n            this.SSCCModel = false;\r\n            this.ConsumeModel = true;\r\n        },\r\n        async GetConsumeViewEntity(id) {\r\n            let res = await getConsumeViewEntity(id);\r\n            if (res) {\r\n                var obj = res.response;\r\n                if (obj.MaterialName) {\r\n                    obj.Material = obj.MaterialName + '-' + obj.MaterialCode;\r\n                }\r\n                if (obj.Quantity2) {\r\n                    obj.Remaining = (Number(obj.Quantity2) - Number(obj.Quantity1)).toFixed(2);\r\n                }\r\n                this.detailobj = obj;\r\n                for (let k in obj) {\r\n                    this.detailList.forEach(item => {\r\n                        if (item.id == k) {\r\n                            item.value = obj[k];\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        async ConsumeSave() {\r\n            let v = this.ConsumeObj.ChangeUnit.toLowerCase() === 'g' ? 1000 : 1;\r\n            if (this.Consumeinputlist[0].value == 0) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return false;\r\n            }\r\n            if (Number(this.Consumeinputlist[0].value) > Number(this.ConsumeObj.Quantity * v)) {\r\n                Message({\r\n                    message: `${this.$t('Consume.Over')}`,\r\n                    type: 'warning'\r\n                });\r\n                return false;\r\n            }\r\n            if (Number(this.Consumeinputlist[0].value) > Number(this.detailList[4].value)) {\r\n                Message({\r\n                    message: `${this.$t('Consume.Over2')}`,\r\n                    type: 'warning'\r\n                });\r\n            }\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                Sscc: this.SSCCValue,\r\n                Quantity: this.Consumeinputlist[0].value,\r\n                IsNotCheckRunOrder: true\r\n            };\r\n            let res = await ConsumeViewSave(params);\r\n            this.BatchId = '';\r\n            this.SSCCValue = '';\r\n            this.ConsumeModel = false;\r\n            this.Consumeinputlist[0].value = '';\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        async GetSegmentBatchList(row) {\r\n            this.BatchId = '';\r\n            this.SegmentBatchList = [];\r\n            let p = {\r\n                ProductionOrderId: row.ID\r\n            };\r\n            let res = await GetSegmentBatchList(p);\r\n            this.SegmentBatchList = res.response;\r\n        },\r\n        async getQrCode() {\r\n            let code = this.Startlist[2].value + this.Startlist[2].value2 + this.Startlist[2].value3;\r\n            let res = await MyGetQrCode('', this.checkRow.ID, code);\r\n            this.Startlist[3].value = res.response;\r\n        },\r\n        async SendColos() {\r\n            let flag = this.Startlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                poId: this.checkRow.ID,\r\n                batchCode: this.Startlist[2].value + this.Startlist[2].value2 + this.Startlist[2].value3,\r\n                count: this.Startlist[3].value\r\n            };\r\n            if (params.batchCode.length > 10) {\r\n                Message({\r\n                    message: `${this.$t('Overview.BatchCodeLong')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let res = await MySendLabelPrintToColos(params);\r\n            this.getPageList();\r\n            this.StartModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        async getBatchCode() {\r\n            let LineCode = this.checkRow.LineCode;\r\n            let productionId = this.checkRow.ID;\r\n            let date = moment(this.Startlist[1].value).format('YYYY-MM-DD HH:mm:ss');\r\n            let p = {\r\n                LineCode: this.Startlist[2].value,\r\n                equipmentCode: LineCode,\r\n                productionDate: date,\r\n                productionId: productionId\r\n            };\r\n            let res = await GetBatchCode(p);\r\n            if (res.response == null) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'warning'\r\n                });\r\n            } else {\r\n                this.Startlist[2].value = res.response.substring(0, 2);\r\n                this.Startlist[2].value2 = res.response.substring(2, 5);\r\n            }\r\n        },\r\n        async GetShiftSelect() {\r\n            let res = await GetProductionShiftSelect();\r\n            this.ShiftList = res.response;\r\n        },\r\n        handleSelectionChange(val) {\r\n            this.selectTabelData = val;\r\n            this.tablechooselist = val.length;\r\n        },\r\n        addNew() {\r\n            this.ShiftID = '';\r\n            this.ShiftListModal = true;\r\n        },\r\n        async ShiftSave() {\r\n            if (this.ShiftID == '') {\r\n                Message({\r\n                    message: this.$t('POList.CHECKMaterialPreShift'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let arr = this.selectTabelData.map(item => {\r\n                return item.ID;\r\n            });\r\n            let params = {\r\n                ShiftID: this.ShiftID,\r\n                ProIDS: arr\r\n            };\r\n            let res = await GetProductionUpdateShift(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.ShiftListModal = false;\r\n            this.getPageList();\r\n        },\r\n        async toRebuildBatch(isPl) {\r\n            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_GJPC')}`, '', {\r\n                confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                type: 'warning'\r\n            }).then(async () => {\r\n                let arr = [];\r\n                if (isPl) {\r\n                    arr = this.selectTabelData.map(item => {\r\n                        return item.ID;\r\n                    });\r\n                } else {\r\n                    arr = [this.detailobj.ID];\r\n                }\r\n                let res = await RebuildBatch(arr);\r\n                this.detailShow = false;\r\n                this.getPageList();\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            });\r\n        },\r\n        async toBindPoRecipe(isPl) {\r\n            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_BDPF')}`, '', {\r\n                confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                type: 'warning'\r\n            }).then(async () => {\r\n                let arr = [];\r\n                if (isPl) {\r\n                    arr = this.selectTabelData.map(item => {\r\n                        return item.ID;\r\n                    });\r\n                } else {\r\n                    arr = [this.detailobj.ID];\r\n                }\r\n                let res = await BindPoRecipe(arr);\r\n                this.detailShow = false;\r\n                this.getPageList();\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            });\r\n        },\r\n        async toRelease() {\r\n            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_SF')}`, '', {\r\n                confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                type: 'warning'\r\n            }).then(async () => {\r\n                let obj = {\r\n                    ID: this.detailobj.ID\r\n                };\r\n                if (this.detailobj.NeedQARelease == '0') {\r\n                    let parmas = {\r\n                        key: '2',\r\n                        body: JSON.stringify(obj)\r\n                    };\r\n                    let res = await OperationPo(parmas);\r\n                    this.getPageList();\r\n                    this.$refs[this.activeName].getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);\r\n                    Message({\r\n                        message: res.msg,\r\n                        type: 'success'\r\n                    });\r\n                } else {\r\n                    let parmas = {\r\n                        key: '5',\r\n                        body: JSON.stringify(obj)\r\n                    };\r\n                    let res = await OperationPo(parmas);\r\n                    this.getPageList();\r\n                    this.$refs[this.activeName].getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);\r\n                    Message({\r\n                        message: res.msg,\r\n                        type: 'success'\r\n                    });\r\n                }\r\n            });\r\n        },\r\n        async getData2(item) {\r\n            if (item.id == 'ProduceStatus') {\r\n                if (item.value != '') {\r\n                    let res = await this.$getNewDataDictionary(item.value);\r\n                    //let res = this.ReasonList.find(x=>x.ItemCode = item.value)\r\n                    // console.log(res);\r\n                    let data = res;\r\n                    if (data.length > 0) {\r\n                        data.forEach(item1 => {\r\n                            item1.key = item1.ItemValue;\r\n                            // console.log(this._i18n.locale);\r\n                            item1.label = this._i18n.locale === 'en' ? item1.ItemValue : item1.ItemName;\r\n                        });\r\n                    }\r\n                    this.Completelist[4].options = data;\r\n                    console.log(this.Completelist[4].options);\r\n                    this.Completelist[4].value = '';\r\n                }\r\n            }\r\n        },\r\n        async CompleteSave() {\r\n            let flag = this.Completelist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '' || item.value == null;\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                Id: this.detailobj.ID,\r\n                Status: 3,\r\n                ProduceStatus: this.Completelist[3].value,\r\n                Reason: this.Completelist[4].value\r\n            };\r\n            let res = await UpdatePoStatus(params);\r\n            this.detailShow = false;\r\n            this.CompleteModel = false;\r\n            this.getPageList();\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        toComplete() {\r\n            this.detailobj.Material = this.detailobj.MaterialDescription + '-' + this.detailobj.MaterialCode;\r\n            this.detailobj.PlanQuantity = this.detailobj.PlanQty + this.detailobj.Unit;\r\n            this.detailobj.ActualQuantity = this.detailobj.ActualQty + this.detailobj.Unit;\r\n            this.Completelist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            //console.log(this.Completelist[4]);\r\n            this.Completelist.forEach(item => {\r\n                for (let k in this.detailobj) {\r\n                    if (item.id == k) {\r\n                        item.value = this.detailobj[k];\r\n                    }\r\n                }\r\n            });\r\n            this.Completelist[4].options = [];\r\n            let PlanQuantity = Number(this.detailobj.PlanQty);\r\n            let ActualQuantity = Number(this.detailobj.ActualQty);\r\n            if (PlanQuantity > ActualQuantity) {\r\n                this.Completelist[3].value = 'NotComplete';\r\n            } else if (PlanQuantity == ActualQuantity) {\r\n                this.Completelist[3].value = 'CompleteAtOnce';\r\n            } else {\r\n                this.Completelist[3].value = 'OverComplete';\r\n            }\r\n            //console.log(this.ReasonList);\r\n            this.ReasonList.forEach(x => {\r\n                if (x.ItemCode == this.Completelist[3].value) {\r\n                    let res = this.Completelist[4].options.find(x1 => x1.ItemCode === this.Completelist[3].value && x1.ItemValue === x.ItemValue);\r\n                    if (res === null || res === undefined || typeof res === 'undefined') {\r\n                        x.key = x.ItemValue;\r\n                        x.label = this._i18n.locale === 'en' ? x.ItemValue : x.ItemName;\r\n                        this.Completelist[4].options.push(x);\r\n                    }\r\n                }\r\n            });\r\n            console.log(this.Completelist[4].options);\r\n            this.CompleteModel = true;\r\n        },\r\n        editShow() {\r\n            this.EditModel = true;\r\n        },\r\n        tabChange() {\r\n            setTimeout(() => {\r\n                this.$refs[this.activeName].getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);\r\n            }, 200);\r\n        },\r\n        async detaildrawShow(obj) {\r\n            this.activeName = 'Execute';\r\n            this.detailobj = obj;\r\n            let res = await GetLastProcessData('', this.detailobj.ID);\r\n            this.detailShow = true;\r\n            this.detailobj.Material = this.detailobj.MaterialDescription + '-' + this.detailobj.MaterialCode;\r\n            setTimeout(() => {\r\n                this.$refs.execute.getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);\r\n            }, 200);\r\n            for (let k in this.detailobj) {\r\n                this.Editlist.forEach(item => {\r\n                    if (item.id == k) {\r\n                        item.value = this.detailobj[k];\r\n                    }\r\n                });\r\n            }\r\n            if (res.response.IsReminded == '0') {\r\n                MessageBox.confirm(`${this.$t('POList.warningText')}`, '', {\r\n                    confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                    cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                    closeOnClickModal: false,\r\n                    type: 'warning'\r\n                })\r\n                    .then(async () => {\r\n                        let params = {\r\n                            IsReminded: '1',\r\n                            ID: res.response.ID\r\n                        };\r\n                        let res2 = await UpdateMaterialProcessDataStatus(params);\r\n                        Message({\r\n                            message: res2.msg,\r\n                            type: 'success'\r\n                        });\r\n                    })\r\n                    .catch(async () => {});\r\n            }\r\n        },\r\n        getDay(day) {\r\n            var today = new Date();\r\n            var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;\r\n            today.setTime(targetday_milliseconds); //注意，这行是关键代码\r\n            var tYear = today.getFullYear();\r\n            var tMonth = today.getMonth();\r\n            var tDate = today.getDate();\r\n            tMonth = this.doHandleMonth(tMonth + 1);\r\n            tDate = this.doHandleMonth(tDate);\r\n            return tYear + '-' + tMonth + '-' + tDate;\r\n        },\r\n        doHandleMonth(month) {\r\n            var m = month;\r\n            if (month.toString().length == 1) {\r\n                m = '0' + month;\r\n            }\r\n            return m;\r\n        },\r\n        getStatusName(key) {\r\n            if (key) {\r\n                let name = '';\r\n                this.StatusList.forEach(item => {\r\n                    if (item.ItemValue == key) {\r\n                        name = item.ItemName;\r\n                    }\r\n                });\r\n                return name;\r\n            }\r\n        },\r\n        getReasonName(key1, key2) {\r\n            if (key1 === null || key1 === '' || key2 === null || key2 === '') {\r\n                return '';\r\n            }\r\n            let name = key2;\r\n            //console.log(this._i18n.locale);\r\n            if (this._i18n.locale == 'en') {\r\n                return name;\r\n            }\r\n            this.ReasonList.forEach(item => {\r\n                if (item.ItemCode === key1 && item.ItemValue === key2) {\r\n                    name = item.ItemName;\r\n                }\r\n            });\r\n            //console.log(name);\r\n            return name;\r\n        },\r\n        getStatusColor(key) {\r\n            if (key) {\r\n                let color = '';\r\n                this.StatusList.forEach(item => {\r\n                    if (item.ItemValue == key) {\r\n                        color = item.Description;\r\n                    }\r\n                });\r\n                return color;\r\n            }\r\n        },\r\n\r\n        getsearch() {\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.getPageList();\r\n        },\r\n        getempty() {\r\n            this.QuickSearch = '';\r\n            this.timepicker = [];\r\n            this.timepicker[0] = this.getDay(0);\r\n            this.timepicker[1] = this.getDay(3);\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.searchlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            this.getPageList();\r\n        },\r\n        async getStatus() {\r\n            let params = {\r\n                ItemCode: 'ProductionOrderStatus'\r\n            };\r\n            const res = await GetDataTreeList(params);\r\n            let data = res.response.data;\r\n            this.StatusList = data;\r\n            data.forEach(item => {\r\n                item.label = item.ItemName;\r\n                item.value = item.ItemValue;\r\n            });\r\n\r\n            this.ReasonList = [];\r\n            this.Completelist[3].options.forEach(async item => {\r\n                let p = {\r\n                    ItemCode: item.key\r\n                };\r\n                const res = await GetDataTreeList(p);\r\n                let data = res.response.data;\r\n                //let res = await this.$getNewDataDictionary(item.key);\r\n                console.log(res.response.data);\r\n                res.response.data.forEach(item1 => {\r\n                    this.ReasonList.push(item1);\r\n                });\r\n            });\r\n\r\n            this.Editinputlist[0].options = data;\r\n            this.searchlist[0].option = data;\r\n            this.getPageList();\r\n        },\r\n        async getPageList() {\r\n            if (this.timepicker == null) {\r\n                this.timepicker = [];\r\n            }\r\n            let params = {\r\n                NeedQARelease: '0',\r\n                Resource: '',\r\n                MaterialDescription: '',\r\n                ProductionOrderNo: '',\r\n                StatusList: this.searchlist[0].value,\r\n                StartTime: this.timepicker[0],\r\n                EndTime: this.timepicker[1] == undefined ? '' : this.timepicker[1] + ' 23:59:59',\r\n                FillLineCode: '',\r\n                Key: this.QuickSearch,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize,\r\n                orderByFileds: '',\r\n                Formula: '',\r\n                SegmentCode: ''\r\n            };\r\n            for (let k in params) {\r\n                this.searchlist.forEach(item => {\r\n                    if (k == item.id) {\r\n                        params[k] = item.value;\r\n                    }\r\n                });\r\n            }\r\n            const res = await GetListViewList(params);\r\n            if (res) {\r\n                this.tableList = res.response.data;\r\n            }\r\n            // if (res.response.count == 0) {\r\n            //     this.tableList = [];\r\n            // } else {\r\n            //     this.tableList = res.response.data;\r\n            // }\r\n            this.pageOptions.total = res.response.dataCount;\r\n        },\r\n        handleSizeChange(val) {\r\n            this.pageOptions.pageSize = val;\r\n            this.getPageList();\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.pageOptions.page = val;\r\n            this.getPageList();\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scope>\r\n.PoList {\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin-bottom: 10px;\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 50%;\r\n            text-align: right;\r\n        }\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n    .splitdetailbox {\r\n        padding: 10px 0;\r\n        border: 1px solid #e8e8e8;\r\n        margin-bottom: 5px;\r\n        .dialogdetailbox {\r\n            display: flex;\r\n            align-items: center;\r\n            width: 100%;\r\n            margin-top: 10px;\r\n            .dialogdetailsinglelabel {\r\n                font-weight: 600;\r\n                width: 47%;\r\n                text-align: right;\r\n            }\r\n            .dialogdetailsinglevalue {\r\n                width: 78%;\r\n                margin-left: 20px;\r\n            }\r\n        }\r\n    }\r\n    .drawerTitlelabel {\r\n        color: #808080;\r\n        font-size: 1rem;\r\n        .statusbox {\r\n            width: auto !important;\r\n        }\r\n    }\r\n    .dialog-title {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n    }\r\n    .drawEditBox {\r\n        font-size: 1.5rem;\r\n        margin-right: 15px;\r\n        cursor: pointer;\r\n    }\r\n}\r\n.el-dialog__body {\r\n    .el-input {\r\n        width: 250px !important;\r\n    }\r\n    .el-select {\r\n        width: 250px !important;\r\n    }\r\n    .el-textarea {\r\n        width: 250px !important;\r\n    }\r\n    .longwidthinput {\r\n        .el-input {\r\n            width: 400px !important;\r\n        }\r\n        .el-select {\r\n            width: 400px !important;\r\n        }\r\n        .el-textarea {\r\n            width: 400px !important;\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}