{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\auth\\Login.vue?vue&type=style&index=0&id=0e0d6e88&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\auth\\Login.vue", "mtime": 1750254216324}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoucGFnZS1sb2dpbiB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgbWluLXdpZHRoOiA5MjBweDsNCiAgbWF4LXdpZHRoOiA5MjBweDsNCiAgbWFyZ2luOiAtMjIxcHggYXV0bzsNCg0KICAvLyAubG9naW5pbWcgew0KICAvLyAgICAgLnYtcmVzcG9uc2l2ZV9fY29udGVudCB7DQogIC8vICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjQpICFpbXBvcnRhbnQ7DQogIC8vICAgICB9DQogIC8vIH0NCiAgLmxhbmcgew0KICAgIGZsb2F0OiByaWdodDsNCiAgfQ0KDQogIC5jb3B5IHsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgbWFyZ2luLXRvcDogNDhweDsNCiAgICBjb2xvcjogI2FhYTsNCiAgfQ0KDQogIC5sb2dvMSB7DQogICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgIHRvcDogLTc0cHg7DQogIH0NCn0NCg0KLmltZyB7DQogIGhlaWdodDogY2FsYyg1MHZoIC0gMTZweCk7DQp9DQoNCi5sb2FkaW5nLWJveCB7DQogIHBvc2l0aW9uOiBmaXhlZDsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCiAgdG9wOiAwOw0KICBsZWZ0OiAwOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgLy8gYmFja2dyb3VuZDogI2ZmZjsNCg0KICAvLyAjM2RjZDU4DQogIDo6di1kZWVwIC5hbnQtc3Bpbi10ZXh0IHsNCiAgICBjb2xvcjogIzNkY2Q1ODsNCiAgfQ0KDQogIDo6di1kZWVwIC5hbnQtc3Bpbi1kb3QuYW50LXNwaW4tZG90LXNwaW4gew0KICAgIGkgew0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzNkY2Q1ODsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["Login.vue"], "names": [], "mappings": ";AAiKA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "Login.vue", "sourceRoot": "src/views/auth", "sourcesContent": ["<template>\r\n  <div class=\"\">\r\n    <v-img class=\"img\" src=\"/static/defaultbg_2.png\" alt=\"\"></v-img>\r\n\r\n    <v-row class=\"page-login float\" fill-height>\r\n      <v-col :cols=\"12\">\r\n        <div class=\"logo1\">\r\n          <v-img class=\"float-left mx-15\" src=\"/static/Schneider_logo.png\" width=\"260px\" style=\"position: relative;margin-top: 0vh;\"></v-img>\r\n          <h2 class=\"float-right mx-15\" style=\"color: white; line-height: 82px;\">\r\n            <div style=\"margin-left: 2vw\">数字化工厂管理平台</div>\r\n          </h2>\r\n        </div>\r\n        <v-card class=\"d-flex flex-row mt-4\">\r\n          <v-img class=\"loginimg\" lazy-src=\"/static/Slogo_left_2.png\" src=\"/static/Slogo_left_2.png\"></v-img>\r\n          <v-card-text>\r\n            <v-form ref=\"form\" v-model=\"formValid\" class=\"ma-10\" width=\"500\" lazy-validation>\r\n              <v-text-field v-model=\"formModel.username\" append-icon=\"mdi-account\" autocomplete=\"off\"\r\n                            name=\"login\" autofocus :label=\"$t('username')\" :placeholder=\"$t('username')\" type=\"text\"\r\n                            required outlined :rules=\"formRule.username\" />\r\n              <v-text-field v-model=\"formModel.password\" append-icon=\"mdi-lock\" autocomplete=\"off\"\r\n                            name=\"password\" :label=\"$t('password')\" :placeholder=\"$t('password')\" type=\"password\"\r\n                            :rules=\"formRule.password\" required outlined @keyup.enter=\"handleLogin\" />\r\n              <v-btn large color=\"primary\" style=\"width: 100%\" :loading=\"loading\" @click=\"handleLogin\">\r\n                {{ $t('login') }}\r\n              </v-btn>\r\n            </v-form>\r\n            <div class=\"lang\">\r\n              <v-btn text color=\"primary\" @click=\"goDomainEntry()\">{{ $t('GLOBAL._YDL') }}</v-btn>\r\n              <LocaleSwitch />\r\n            </div>\r\n          </v-card-text>\r\n        </v-card>\r\n        <div class=\"copy\" align-center>CopyRight&copy;2022施耐德电气（中国）有限公司</div>\r\n      </v-col>\r\n    </v-row>\r\n    <v-row class=\"img my-0\" fill-height></v-row>\r\n    <!-- <div class=\"loading-box\">\r\n        <a-spin tip=\"系统正在初始化中...\" :spinning=\"loading\">\r\n        </a-spin>\r\n    </div> -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nconst name = 'page-login';\r\nimport { configUrl } from '@/config';\r\n\r\nconst SSO_URL = configUrl[process.env.VUE_APP_SERVE].SSO_URL;\r\nexport default {\r\n  name: name,\r\n  components: {\r\n    LocaleSwitch: () => import('@/components/locale/LocaleSwitch')\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      formValid: false,\r\n      formModel: {\r\n        username: '',\r\n        password: '',\r\n        token: ''\r\n      },\r\n      formRule: {\r\n        username: [v => !!v || this.$t('rule.requiredUsername')],\r\n        password: [v => !!v || this.$t('rule.requiredPassword')]\r\n      },\r\n      redirectPath: '/',\r\n      queryPar: {}\r\n    };\r\n  },\r\n  watch: {\r\n    $route() {\r\n      location.reload();\r\n    }\r\n  },\r\n  mounted() {\r\n    this.isOuterChain();\r\n  },\r\n  methods: {\r\n    // 跳转使用域登录\r\n    goDomainEntry() {\r\n      console.log(\"host\", window.document.location.host)\r\n      const callback = window.location.origin;\r\n      const url = `https://sso.aac.com/login.aspx?url=${decodeURIComponent(callback)}`;\r\n      window.location.href = url\r\n    },\r\n    isOuterChain() {\r\n      const o = this.$route.query;\r\n      const { redirect, par, code, loginType } = o;\r\n\r\n      if (loginType && loginType.toLowerCase() === 'sso') {\r\n        window.location.href = SSO_URL\r\n        return false\r\n      }\r\n\r\n      this.redirectPath = redirect;\r\n      // queryPar：外链跳转过来带的参数\r\n      this.queryPar = par ? JSON.parse(par) : {};\r\n      //_DLZH： 自动登录账号；_DLMM： 自动登录密码\r\n      const { _DLZH, _DLMM } = this.queryPar;\r\n      // this.$nextTick(()=>{\r\n\r\n      // })\r\n      if (_DLZH && _DLMM) {\r\n        console.log(_DLZH, _DLMM);\r\n        this.formModel.username = _DLZH;\r\n        this.formModel.password = _DLMM;\r\n        this.loginPar();\r\n      } else if (code) {\r\n        this.formModel.token = code\r\n        this.loginPar()\r\n      } else {\r\n        // this.goDomainEntry()\r\n      }\r\n    },\r\n    handleLogin() {\r\n      if (this.$refs.form.validate()) {\r\n        this.loginPar();\r\n      }\r\n    },\r\n    loginPar() {\r\n      this.loading = true;\r\n      this.$store\r\n          .dispatch('login', this.formModel)\r\n          .then(() => {\r\n            this.handlePermission();\r\n          })\r\n          .catch(() => {\r\n            window._VMA.$emit('SHOW_SNACKBAR', {\r\n              show: true,\r\n              text: '登录失败',\r\n              color: 'error'\r\n            });\r\n            this.loading = false;\r\n          });\r\n    },\r\n    handlePermission() {\r\n      this.$store\r\n          .dispatch('getPermission')\r\n          .then(() => {\r\n            this.loading = false;\r\n            // 登录成功，去掉地址栏账号密码\r\n            delete this.queryPar._DLZH;\r\n            delete this.queryPar._DLMM;\r\n            const route = this.redirectPath ? { path: this.redirectPath, query: this.queryPar } : { path: '/' };\r\n            this.$store.commit('SETFLAG', true)\r\n            this.$router.push(route);\r\n          })\r\n          .catch(() => {\r\n            this.$router.push('/404');\r\n          });\r\n    },\r\n    handleRegister() {\r\n      console.log(this);\r\n    },\r\n    handleSocialLogin() { }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-login {\r\n  position: relative;\r\n  min-width: 920px;\r\n  max-width: 920px;\r\n  margin: -221px auto;\r\n\r\n  // .loginimg {\r\n  //     .v-responsive__content {\r\n  //         background: rgba(255, 255, 255, 0.4) !important;\r\n  //     }\r\n  // }\r\n  .lang {\r\n    float: right;\r\n  }\r\n\r\n  .copy {\r\n    text-align: center;\r\n    margin-top: 48px;\r\n    color: #aaa;\r\n  }\r\n\r\n  .logo1 {\r\n    position: absolute;\r\n    top: -74px;\r\n  }\r\n}\r\n\r\n.img {\r\n  height: calc(50vh - 16px);\r\n}\r\n\r\n.loading-box {\r\n  position: fixed;\r\n  width: 100%;\r\n  height: 100%;\r\n  top: 0;\r\n  left: 0;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  // background: #fff;\r\n\r\n  // #3dcd58\r\n  ::v-deep .ant-spin-text {\r\n    color: #3dcd58;\r\n  }\r\n\r\n  ::v-deep .ant-spin-dot.ant-spin-dot-spin {\r\n    i {\r\n      background-color: #3dcd58;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}