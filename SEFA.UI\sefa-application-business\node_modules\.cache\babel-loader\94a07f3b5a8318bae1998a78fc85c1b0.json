{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\productionManagement\\ResourceDefinition\\components\\form-dialog-operation.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\productionManagement\\ResourceDefinition\\components\\form-dialog-operation.vue", "mtime": 1750254216361}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgc2F2ZU9wZXJhdGlvbiB9IGZyb20gJ0AvYXBpL3Byb2R1Y3Rpb25NYW5hZ2VtZW50L0Zvcm11bGEnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ2FkZCcsCgogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkaWFsb2dGb3JtOiB7fSwKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGZvcm1Mb2FkaW5nOiBmYWxzZSwKICAgICAgcnVsZXM6IHsKICAgICAgICBTZWdtZW50Q29kZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpeW3peautee8lueggScsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9XSwKICAgICAgICBTZWdtZW50TmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpeW3peauteWQjeensCcsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9XQogICAgICB9CiAgICB9OwogIH0sCgogIG1vdW50ZWQoKSB7fSwKCiAgbWV0aG9kczogewogICAgc3VibWl0KCkgewogICAgICB0aGlzLiRyZWZzLmRpYWxvZ0Zvcm0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgc2F2ZU9wZXJhdGlvbih0aGlzLmRpYWxvZ0Zvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKHJlcy5tc2cpOwogICAgICAgICAgICB0aGlzLiRlbWl0KCdzYXZlRm9ybScpOwogICAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAoKICAgIHNob3coZGF0YSkgewogICAgICBjb25zb2xlLmxvZyhkYXRhKTsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy4kbmV4dFRpY2soXyA9PiB7CiAgICAgICAgdGhpcy5kaWFsb2dGb3JtID0geyAuLi5kYXRhCiAgICAgICAgfTsKICAgICAgICB0aGlzLiRyZWZzLmRpYWxvZ0Zvcm0ucmVzZXRGaWVsZHMoKTsKICAgICAgfSk7CiAgICB9CgogIH0KfTs="}, {"version": 3, "mappings": "AAqBA;AAEA;EACAA,WADA;;EAEAC;IACA;MACAC,cADA;MAEAC,oBAFA;MAGAC,kBAHA;MAIAC;QACAC,cACA;UAAAC;UAAAC;UAAAC;QAAA,CADA,CADA;QAIAC,cACA;UAAAH;UAAAC;UAAAC;QAAA,CADA;MAJA;IAJA;EAaA,CAhBA;;EAiBAE,WACA,CAlBA;;EAmBAC;IACAC;MACA;QACA;UACAC;YACA;YACA;YACA;UACA,CAJA;QAKA;MACA,CARA;IASA,CAXA;;IAYAC;MACAC;MACA;MACA;QACA,oBACA;QADA;QAGA;MACA,CALA;IAMA;;EArBA;AAnBA", "names": ["name", "data", "dialogForm", "dialogVisible", "formLoading", "rules", "SegmentCode", "required", "message", "trigger", "SegmentName", "mounted", "methods", "submit", "saveOperation", "show", "console"], "sourceRoot": "src/views/productionManagement/ResourceDefinition/components", "sources": ["form-dialog-operation.vue"], "sourcesContent": ["<template>\r\n  <el-dialog :title=\"dialogForm.ID ? '编辑' : '新增'\" :visible.sync=\"dialogVisible\" width=\"600px\"\r\n    :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\r\n    @close=\"dialogVisible = false\">\r\n    <el-form :rules=\"rules\" ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"80px\">\r\n      <el-form-item label=\"工段编码\" prop=\"SegmentCode\">\r\n        <el-input v-model=\"dialogForm.SegmentCode\" :maxlength=\"20\" placeholder=\"\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"工段名称\" prop=\"SegmentName\">\r\n        <el-input v-model=\"dialogForm.SegmentName\" :maxlength=\"20\" placeholder=\"\" />\r\n      </el-form-item>\r\n    </el-form>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\r\n      <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\"\r\n        @click=\"submit()\">确定\r\n      </el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n<script>\r\nimport { saveOperation } from '@/api/productionManagement/Formula';\r\n\r\nexport default {\r\n  name: 'add',\r\n  data() {\r\n    return {\r\n      dialogForm: {},\r\n      dialogVisible: false,\r\n      formLoading: false,\r\n      rules: {\r\n        SegmentCode: [\r\n          { required: true, message: '请输入工段编码', trigger: 'blur' },\r\n        ],\r\n        SegmentName: [\r\n          { required: true, message: '请输入工段名称', trigger: 'blur' },\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$refs.dialogForm.validate((valid) => {\r\n        if (valid) {\r\n          saveOperation(this.dialogForm).then(res => {\r\n            this.$message.success(res.msg)\r\n            this.$emit('saveForm')\r\n            this.dialogVisible = false\r\n          })\r\n        }\r\n      });\r\n    },\r\n    show(data) {\r\n      console.log(data);\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.dialogForm = {\r\n          ...data\r\n        }\r\n        this.$refs.dialogForm.resetFields()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}