<template>
    <div class="usemystyle PoList">
        <div class="InventorySearchBox">
            <div class="searchbox">
                <div class="datebox">
                    <div class="datepickbox">
                        <el-date-picker
                            v-model="timepicker"
                            type="daterange"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            range-separator="-"
                            :start-placeholder="$t('DFM_RL._KSRQ')"
                            :end-placeholder="$t('DFM_RL._JSRQ')"
                        ></el-date-picker>
                    </div>
                </div>
                <div class="inputformbox" v-for="(item, index) in searchlist" :key="index">
                    <v-text-field class="vueinput" type="text" v-if="item.type == 'input'" v-model.trim="item.value" dense outlined :label="item.name" :placeholder="item.name" />
                    <el-select multiple filterable clearable v-model="item.value" v-if="item.type == 'select'" :myid="item.id" :placeholder="item.name">
                        <el-option v-for="(it, ind) in item.option" :key="ind" :label="it.label" :value="it.value"></el-option>
                    </el-select>
                    <el-checkbox v-if="item.type == 'checkbox'" :myid="item.id" v-model="item.value">{{ item.name }}</el-checkbox>
                </div>
            </div>
            <div class="searchbox">
                <el-input class="quickSearchinput" :placeholder="$t('BatchPallets.QuickSearch')" v-model="QuickSearch">
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>
                <el-button style="margin-left: 5px" size="small" icon="el-icon-refresh" @click="getsearch()">{{ this.$t('Inventory.refresh') }}</el-button>
                <el-button size="small" style="margin-left: 5px" icon="el-icon-s-help" @click="getempty()">{{ this.$t('GLOBAL._CZ') }}</el-button>
                <el-button
                    class="tablebtn"
                    v-has="'PRO_PREP_SHIFT'"
                    size="small"
                    :disabled="tablechooselist > 0 ? false : true"
                    style="width: 160px; margin-left: 5px"
                    icon="el-icon-plus"
                    @click="addNew()"
                >
                    {{ this.$t('POList.AddMaterialPreShift') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}
                </el-button>
                <el-button class="tablebtn" size="small" :disabled="tablechooselist > 0 ? false : true" style="width: 160px; margin-left: 5px" icon="el-icon-plus" @click="toRebuildBatch(true)">
                    {{ this.$t('POList.constructingbatches') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}
                </el-button>
                <!-- <el-button class="tablebtn" size="small" :disabled="tablechooselist > 0 ? false : true" style="width: 160px; margin-left: 5px" icon="el-icon-plus" @click="toBindPoRecipe(true)">
                    {{ this.$t('POList.BindPoRecipe') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}
                </el-button> -->
                <!-- <el-button class="tablebtn" size="small" style="width: 160px; margin-left: 5px" icon="el-icon-plus" @click="ToThroatOutput()">
                    {{ this.$t('POList.ThroatOutput') }}
                </el-button> -->
            </div>
        </div>
        <div class="tablebox">
            <el-table border :data="tableList" @selection-change="handleSelectionChange" style="width: 100%" height="700">
                <el-table-column type="selection" width="55" fixed="left" :selectable="checkSelectable"></el-table-column>
                <el-table-column
                    v-for="(item, index) in tableheader"
                    :fixed="item.fixed ? item.fixed : false"
                    :key="index"
                    sortable
                    :align="item.align"
                    :prop="item.prop ? item.prop : item.value"
                    :label="$t(`$vuetify.dataTable.${tableId}.${item.value}`)"
                    :width="item.width"
                >
                    <template v-slot:header="scope">
                        <span v-if="item.icon">
                            <i :class="item.icon"></i>
                        </span>
                        <span v-if="!item.icon">{{ scope.column.label }}</span>
                    </template>
                    <template slot-scope="scope">
                        <i class="el-icon-document" v-if="scope.column.property == 'detail'" @click="detaildrawShow(scope.row)"></i>
                        <span v-else>
                            <span v-if="scope.column.property == 'PlanStartTime'">{{ $dayjs(scope.row.PlanStartTime).format('YYYY-MM-DD HH:mm') }}</span>
                            <span v-else-if="scope.column.property == 'PlanEndTime'">{{ $dayjs(scope.row.PlanEndTime).format('YYYY-MM-DD HH:mm') }}</span>
                            <!-- <span v-else-if="scope.column.property == 'SapDate'">{{ scope.row.SapDate == null ? '' : $dayjs(scope.row.SapDate).format('YYYY-MM-DD') }}</span> -->
                          
                            <span v-else-if="scope.column.property == 'Sequence'">
                                <div style="color: #808080;font-weight: 900;font-size: larger">{{ scope.row.Sequence }}</div>
                            </span>
                            <span v-else-if="scope.column.property == 'PoStatus'">
                                <div class="statusbox" :style="{ background: `${getStatusColor(scope.row.PoStatus)}` }">
                                    {{ getStatusName(scope.row.PoStatus) }}
                                </div>
                            </span>
                            <!-- <span style="display: flex" v-else-if="scope.column.property == 'Execute'">
                                <div class="statusbox Execute1">
                                    {{ scope.row.RunningCount }}
                                </div>
                                <div class="statusbox Execute2">
                                    {{ scope.row.StopedCount }}
                                </div>
                                <div class="statusbox Execute3">
                                    {{ scope.row.ExecutionCount }}
                                </div>
                            </span> -->
                            <!-- <span v-else-if="scope.column.property == 'Bezei'">
                                <div v-for="(each, index1) in scope.row[scope.column.property].split(';')" :key="index1" class="text-left">{{ each }}</div>
                            </span> -->
                            <!-- <span v-else-if="scope.column.property == 'Count'">
                                {{ scope.row.Sequence + '/' + scope.row.Count }}
                            </span> -->
                            <!-- <span v-else-if="scope.column.property == 'IsHavePreservative'">
                                <i :class="scope.row[item.value] === '1' ? 'el-icon-star-on' : ''"></i>
                            </span> -->
                            <span v-else-if="scope.column.property == 'QaStatus'">
                                <!-- <div class="statusbox" :style="{ background: `${(scope.row.QaStatus=='通过'?'green':'yellow')}`}">
                                    {{ scope.row.QaStatus }}
                                </div> -->
                                <div class="qAstatusbox" :style="{ background: scope.row.QaStatus === '通过' ? '#3DCD58' : '#FFA500' }">{{ scope.row.QaStatus }}</div>
                            </span>
                            <span v-else-if="scope.column.property == 'operate'" style="display: flex; justify-content: space-evenly">
                                <el-button class="operatebtn" size="mini" style="width: 70px" icon="el-icon-full-screen" @click="ScanOpen(scope.row)">
                                    {{ $t('Consume.Scan') }}
                                </el-button>
                                <el-button class="operatebtn" size="mini" style="width: 70px" icon="el-icon-printer" @click="toColos(scope.row)">
                                    {{ $t('Inventory.Print') }}
                                </el-button>
                                <el-button
                                    class="operatebtn"
                                    size="mini"
                                    style="width: 70px"
                                    :disabled="scope.row.ExecutionId == '' || scope.row.ExecutionId == null"
                                    icon="el-icon-video-play"
                                    @click="ProduceOpen(scope.row)"
                                >
                                    {{ $t('Consume.Produce') }}
                                </el-button>
                                <!-- <i class="el-icon-zoom-in" @click="ScanOpen(scope.row)" style="margin-right: 12px"></i>
                                <i class="el-icon-printer" @click="toColos(scope.row)"></i> -->
                            </span>
                            <span v-else-if="scope.column.property == 'ActualQty'">{{ scope.row[item.value] }}{{ scope.row.Unit }}</span>
                            <span v-else-if="scope.column.property == 'PlanQty'">{{ scope.row[item.value] }}{{ scope.row.Unit }}</span>
                            <!-- <span v-else-if="scope.column.property == 'ActualQty'">{{ scope.row[item.value] }}{{ scope.row.Unit }}</span> -->
                            <span v-else-if="scope.column.property == 'ProduceStatus'">{{ scope.row[item.value] === null ? '' : $t(`POList.${scope.row[item.value]}`) }}</span>
                            <!-- <span v-else-if="scope.column.property == 'Reason'">{{ getReasonName(scope.row.ProduceStatus, scope.row.Reason) }}</span> -->
                            <span v-else>{{ scope.row[item.prop] }}</span>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <div class="paginationbox">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="pageOptions.page"
                    :page-sizes="pageOptions.pageSizeitems"
                    :page-size="pageOptions.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageOptions.total"
                    background
                ></el-pagination>
            </div>
        </div>
        <el-dialog :title="$t('Consume.Produce')" id="Producedialog" :visible.sync="ProduceModel" width="650px">
            <div class="splitdetailbox">
                <div class="dialogdetailbox" v-for="(item, index) in Producelist" :key="index">
                    <div class="dialogdetailsinglelabel">{{ item.label }}</div>
                    <div class="dialogdetailsinglevalue">
                        <span>{{ item.value }}</span>
                    </div>
                </div>
            </div>
            <div class="splitdetailbox">
                <div class="dialogdetailbox" v-for="(item, index) in Produceinputlist" :key="index">
                    <div class="dialogdetailsinglelabel">{{ item.label }}{{ item.require ? ' *' : '' }}</div>
                    <div class="dialogdetailsinglevalue">
                        <el-input :disabled="item.disable" onkeyup="value=value.replace(/^0+|[^0-9\.]/g, '')" v-if="item.type == 'inputNumber'" v-model="item.value">
                            <template slot="append">{{ BtnObj.Unit1 }}</template>
                        </el-input>
                        <el-input :disabled="item.disable" v-if="item.type == 'input'" v-model="item.value"></el-input>
                        <el-select :disabled="item.disable" clearable v-else-if="item.type == 'select'" v-model="item.value" filterable>
                            <el-option v-for="it in item.option" :key="it.value" :label="it.label" :value="it.value"></el-option>
                        </el-select>
                        <el-date-picker :disabled="item.disable" v-else-if="item.type == 'date'" @change="GetDate(item.id)" :type="item.datetype" v-model="item.value"></el-date-picker>
                        <el-switch v-else-if="item.type == 'switch'" v-model="item.value" active-color="#3dcd58" inactive-color="#ff4949"></el-switch>
                    </div>
                </div>
                <div class="dialogdetailbox" v-if="Produceinputlist[5].value == true">
                    <div class="dialogdetailsinglelabel">{{ $t('Consume.selectprinter') }}</div>
                    <div class="dialogdetailsinglevalue">
                        <el-select clearable v-model="selectprinter" filterable>
                            <el-option v-for="it in selectprinterOption" :key="it.value" :label="it.label" :value="it.value"></el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="tablebtn" icon="el-icon-video-play" @click="SaveProduce()">
                    {{ $t('Consume.Produce') }}
                </el-button>
                <el-button @click="ProduceModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
        <el-drawer size="80%" :wrapperClosable="false" :visible.sync="detailShow" direction="rtl">
            <div slot="title" class="dialog-title">
                <div class="drawerTitlelabel">
                    <span style="font-size: 1.5rem; color: #494949; margin-right: 5px">{{ detailobj.ProductionOrderNo }}</span>
                    <span>{{ detailobj.Resource }}</span>
                    |
                    <span>{{ detailobj.MaterialCode }}</span>
                    -
                    <span>{{ detailobj.MaterialDescription }}</span>
                    |
                    <span>{{ detailobj.PlanQty }}</span>
                    |
                    <span style="width: 11vh; padding: 4px; margin-right: 0; display: inline-block; text-align: center" class="statusbox" :style="{ background: getStatusColor(detailobj.PoStatus) }">
                        {{ getStatusName(detailobj.PoStatus) }}
                    </span>
                    |
                    <span style="color: #494949">{{ detailobj.PlanStartTime }}</span>
                    -
                    <span style="color: #494949">{{ detailobj.PlanEndTime }}</span>
                </div>
                <!-- <div class="drawEditBox">
                    <i class="el-icon-edit-outline" @click="editShow()"></i>
                </div> -->
            </div>
            <div class="InventorySearchBox" style="margin-bottom: 0px">
                <!-- <div class="InventorySearchBox" style="margin-bottom: 0px" v-if="Number(detailobj.PoStatus) <= 3"> -->
                <div class="searchbox">
                    <!-- <el-button size="small" v-if="detailobj.PoStatus == '0'" @click="toRelease()" class="tablebtn" style="margin-left: 5px">
                        {{ $t('POList.Release') }}
                    </el-button>
                    <el-button size="small" @click="toComplete()" v-if="detailobj.PoStatus == '1' || detailobj.PoStatus == '2' || detailobj.PoStatus == '3'" class="tablebtn" style="margin-left: 5px">
                        {{ $t('POList.Complete') }}
                    </el-button>
                    <el-button size="small" @click="toRevokeRelease()" v-if="detailobj.PoStatus == '1'" class="tablebtn" style="margin-left: 5px">
                        {{ $t('POList.RevokeRelease') }}
                    </el-button> -->

                    <el-button
                        size="small"
                        @click="toRelease()"
                        v-if="(detailobj.PoStatus == '1' && detailobj.NeedQARelease == '0') || (detailobj.PoStatus == '7' && detailobj.NeedQARelease == '1')"
                        class="tablebtn"
                        style="margin-left: 5px"
                    >
                        {{ $t('POList.Release') }}
                    </el-button>
                    <el-button
                        size="small"
                        @click="toComplete()"
                        v-if="detailobj.PoStatus == '2' || detailobj.PoStatus == '5' || detailobj.PoStatus == '6'"
                        class="tablebtn"
                        style="margin-left: 5px; width: 170px"
                    >
                        {{ $t('POList.Complete') }}
                    </el-button>
                    <el-button size="small" @click="Reopen()" v-if="detailobj.PoStatus == '3'" class="tablebtn" style="margin-left: 5px">
                        {{ $t('POList.Reopen') }}
                    </el-button>
                    <el-button size="small" @click="toWCSUpDate()" v-if="detailobj.SendWcs == '1'" class="tablebtn" style="margin-left: 5px; width: 170px">
                        {{ $t('POList.WCSUpDate') }}
                    </el-button>
                    <el-button size="small" @click="toRebuildBatch()" v-if="detailobj.PoStatus == '2'" class="tablebtn" style="margin-left: 5px; width: 170px">
                        {{ $t('POList.constructingbatches') }}
                    </el-button>
                    <el-button size="small" @click="toBindPoRecipe()" class="tablebtn" style="margin-left: 5px; width: 170px">
                        {{ $t('POList.BindPoRecipe') }}
                    </el-button>
                </div>
            </div>
            <el-tabs v-model="activeName" @tab-click="tabChange" type="border-card">
                <el-tab-pane :label="this.$t('POList.Execute')" name="Execute">
                    <execute :ProductionOrderNo="detailobj.ID" ref="execute"></execute>
                </el-tab-pane>
                <el-tab-pane :label="this.$t('POList.batch')" name="batch">
                    <batch :ProductionOrderNo="detailobj.ID" ref="batch"></batch>
                </el-tab-pane>
                <el-tab-pane :label="this.$t('POList.consume')" name="consume"><consume :ProductionOrderNo="detailobj.ID" ref="consume"></consume></el-tab-pane>
                <el-tab-pane :label="this.$t('POList.produce')" name="produce"><produce :ProductionOrderNo="detailobj.ID" ref="produce"></produce></el-tab-pane>
                <el-tab-pane :label="this.$t('POList.parameter')" name="parameter"><parameter :ProductionOrderNo="detailobj.ID" ref="parameter"></parameter></el-tab-pane>
                <el-tab-pane :label="this.$t('POList.formula')" name="formula"><formula :ProductionOrderNo="detailobj.ID" ref="formula"></formula></el-tab-pane>
                <el-tab-pane :label="this.$t('POList.Processlongtext')" name="Processlongtext">
                    <processlongtext :ProductionOrderNo="detailobj.ID" :NeedQARelease="detailobj.NeedQARelease" :PoStatus="detailobj.PoStatus" ref="Processlongtext"></processlongtext>
                </el-tab-pane>
                <el-tab-pane :label="this.$t('POList.property')" name="property">
                    <property :ProductionOrderNo="detailobj.ID" ref="property"></property>
                </el-tab-pane>
            </el-tabs>
        </el-drawer>
        <el-dialog :title="$t('POList.WCSUpDate')" id="Editdialog" :visible.sync="WCSModel" width="650px">
            <div class="splitdetailbox">
                <div class="dialogdetailbox" v-for="(item, index) in WCSinputlist" :key="index">
                    <div class="dialogdetailsinglelabel" style="font-weight: 500">{{ item.label }}</div>
                    <div class="dialogdetailsinglevalue">
                        <el-input onkeyup="value=value.replace(/^0+|[^0-9\.]/g, '')" v-if="item.type == 'number'" v-model="item.value">
                            <template slot="append">{{ detailobj.Unit }}</template>
                        </el-input>
                        <el-input v-else-if="item.type == 'input'" v-model="item.value"></el-input>
                        <span v-else>{{ item.value }}</span>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="tablebtn" icon="el-icon-check-outline" @click="WCSSave()">
                    {{ $t('GLOBAL._QD') }}
                </el-button>
                <el-button @click="WCSModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>

        <el-dialog :title="$t('POList.EditTitle')" id="Editdialog" :visible.sync="EditModel" width="650px">
            <div class="splitdetailbox">
                <div class="dialogdetailbox" v-for="(item, index) in Editlist" :key="index">
                    <div class="dialogdetailsinglelabel">{{ item.label }}</div>
                    <div class="dialogdetailsinglevalue">
                        <el-input onkeyup="value=value.replace(/^0+|[^0-9\.]/g, '')" v-if="item.type == 'input'" v-model="item.value">
                            <template slot="append">{{ $t('POList.KGHOUR') }}</template>
                        </el-input>
                        <span v-else>{{ item.value }}</span>
                    </div>
                </div>
            </div>
            <div class="splitdetailbox">
                <div class="dialogdetailbox" v-for="(item, index) in Editinputlist" :key="index">
                    <div class="dialogdetailsinglelabel" style="font-weight: 500">{{ item.label }}</div>
                    <div class="dialogdetailsinglevalue">
                        <el-input onkeyup="value=value.replace(/^0+|[^0-9\.]/g, '')" v-if="item.type == 'input'" v-model="item.value">
                            <template slot="append">{{ detailobj.Unit }}</template>
                        </el-input>
                        <el-select clearable v-else-if="item.type == 'select'" v-model="item.value" filterable>
                            <el-option v-for="(it, ind) in item.options" :key="ind" :label="it.label" :value="it.value"></el-option>
                        </el-select>
                        <el-input v-else-if="item.type == 'textArea'" type="textarea" autosize v-model="item.value"></el-input>
                        <span v-else>{{ item.value }}</span>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="tablebtn" icon="el-icon-edit-outline">
                    {{ $t('POList.Edit') }}
                </el-button>
                <el-button @click="EditModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>

        <el-dialog :title="$t('POList.AddMaterialPreShift')" :visible.sync="ShiftListModal">
            <div class="dialogdetailbox">
                <div class="dialogdetailsinglelabel" style="font-weight: 500">{{ $t('POList.MaterialPreShift') }}*</div>
                <div class="dialogdetailsinglevalue">
                    <el-select v-model="ShiftID">
                        <el-option v-for="it in ShiftList" :key="it.ID" :label="it.Name" :value="it.ID"></el-option>
                    </el-select>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="ShiftListModal = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
                <el-button class="tablebtn" icon="el-icon-edit-outline" @click="ShiftSave()">
                    {{ $t('GLOBAL._QR') }}
                </el-button>
            </span>
        </el-dialog>
        <el-dialog :title="$t('POList.Complete')" :visible.sync="CompleteModel" width="650px">
            <div class="dialogdetailbox" v-for="(item, index) in Completelist" :key="index">
                <div class="dialogdetailsinglelabel">{{ item.label }}{{ item.require ? ' *' : '' }}</div>
                <div class="dialogdetailsinglevalue">
                    <el-select @change="getData2(item)" v-model="item.value" clearable filterable v-if="item.type == 'select'">
                        <el-option v-for="(it, ind) in item.options" :key="ind" :label="it.label" :value="it.key"></el-option>
                    </el-select>
                    <span v-else>{{ item.value }}</span>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="CompleteModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
                <el-button class="tablebtn" icon="el-icon-edit-outline" @click="CompleteSave()">
                    {{ $t('GLOBAL._QR') }}
                </el-button>
            </span>
        </el-dialog>
        <el-dialog :title="$t('Inventory.Print')" id="Startdialog" :visible.sync="StartModel" width="650px">
            <div class="splitdetailbox">
                <div class="dialogdetailbox" v-for="(item, index) in Startlist" :key="index" v-show="!item.notShow">
                    <div class="dialogdetailsinglelabel" :style="{ width: item.type == 'BatchCode' ? '20%' : '20%' }">{{ item.label }}{{ item.require ? ' *' : '' }}</div>
                    <div class="dialogdetailsinglevalue longwidthinput" :style="{ width: item.type == 'BatchCode' ? '400px' : '77%' }">
                        <el-input v-if="item.type == 'input'" v-model="item.value" :disabled="item.disabled"></el-input>
                        <div v-else-if="item.type == 'BatchCode'" style="display: flex">
                            <el-input @change="getQrCode()" v-model="item.value"></el-input>
                            <el-input v-model="item.value2" disabled></el-input>
                            <el-input @change="getQrCode()" v-model="item.value3"></el-input>
                            <el-button class="tablebtn" @click="getBatchCode()" size="mini" style="margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff" icon="el-icon-refresh"></el-button>
                        </div>
                        <el-select @change="GetData()" clearable v-else-if="item.type == 'select'" v-model="item.value" filterable>
                            <el-option v-for="(it, ind) in item.options" :key="ind" :label="it.value" :value="it.key"></el-option>
                        </el-select>
                        <el-date-picker
                            @change="GetData()"
                            v-else-if="item.type == 'date'"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            :disabled="item.disabled"
                            v-model="item.value"
                            type="datetime"
                        ></el-date-picker>
                        <span v-else>{{ chooseItem.TargetQuantity }}{{ chooseItem.Unit1 }}</span>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="tablebtn" icon="el-icon-video-play" @click="SendColos()">
                    {{ $t('Overview.SendColos') }}
                </el-button>
                <el-button @click="StartModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
        <el-dialog :title="$t('Consume.Scan')" id="SSCCdialog" :visible.sync="SSCCModel" width="650px">
            <div style="display: flex">
                <div :style="'100%'">
                    <div class="dialogdetailbox">
                        <div class="dialogdetailsinglelabel" :style="{ width: '20%' }">{{ $t('Consume.SSCC') + ' *' }}</div>
                        <div class="dialogdetailsinglevalue longwidthinput" :style="{ width: '77%' }">
                            <el-input v-model="SSCCValue" @keyup.enter.native="SearchSscc()">
                                <template slot="append"><i slot="suffix" class="el-icon-full-screen" @click="SearchSscc()"></i></template>
                            </el-input>
                        </div>
                    </div>
                    <div class="dialogdetailbox">
                        <div class="dialogdetailsinglelabel" :style="{ width: '20%' }">{{ $t('Overview.Batch') + ' *' }}</div>
                        <div class="dialogdetailsinglevalue longwidthinput" :style="{ width: '77%' }">
                            <el-select clearable v-model="BatchId" filterable>
                                <el-option v-for="it in SegmentBatchList" :key="it.key" :label="it.value" :value="it.key"></el-option>
                            </el-select>
                        </div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="tablebtn" icon="el-icon-refresh-left" @click="ShowQRCode()">
                    {{ $t('Consume.Scan') }}
                </el-button>
                <el-button class="tablebtn" icon="el-icon-zoom-in" @click="SearchSscc()">
                    {{ $t('Consume.Search') }}
                </el-button>
                <el-button @click="SSCCModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
        <QRcode ref="QRcode" @getQRcodesRes="getQRcodesRes"></QRcode>
        <el-dialog id="Consumedialog" :visible.sync="ConsumeModel" width="650px">
            <span slot="title" class="dialog-title">
                <div class="dialogtitlebox">{{ $t('Consume.Consume') }}</div>
            </span>
            <div class="splitdetailbox">
                <div class="splitdetailbox">
                    <div class="dialogdetailbox" v-for="(item, index) in Consumelist" :key="index">
                        <div class="dialogdetailsinglelabel">{{ item.label }}</div>
                        <div class="dialogdetailsinglevalue">
                            <span>{{ item.value }}</span>
                        </div>
                    </div>
                </div>
                <div class="splitdetailbox">
                    <div class="dialogdetailbox" v-for="(item, index) in Consumeinputlist" :key="index">
                        <div class="dialogdetailsinglelabel">{{ item.label }}{{ item.require ? ' *' : '' }}</div>
                        <div class="dialogdetailsinglevalue">
                            <el-input type="number" v-if="item.id == 'Quantity'" v-model="item.value">
                                <template slot="append">{{ ConsumeObj.ChangeUnit }}</template>
                            </el-input>
                        </div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="tablebtn" icon="el-icon-folder-checked" @click="ConsumeSave()">
                    {{ $t('GLOBAL._BC') }}
                </el-button>
                <el-button @click="ConsumeModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
        <el-dialog :title="$t('POList.ThroatOutput')" :visible.sync="ThroatOutputModal" width="500px">
            <div class="dialogdetailbox" style="margin-top: 15px">
                <div class="dialogdetailsinglelabel" style="font-weight: 500; width: 45%">{{ $t('PackagingWorkOrder.Order_Sort.PlanStartTime') }}*</div>
                <div class="dialogdetailsinglevalue" style="width: 100%">
                    <el-date-picker v-model="PlanStartTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder=""></el-date-picker>
                </div>
            </div>
            <div class="dialogdetailbox">
                <div class="dialogdetailsinglelabel" style="font-weight: 500; width: 45%">{{ $t('POList.SourceStroage') }}*</div>
                <div class="dialogdetailsinglevalue" style="width: 100%">
                    <el-select v-model="Source" filterable>
                        <el-option v-for="it in SourceList" :key="it.key" :label="it.value" :value="it.key"></el-option>
                    </el-select>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="ThroatOutputModal = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
                <el-button class="tablebtn" icon="el-icon-edit-outline" @click="ThroatOutputSave()">
                    {{ $t('GLOBAL._QR') }}
                </el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import '@/views/Inventory/mystyle.scss';
import { WLPOlist } from '@/columns/factoryPlant/tableHeaders';
import {
    GetListViewList,
    GetProductionUpdateShift,
    GetProductionShiftSelect,
    GetLastProcessData,
    UpdateMaterialProcessDataStatus,
    toSendOrderInfoToSS,
    GettoRelease,
    UpdatePoStatus,
    RebuildBatch,
    BindPoRecipe,
    OperationPo,
    MyGetEquipmentsByOrderId,
    MySendLabelPrintToColos,
    MyGetQrCode,
    getDataDictionary,
    GetSegmentBatchList
} from '@/api/Producting/POlist.js';
import { GetPrinit2 } from '@/api/Inventory/common.js';
import { GetProduceOpen, GetBPEquipmentsSelect, GetBatchCode, ConsumeScanSSCC, ConsumeViewSave, getConsumeViewEntity, ProduceLocation, ProduceSave } from '@/api/Inventory/Overview.js';
import { GetDataTreeList } from '@/api/factoryPlant/process.js';
import { Message, MessageBox } from 'element-ui';
import moment from 'moment';
import { GetMSelectListClass, GetUnitList } from '@/api/Inventory/Inventory.js';
export default {
    components: {
        execute: () => import('./components/Execute'),
        consume: () => import('./components/consume'),
        batch: () => import('./components/batch'),
        parameter: () => import('./components/parameter'),
        formula: () => import('./components/formula'),
        produce: () => import('./components/produce'),
        processlongtext: () => import('./components/Processlongtext'),
        property: () => import('./components/property')
    },
    data() {
        return {
            ThroatOutputModal: false,
            PlanStartTime: null,
            Source: '',
            SourceList: [],
            StartModel: false,
            WCSModel: false,
            WCSinputlist: [
                {
                    label: this.$t('POList.Num'),
                    id: 'quantity',
                    require: true,
                    value: '',
                    type: 'number'
                },
                {
                    label: this.$t('TRACE_WGJC._PCH'),
                    id: 'lotCode',
                    require: true,
                    value: '',
                    type: 'input'
                }
            ],
            Startlist: [
                {
                    notShow: true,
                    label: this.$t('ANDON_BJZY.EquipmentName'),
                    id: 'EquipmentId',
                    value: '',
                    // require: true,
                    type: 'select',
                    options: []
                },
                {
                    label: this.$t('Overview.ProductionDate'),
                    id: 'ProductionDate',
                    require: true,
                    value: '',
                    type: 'date'
                },
                {
                    label: this.$t('Overview.BatchCode'),
                    id: 'LotCode',
                    require: true,
                    value: '',
                    value2: '',
                    value3: '',
                    type: 'BatchCode'
                },
                {
                    label: this.$t('Overview.QrCode'),
                    id: 'QrCode',
                    disabled: true,
                    value: '',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.INV_TPQD.PrintCount'),
                    id: 'PrintCount',
                    require: true,
                    value: '',
                    type: 'input'
                }
            ],
            showFrom: true,
            timepicker: [this.getDay(0), this.getDay(3)],
            tableheader: WLPOlist,
            tableId: 'PRO_POLIST',
            QuickSearch: '',
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            searchlist: [
                {
                    type: 'select',
                    name: this.$t('POList.Status'),
                    id: 'Available',
                    value: [],
                    option: []
                },
                {
                    type: 'input',
                    value: '',
                    id: 'MaterialDescription',
                    name: this.$t('POList.Material')
                },
                {
                    type: 'input',
                    value: '',
                    id: 'Resource',
                    name: this.$t('POList.Source')
                },
                {
                    type: 'input',
                    value: '',
                    id: 'ProductionOrderNo',
                    name: this.$t('POList.ProcessOrder')
                },
                {
                    type: 'input',
                    value: '',
                    id: 'Formula',
                    name: this.$t('$vuetify.dataTable.PRO_POLIST.Formula')
                },
                {
                    type: 'input',
                    value: '',
                    id: 'SegmentCode',
                    name: this.$t('POList.SegmentCode')
                }
            ],
            tableList: [],
            activeName: 'Execute',
            detailShow: false,
            detailobj: {},
            ProduceModel: false,
            BtnObj: {},
            Producelist: [
                {
                    label: this.$t('Consume.ProcessOrder'),
                    value: '',
                    id: 'ProductionOrderNo'
                },
                {
                    label: this.$t('Consume.Source'),
                    value: '',
                    id: 'Source'
                },
                {
                    label: this.$t('Consume.Material'),
                    value: '',
                    id: 'MaterialDescription'
                }
            ],
            Produceinputlist: [
                {
                    label: this.$t('Consume.ProductionDate'),
                    id: 'ProductionDate',
                    value: '',
                    type: 'date',
                    datetype: 'datetime'
                },
                {
                    label: this.$t('Consume.ExpirationDate'),
                    id: 'ExpirationDate',
                    value: '',
                    type: 'date',
                    disable: true,
                    datetype: 'datetime'
                },
                {
                    label: this.$t('Consume.Batch'),
                    id: 'BatchCode',
                    value: '',
                    disable: true,
                    type: 'input'
                },
                {
                    label: this.$t('Consume.Location'),
                    id: 'Location',
                    value: '',
                    type: 'select',
                    option: [],
                    require: true
                },
                {
                    label: this.$t('Consume.Quantity'),
                    id: 'Quantity',
                    value: '',
                    type: 'inputNumber',
                    require: true
                },
                {
                    label: this.$t('Consume.printlabel'),
                    id: 'printlabel',
                    value: false,
                    type: 'switch'
                }
            ],
            selectprinter: '',
            selectprinterOption: [],
            detailArr: [],
            EditModel: false,
            Editlist: [
                {
                    label: this.$t('POList.ProductionOrderNo'),
                    value: '',
                    id: 'ProductionOrderNo'
                },
                {
                    label: this.$t('POList.Source'),
                    value: '',
                    id: 'Source'
                },
                {
                    label: this.$t('POList.Material'),
                    value: '',
                    id: 'Material'
                },
                {
                    label: this.$t('POList.MaterialVersion'),
                    value: '',
                    id: 'MaterialVersionNumber'
                },
                {
                    label: this.$t('POList.PlanStartTime'),
                    value: '',
                    id: 'PlanStartTime'
                },
                {
                    label: this.$t('POList.PlanEndTime'),
                    value: '',
                    id: 'PlanEndTime'
                },
                {
                    type: 'input',
                    label: this.$t('POList.Speed'),
                    value: '',
                    id: 'Speed'
                }
            ],
            Editinputlist: [
                {
                    label: this.$t('POList.Status'),
                    id: 'Status',
                    value: '',
                    type: 'select',
                    options: []
                },
                {
                    label: this.$t('POList.Num'),
                    id: 'Num',
                    value: '',
                    type: 'input'
                }
            ],
            StatusList: [],
            ReasonList: [],
            ShiftList: [],
            selectTabelData: [],
            tablechooselist: 0,
            ShiftID: '',
            ShiftListModal: false,
            checkRow: {},
            CompleteModel: false,
            SSCCModel: false,
            SSCCValue: '',
            BatchId: '',
            ConsumeModel: false,
            ConsumeObj: {},
            SegmentBatchList: [],
            Consumelist: [
                {
                    label: this.$t('Consume.ProcessOrder'),
                    id: 'ProcessOrderNo',
                    value: ''
                },
                {
                    label: this.$t('Consume.Location'),
                    id: 'Location',
                    value: ''
                },
                {
                    label: this.$t('Consume.Material'),
                    id: 'Material',
                    value: ''
                },
                {
                    label: this.$t('Consume.Lot'),
                    id: 'Batch',
                    value: ''
                },
                {
                    label: this.$t('Consume.SSCC'),
                    id: 'Sscc',
                    value: ''
                },
                {
                    label: this.$t('Consume.Quantity'),
                    id: 'QuantitywithUnit',
                    value: ''
                }
            ],
            Consumeinputlist: [
                {
                    label: this.$t('Consume.Quantity'),
                    value: '',
                    id: 'Quantity',
                    require: true
                }
            ],
            detailList: [
                {
                    label: this.$t('Consume.Material'),
                    value: '',
                    id: 'Material'
                },
                {
                    label: this.$t('Consume.StorageBin'),
                    value: '',
                    id: 'StorageBin'
                },
                {
                    label: this.$t('Consume.Required'),
                    value: '',
                    id: 'Quantity2'
                },
                {
                    label: this.$t('Consume.Consumptions'),
                    value: '',
                    id: 'Quantity1'
                },
                {
                    label: this.$t('Consume.Remaining'),
                    value: '',
                    id: 'Remaining'
                },
                {
                    label: this.$t('Consume.Uom'),
                    value: '',
                    id: 'Unit1'
                }
            ],
            Completelist: [
                {
                    label: this.$t('POList.Material'),
                    id: 'Material',
                    value: ''
                },
                {
                    label: this.$t('POList.PlanQty'),
                    id: 'PlanQuantity',
                    value: ''
                },
                {
                    label: this.$t('POList.ActualQty'),
                    id: 'ActualQuantity',
                    value: ''
                },
                {
                    label: this.$t('POList.ProduceStatus'),
                    id: 'ProduceStatus',
                    require: true,
                    type: 'select',
                    options: [
                        {
                            key: 'NotComplete',
                            label: this.$t('POList.NotComplete')
                        },
                        {
                            key: 'OverComplete',
                            label: this.$t('POList.OverComplete')
                        },
                        {
                            key: 'CompleteAtOnce',
                            label: this.$t('POList.CompleteAtOnce')
                        }
                    ],
                    value: ''
                },
                {
                    label: this.$t('POList.Reason'),
                    id: 'Reason',
                    type: 'select',
                    require: true,
                    options: [],
                    value: ''
                }
            ]
        };
    },
    mounted() {
        this.getSourceList();
        this.getStatus();
        this.GetShiftSelect();
        this.getprintList();
    },
    methods: {
        async Reopen() {
            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_Reopen')}`, '', {
                confirmButtonText: `${this.$t('GLOBAL._QD')}`,
                cancelButtonText: `${this.$t('GLOBAL._GB')}`,
                type: 'warning'
            }).then(async () => {
                let params = {
                    Id: this.detailobj.ID,
                    Status: 6
                };
                let res = await UpdatePoStatus(params);
                this.detailShow = false;
                this.getPageList();
                Message({
                    message: res.msg,
                    type: 'success'
                });
            });
        },
        async ThroatOutputSave() {
            if (this.Source == '' || this.PlanStartTime == '') {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'warning'
                });
                return;
            }
            let params = {
                EquipmentId: this.Source,
                ProductionDate: this.PlanStartTime
            };
            let res = await GetProduceOpen(params);
            let data = res.response;
            if (data == null) {
                Message({
                    message: `${this.$t('POList.NotFoundPO')}`,
                    type: 'warning'
                });
                return;
            } else {
                this.ProduceOpen(data);
                this.ThroatOutputModal = false;
            }
        },
        async getSourceList() {
            let res = await GetBPEquipmentsSelect();
            this.SourceList = res.response;
        },
        ToThroatOutput() {
            this.PlanStartTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.Source = '';
            this.ThroatOutputModal = true;
        },
        ShowQRCode() {
            this.$refs.QRcode.getQRcode();
        },
        getQRcodesRes(val) {
            this.SSCCValue = val.text;
            this.SearchSscc();
        },
        async getprintList() {
            let res = await GetPrinit2();
            res.response.forEach(item => {
                item.value = item.ID;
                item.label = item.Code;
                item.ItemName = item.Code;
                item.ItemValue = item.ID;
            });
            this.selectprinterOption = res.response;
        },
        async SaveProduce() {
            if (this.Produceinputlist[5].value === true && this.selectprinter === '') {
                Message({
                    message: `${this.$t('Inventory.PleaseSelectPrinter')}`,
                    type: 'warning'
                });
                return;
            }
            let flag = this.Produceinputlist.some(item => {
                if (item.require) {
                    return item.value == '';
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'warning'
                });
                return;
            }
            let params = {
                ExpirationDate: this.Produceinputlist[1].value,
                ProductionDate: this.Produceinputlist[0].value,
                BatchId: this.BtnObj.BatchId,
                Location: this.Produceinputlist[3].value,
                Quantity: this.Produceinputlist[4].value,
                EquipmentId: this.BtnObj.RunEquipmentId,
                ExecutionId: this.BtnObj.ExecutionId,
                UnitId: this.BtnObj.UnitId,
                IsPrint: this.Produceinputlist[5].value,
                PrintId: this.selectprinter
            };
            let res = await ProduceSave(params);
            Message({
                message: res.msg,
                type: 'success'
            });
            this.getPageList();
            this.ProduceModel = false;
        },
        async ProduceOpen(row) {
            this.Produceinputlist[3].value = '';
            this.BtnObj = row;
            let params = {
                EquipmentId: row.RunEquipmentId
            };
            this.selectprinter = '';
            let res = await ProduceLocation(params);
            res.response.forEach(item => {
                item.label = item.LocationName;
                item.value = item.LocationId;
                if (item.IsDefault == '1') {
                    this.Produceinputlist[3].value = item.LocationId;
                }
            });
            this.Produceinputlist[3].option = res.response;
            if (this.selectprinterOption.length != 0) {
                this.selectprinter = this.selectprinterOption[0].value;
            }
            for (let k in row) {
                this.Producelist.forEach(item => {
                    if (item.id == k) {
                        if (k == 'MaterialDescription') {
                            item.value = row.MaterialDescription + '-' + row.MaterialCode;
                        } else {
                            item.value = row[k];
                        }
                    }
                });
            }
            this.Produceinputlist.forEach(item => {
                if (item.id == 'printlabel') {
                    item.value = false;
                } else if (item.id != 'Location') {
                    item.value = '';
                }
            });
            this.Produceinputlist[0].value = row.ProductionDate;
            this.Produceinputlist[1].value = row.ExpirationDate;
            this.Produceinputlist[2].value = row.BatchCode;
            this.ProduceModel = true;
        },
        checkSelectable(row) {
            if (row.PoStatus == '1' || row.PoStatus == '2') {
                return true;
            } else {
                return false;
            }
        },
        toWCSUpDate() {
            this.WCSinputlist.forEach(item => {
                item.value = '';
            });
            this.WCSModel = true;
        },
        toColos(row) {
            this.checkRow = row;
            this.Startlist.forEach((item, index) => {
                if (index == 1) {
                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
                } else if (index == 2) {
                    item.value2 = '';
                    item.value3 = '';
                } else {
                    item.value = '';
                }
            });
            this.getBatchCode();
            this.StartModel = true;
            // this.getEquipmentList();
        },
        async WCSSave() {
            let flag = this.WCSinputlist.some(item => {
                if (item.require) {
                    return item.value == '';
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'warning'
                });
                return;
            }
            let obj = {};
            this.WCSinputlist.forEach(item => {
                obj[item.id] = item.value;
            });
            obj.actionType = 3;
            obj.productionOrderId = this.detailobj.ID;
            let res = await toSendOrderInfoToSS(obj);
            this.getPageList();
            this.WCSModel = false;
            Message({
                message: res.msg,
                type: 'success'
            });
        },
        // async getEquipmentList() {
        //     let id = JSON.stringify(this.checkRow.ID);
        //     let res = await MyGetEquipmentsByOrderId(id);
        //     res.response.forEach(item => {
        //         item.key = item.key + '|' + item.value;
        //     });
        //     this.Startlist[0].options = res.response;
        //     this.StartModel = true;
        // },
        GetData() {
            if (this.Startlist[1].value != '') {
                this.getBatchCode();
            }
        },
        ScanOpen(row) {
            this.SSCCValue = '';
            this.SSCCModel = true;
            this.GetSegmentBatchList(row);
        },
        async SearchSscc(row) {
            if (row) {
                this.SSCCValue = row.Sscc;
            }
            if (this.SSCCValue === '' || this.SSCCValue === null) {
                Message({
                    message: `${this.$t('ConsumptionHistory.NonSSCC')}`,
                    type: 'error'
                });
                return;
            }
            if (this.BatchId === '' || this.BatchId === null) {
                Message({
                    message: `${this.$t('ConsumptionHistory.NoBatchId')}`,
                    type: 'error'
                });
                return;
            }
            let params = {
                BatchId: this.BatchId,
                Sscc: this.SSCCValue
            };
            let res = await ConsumeScanSSCC(params);
            let data = res.response;
            this.Id = data.ID;
            if (data.Location2) {
                data.Location = data.Location1Code + '-' + data.Location2;
            }
            if (data.MaterialCode) {
                data.Material = data.MaterialName + '-' + data.MaterialCode;
            }
            if (data.Quantity) {
                data.QuantitywithUnit = data.Quantity + ' ' + data.Unit1;
            }
            this.ConsumeObj = data;
            for (let k in data) {
                this.Consumelist.forEach(item => {
                    if (item.id == k) {
                        item.value = data[k];
                    }
                });
            }
            let v = this.ConsumeObj.ChangeUnit.toLowerCase() === 'g' ? 1000 : 1;
            this.Consumeinputlist[0].value = data.Quantity * v;
            //this.Consumeinputlist[0].value = data.Quantity;

            await this.GetConsumeViewEntity(data.ID);

            this.SSCCModel = false;
            this.ConsumeModel = true;
        },
        async GetConsumeViewEntity(id) {
            let res = await getConsumeViewEntity(id);
            if (res) {
                var obj = res.response;
                if (obj.MaterialName) {
                    obj.Material = obj.MaterialName + '-' + obj.MaterialCode;
                }
                if (obj.Quantity2) {
                    obj.Remaining = (Number(obj.Quantity2) - Number(obj.Quantity1)).toFixed(2);
                }
                this.detailobj = obj;
                for (let k in obj) {
                    this.detailList.forEach(item => {
                        if (item.id == k) {
                            item.value = obj[k];
                        }
                    });
                }
            }
        },
        async ConsumeSave() {
            let v = this.ConsumeObj.ChangeUnit.toLowerCase() === 'g' ? 1000 : 1;
            if (this.Consumeinputlist[0].value == 0) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'warning'
                });
                return false;
            }
            if (Number(this.Consumeinputlist[0].value) > Number(this.ConsumeObj.Quantity * v)) {
                Message({
                    message: `${this.$t('Consume.Over')}`,
                    type: 'warning'
                });
                return false;
            }
            if (Number(this.Consumeinputlist[0].value) > Number(this.detailList[4].value)) {
                Message({
                    message: `${this.$t('Consume.Over2')}`,
                    type: 'warning'
                });
            }
            let params = {
                BatchId: this.BatchId,
                Sscc: this.SSCCValue,
                Quantity: this.Consumeinputlist[0].value,
                IsNotCheckRunOrder: true
            };
            let res = await ConsumeViewSave(params);
            this.BatchId = '';
            this.SSCCValue = '';
            this.ConsumeModel = false;
            this.Consumeinputlist[0].value = '';
            Message({
                message: res.msg,
                type: 'success'
            });
        },
        async GetSegmentBatchList(row) {
            this.BatchId = '';
            this.SegmentBatchList = [];
            let p = {
                ProductionOrderId: row.ID
            };
            let res = await GetSegmentBatchList(p);
            this.SegmentBatchList = res.response;
        },
        async getQrCode() {
            let code = this.Startlist[2].value + this.Startlist[2].value2 + this.Startlist[2].value3;
            let res = await MyGetQrCode('', this.checkRow.ID, code);
            this.Startlist[3].value = res.response;
        },
        async SendColos() {
            let flag = this.Startlist.some(item => {
                if (item.require) {
                    return item.value == '';
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'warning'
                });
                return;
            }
            let params = {
                poId: this.checkRow.ID,
                batchCode: this.Startlist[2].value + this.Startlist[2].value2 + this.Startlist[2].value3,
                count: this.Startlist[3].value
            };
            if (params.batchCode.length > 10) {
                Message({
                    message: `${this.$t('Overview.BatchCodeLong')}`,
                    type: 'warning'
                });
                return;
            }
            let res = await MySendLabelPrintToColos(params);
            this.getPageList();
            this.StartModel = false;
            Message({
                message: res.msg,
                type: 'success'
            });
        },
        async getBatchCode() {
            let LineCode = this.checkRow.LineCode;
            let productionId = this.checkRow.ID;
            let date = moment(this.Startlist[1].value).format('YYYY-MM-DD HH:mm:ss');
            let p = {
                LineCode: this.Startlist[2].value,
                equipmentCode: LineCode,
                productionDate: date,
                productionId: productionId
            };
            let res = await GetBatchCode(p);
            if (res.response == null) {
                Message({
                    message: res.msg,
                    type: 'warning'
                });
            } else {
                this.Startlist[2].value = res.response.substring(0, 2);
                this.Startlist[2].value2 = res.response.substring(2, 5);
            }
        },
        async GetShiftSelect() {
            let res = await GetProductionShiftSelect();
            this.ShiftList = res.response;
        },
        handleSelectionChange(val) {
            this.selectTabelData = val;
            this.tablechooselist = val.length;
        },
        addNew() {
            this.ShiftID = '';
            this.ShiftListModal = true;
        },
        async ShiftSave() {
            if (this.ShiftID == '') {
                Message({
                    message: this.$t('POList.CHECKMaterialPreShift'),
                    type: 'warning'
                });
                return;
            }
            let arr = this.selectTabelData.map(item => {
                return item.ID;
            });
            let params = {
                ShiftID: this.ShiftID,
                ProIDS: arr
            };
            let res = await GetProductionUpdateShift(params);
            Message({
                message: res.msg,
                type: 'success'
            });
            this.ShiftListModal = false;
            this.getPageList();
        },
        async toRebuildBatch(isPl) {
            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_GJPC')}`, '', {
                confirmButtonText: `${this.$t('GLOBAL._QD')}`,
                cancelButtonText: `${this.$t('GLOBAL._GB')}`,
                type: 'warning'
            }).then(async () => {
                let arr = [];
                if (isPl) {
                    arr = this.selectTabelData.map(item => {
                        return item.ID;
                    });
                } else {
                    arr = [this.detailobj.ID];
                }
                let res = await RebuildBatch(arr);
                this.detailShow = false;
                this.getPageList();
                Message({
                    message: res.msg,
                    type: 'success'
                });
            });
        },
        async toBindPoRecipe(isPl) {
            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_BDPF')}`, '', {
                confirmButtonText: `${this.$t('GLOBAL._QD')}`,
                cancelButtonText: `${this.$t('GLOBAL._GB')}`,
                type: 'warning'
            }).then(async () => {
                let arr = [];
                if (isPl) {
                    arr = this.selectTabelData.map(item => {
                        return item.ID;
                    });
                } else {
                    arr = [this.detailobj.ID];
                }
                let res = await BindPoRecipe(arr);
                this.detailShow = false;
                this.getPageList();
                Message({
                    message: res.msg,
                    type: 'success'
                });
            });
        },
        async toRelease() {
            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_SF')}`, '', {
                confirmButtonText: `${this.$t('GLOBAL._QD')}`,
                cancelButtonText: `${this.$t('GLOBAL._GB')}`,
                type: 'warning'
            }).then(async () => {
                let obj = {
                    ID: this.detailobj.ID
                };
                if (this.detailobj.NeedQARelease == '0') {
                    let parmas = {
                        key: '2',
                        body: JSON.stringify(obj)
                    };
                    let res = await OperationPo(parmas);
                    this.getPageList();
                    this.$refs[this.activeName].getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);
                    Message({
                        message: res.msg,
                        type: 'success'
                    });
                } else {
                    let parmas = {
                        key: '5',
                        body: JSON.stringify(obj)
                    };
                    let res = await OperationPo(parmas);
                    this.getPageList();
                    this.$refs[this.activeName].getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);
                    Message({
                        message: res.msg,
                        type: 'success'
                    });
                }
            });
        },
        async getData2(item) {
            if (item.id == 'ProduceStatus') {
                if (item.value != '') {
                    let res = await this.$getNewDataDictionary(item.value);
                    //let res = this.ReasonList.find(x=>x.ItemCode = item.value)
                    // console.log(res);
                    let data = res;
                    if (data.length > 0) {
                        data.forEach(item1 => {
                            item1.key = item1.ItemValue;
                            // console.log(this._i18n.locale);
                            item1.label = this._i18n.locale === 'en' ? item1.ItemValue : item1.ItemName;
                        });
                    }
                    this.Completelist[4].options = data;
                    console.log(this.Completelist[4].options);
                    this.Completelist[4].value = '';
                }
            }
        },
        async CompleteSave() {
            let flag = this.Completelist.some(item => {
                if (item.require) {
                    return item.value == '' || item.value == null;
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'warning'
                });
                return;
            }
            let params = {
                Id: this.detailobj.ID,
                Status: 3,
                ProduceStatus: this.Completelist[3].value,
                Reason: this.Completelist[4].value
            };
            let res = await UpdatePoStatus(params);
            this.detailShow = false;
            this.CompleteModel = false;
            this.getPageList();
            Message({
                message: res.msg,
                type: 'success'
            });
        },
        toComplete() {
            this.detailobj.Material = this.detailobj.MaterialDescription + '-' + this.detailobj.MaterialCode;
            this.detailobj.PlanQuantity = this.detailobj.PlanQty + this.detailobj.Unit;
            this.detailobj.ActualQuantity = this.detailobj.ActualQty + this.detailobj.Unit;
            this.Completelist.forEach(item => {
                item.value = '';
            });
            //console.log(this.Completelist[4]);
            this.Completelist.forEach(item => {
                for (let k in this.detailobj) {
                    if (item.id == k) {
                        item.value = this.detailobj[k];
                    }
                }
            });
            this.Completelist[4].options = [];
            let PlanQuantity = Number(this.detailobj.PlanQty);
            let ActualQuantity = Number(this.detailobj.ActualQty);
            if (PlanQuantity > ActualQuantity) {
                this.Completelist[3].value = 'NotComplete';
            } else if (PlanQuantity == ActualQuantity) {
                this.Completelist[3].value = 'CompleteAtOnce';
            } else {
                this.Completelist[3].value = 'OverComplete';
            }
            //console.log(this.ReasonList);
            this.ReasonList.forEach(x => {
                if (x.ItemCode == this.Completelist[3].value) {
                    let res = this.Completelist[4].options.find(x1 => x1.ItemCode === this.Completelist[3].value && x1.ItemValue === x.ItemValue);
                    if (res === null || res === undefined || typeof res === 'undefined') {
                        x.key = x.ItemValue;
                        x.label = this._i18n.locale === 'en' ? x.ItemValue : x.ItemName;
                        this.Completelist[4].options.push(x);
                    }
                }
            });
            console.log(this.Completelist[4].options);
            this.CompleteModel = true;
        },
        editShow() {
            this.EditModel = true;
        },
        tabChange() {
            setTimeout(() => {
                this.$refs[this.activeName].getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);
            }, 200);
        },
        async detaildrawShow(obj) {
            this.activeName = 'Execute';
            this.detailobj = obj;
            let res = await GetLastProcessData('', this.detailobj.ID);
            this.detailShow = true;
            this.detailobj.Material = this.detailobj.MaterialDescription + '-' + this.detailobj.MaterialCode;
            setTimeout(() => {
                this.$refs.execute.getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);
            }, 200);
            for (let k in this.detailobj) {
                this.Editlist.forEach(item => {
                    if (item.id == k) {
                        item.value = this.detailobj[k];
                    }
                });
            }
            if (res.response.IsReminded == '0') {
                MessageBox.confirm(`${this.$t('POList.warningText')}`, '', {
                    confirmButtonText: `${this.$t('GLOBAL._QD')}`,
                    cancelButtonText: `${this.$t('GLOBAL._GB')}`,
                    closeOnClickModal: false,
                    type: 'warning'
                })
                    .then(async () => {
                        let params = {
                            IsReminded: '1',
                            ID: res.response.ID
                        };
                        let res2 = await UpdateMaterialProcessDataStatus(params);
                        Message({
                            message: res2.msg,
                            type: 'success'
                        });
                    })
                    .catch(async () => {});
            }
        },
        getDay(day) {
            var today = new Date();
            var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
            today.setTime(targetday_milliseconds); //注意，这行是关键代码
            var tYear = today.getFullYear();
            var tMonth = today.getMonth();
            var tDate = today.getDate();
            tMonth = this.doHandleMonth(tMonth + 1);
            tDate = this.doHandleMonth(tDate);
            return tYear + '-' + tMonth + '-' + tDate;
        },
        doHandleMonth(month) {
            var m = month;
            if (month.toString().length == 1) {
                m = '0' + month;
            }
            return m;
        },
        getStatusName(key) {
            if (key) {
                let name = '';
                this.StatusList.forEach(item => {
                    if (item.ItemValue == key) {
                        name = item.ItemName;
                    }
                });
                return name;
            }
        },
        getReasonName(key1, key2) {
            if (key1 === null || key1 === '' || key2 === null || key2 === '') {
                return '';
            }
            let name = key2;
            //console.log(this._i18n.locale);
            if (this._i18n.locale == 'en') {
                return name;
            }
            this.ReasonList.forEach(item => {
                if (item.ItemCode === key1 && item.ItemValue === key2) {
                    name = item.ItemName;
                }
            });
            //console.log(name);
            return name;
        },
        getStatusColor(key) {
            if (key) {
                let color = '';
                this.StatusList.forEach(item => {
                    if (item.ItemValue == key) {
                        color = item.Description;
                    }
                });
                return color;
            }
        },

        getsearch() {
            this.pageOptions.page = 1;
            this.pageOptions.pageSize = 20;
            this.getPageList();
        },
        getempty() {
            this.QuickSearch = '';
            this.timepicker = [];
            this.timepicker[0] = this.getDay(0);
            this.timepicker[1] = this.getDay(3);
            this.pageOptions.page = 1;
            this.pageOptions.pageSize = 20;
            this.searchlist.forEach(item => {
                item.value = '';
            });
            this.getPageList();
        },
        async getStatus() {
            let params = {
                ItemCode: 'ProductionOrderStatus'
            };
            const res = await GetDataTreeList(params);
            let data = res.response.data;
            this.StatusList = data;
            data.forEach(item => {
                item.label = item.ItemName;
                item.value = item.ItemValue;
            });

            this.ReasonList = [];
            this.Completelist[3].options.forEach(async item => {
                let p = {
                    ItemCode: item.key
                };
                const res = await GetDataTreeList(p);
                let data = res.response.data;
                //let res = await this.$getNewDataDictionary(item.key);
                console.log(res.response.data);
                res.response.data.forEach(item1 => {
                    this.ReasonList.push(item1);
                });
            });

            this.Editinputlist[0].options = data;
            this.searchlist[0].option = data;
            this.getPageList();
        },
        async getPageList() {
            if (this.timepicker == null) {
                this.timepicker = [];
            }
            let params = {
                NeedQARelease: '0',
                Resource: '',
                MaterialDescription: '',
                ProductionOrderNo: '',
                StatusList: this.searchlist[0].value,
                StartTime: this.timepicker[0],
                EndTime: this.timepicker[1] == undefined ? '' : this.timepicker[1] + ' 23:59:59',
                FillLineCode: '',
                Key: this.QuickSearch,
                pageIndex: this.pageOptions.page,
                pageSize: this.pageOptions.pageSize,
                orderByFileds: '',
                Formula: '',
                SegmentCode: ''
            };
            for (let k in params) {
                this.searchlist.forEach(item => {
                    if (k == item.id) {
                        params[k] = item.value;
                    }
                });
            }
            const res = await GetListViewList(params);
            if (res) {
                this.tableList = res.response.data;
            }
            // if (res.response.count == 0) {
            //     this.tableList = [];
            // } else {
            //     this.tableList = res.response.data;
            // }
            this.pageOptions.total = res.response.dataCount;
        },
        handleSizeChange(val) {
            this.pageOptions.pageSize = val;
            this.getPageList();
        },
        handleCurrentChange(val) {
            this.pageOptions.page = val;
            this.getPageList();
        }
    }
};
</script>
<style lang="scss" scope>
.PoList {
    .dialogdetailbox {
        display: flex;
        align-items: center;
        width: 100%;
        margin-bottom: 10px;
        .dialogdetailsinglelabel {
            font-weight: 600;
            width: 50%;
            text-align: right;
        }
        .dialogdetailsinglevalue {
            width: 78%;
            margin-left: 20px;
        }
    }
    .splitdetailbox {
        padding: 10px 0;
        border: 1px solid #e8e8e8;
        margin-bottom: 5px;
        .dialogdetailbox {
            display: flex;
            align-items: center;
            width: 100%;
            margin-top: 10px;
            .dialogdetailsinglelabel {
                font-weight: 600;
                width: 47%;
                text-align: right;
            }
            .dialogdetailsinglevalue {
                width: 78%;
                margin-left: 20px;
            }
        }
    }
    .drawerTitlelabel {
        color: #808080;
        font-size: 1rem;
        .statusbox {
            width: auto !important;
        }
    }
    .dialog-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .drawEditBox {
        font-size: 1.5rem;
        margin-right: 15px;
        cursor: pointer;
    }
}
.el-dialog__body {
    .el-input {
        width: 250px !important;
    }
    .el-select {
        width: 250px !important;
    }
    .el-textarea {
        width: 250px !important;
    }
    .longwidthinput {
        .el-input {
            width: 400px !important;
        }
        .el-select {
            width: 400px !important;
        }
        .el-textarea {
            width: 400px !important;
        }
    }
}
</style>
