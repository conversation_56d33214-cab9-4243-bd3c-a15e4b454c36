{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\FullBag.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\FullBag.vue", "mtime": 1750150388566}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["FullBag.vue"], "names": [], "mappings": ";AA2CA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "FullBag.vue", "sourceRoot": "src/views/Inventory/buildpalletsStart/components", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle PartialBag\">\r\n        <div class=\"tabinputbox\">\r\n            <div class=\"tabinputsinglebox\">\r\n                <el-input size=\"mini\" @change=\"getRowBySSCC\" ref=\"autoFocus\" :placeholder=\"$t('Consume.SSCC')\" v-model=\"sscc\">\r\n                    <template slot=\"append\"><i class=\"el-icon-full-screen\"></i></template>\r\n                </el-input>\r\n            </div>\r\n            <div class=\"tabinputsinglebox\" style=\"flex-direction: row; align-items: center\">\r\n                <div class=\"tabinputsinglelabel\">{{ $t('MaterialPreparationBuild.Bags') }}:</div>\r\n                <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" type=\"number\" v-model=\"Bags\"></el-input>\r\n                <!-- <div class=\"tabbtnsinglebox\" style=\"flex-direction: row; align-items: center; width: 220px\">\r\n                    <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-top\"></el-button>\r\n                    <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-bottom\"></el-button>\r\n                </div> -->\r\n            </div>\r\n            <div class=\"tabinputsinglebox\" style=\"flex-direction: row; align-items: center\">\r\n                <div class=\"tabinputsinglelabel\">{{ $t('MaterialPreparationBuild.BagWeight') }}:</div>\r\n                <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" type=\"number\" v-model=\"BagWeight\"></el-input>\r\n                <!-- <div class=\"tabbtnsinglebox\" style=\"flex-direction: row; align-items: center; width: 250px\">\r\n                    <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-top\"></el-button>\r\n                    <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-bottom\"></el-button>\r\n                </div> -->\r\n            </div>\r\n            <div class=\"tabinputsinglebox\">\r\n                <div class=\"tabbtnsinglebox\">\r\n                    <el-button\r\n                        ref=\"FullBagbtn\"\r\n                        style=\"margin-left: 5px\"\r\n                        :disabled=\"!(ssccFlag && sscc != '' && detailobj.CompleteStates != 'OK' && Bags != 0)\"\r\n                        size=\"small\"\r\n                        icon=\"el-icon-bottom\"\r\n                        class=\"tablebtn\"\r\n                        @click=\"Transfer()\"\r\n                    >\r\n                        {{ this.$t('MaterialPreparationBuild.Transfer') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { TransferFullBag } from '@/api/Inventory/MaterialPreparation.js';\r\nimport { Message, MessageBox } from 'element-ui';\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            sscc: '',\r\n            Bags: 0,\r\n            ssccFlag: false,\r\n            BagWeight: '',\r\n            detailobj: {},\r\n            SubId: '',\r\n            InQuantity: 0\r\n        };\r\n    },\r\n    mounted() {\r\n        this.detailobj = JSON.parse(this.$route.query.query);\r\n        this.BagWeight = this.detailobj.BagSize;\r\n        this.Bags = this.detailobj.BagS;\r\n        this.getbtnStatus();\r\n    },\r\n    methods: {\r\n        getbtnStatus() {\r\n            return this.ssccFlag && this.sscc != '' && this.detailobj.CompleteStates != 'OK' && this.Bags != 0;\r\n        },\r\n        getRowBySSCC() {\r\n            this.$emit('getRowBySscc', this.sscc);\r\n        },\r\n        getSSCC() {\r\n            this.$emit('getRowSSCC', this.sscc);\r\n        },\r\n        async Transfer() {\r\n            let num = Number(this.Bags) * Number(this.BagWeight);\r\n            if (this.$parent.$parent.$parent.isExpirationDate) {\r\n                Message({\r\n                    message: this.$t('MaterialPreparationBuild.OverExpirationDate'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            this.detailobj = this.$parent.$parent.$parent.detailobj;\r\n            let SelectList = this.$parent.$parent.$parent.SelectList;\r\n            if (SelectList == null || SelectList.length == 0) {\r\n                Message({\r\n                    message: this.$t('MaterialPreparationBuild.BatchPalletsEmpty'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            if (num.toFixed() > this.InQuantity) {\r\n                Message({\r\n                    message: this.$t('MaterialPreparationBuild.InQuantityNotEnough'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            if (num + Number(this.detailobj.MQuantity) > this.detailobj.MaxPvalue) {\r\n                Message({\r\n                    message: this.$t('MaterialPreparationBuild.QtyOverMax'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let data = {\r\n                ChangeUnit:this.detailobj.ChangeUnit?\"\":this.detailobj.ChangeUnit,//增加单位转换\r\n                PrintId: window.sessionStorage.getItem('PrintId'),\r\n                subID: this.SubId,\r\n                equpmentID: window.sessionStorage.getItem('room'),\r\n                bags: Number(this.Bags),\r\n                actualValue: this.InQuantity,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                bagWeight: this.BagWeight,\r\n                targetWeight: this.detailobj.MQuantityTotal,\r\n                actualWeight: this.detailobj.MQuantity == null ? 0 : Number(this.detailobj.MQuantity),\r\n                containerID: window.sessionStorage.getItem('BatchPallets'),\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId,\r\n                batchConsumeRequirementId: this.detailobj.BatchConsumeRequirementId,\r\n            };\r\n            if (window.sessionStorage.getItem('MaterialPreparation') != 'clbl') {\r\n                data.actualWeight = this.detailobj.MQuantity == null ? 0 : Number(this.detailobj.MQuantity);\r\n            } else {\r\n                data.actualWeight = this.detailobj.MQuantity == null ? 0 : Number(this.detailobj.MQuantity);\r\n            }\r\n            let res = await TransferFullBag(data);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$nextTick(_ => {\r\n                this.sscc = '';\r\n                this.$refs.autoFocus.focus();\r\n            });\r\n            this.$emit('getRefresh');\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\">\r\n.PartialBag {\r\n    padding: 10px;\r\n    margin: 10px;\r\n    height: 88px;\r\n    width: 100%;\r\n    border: 1px solid #ebeef5;\r\n    .tabinputbox {\r\n        height: 100%;\r\n        width: 100%;\r\n        display: flex;\r\n    }\r\n    .statusbox {\r\n        padding: 0 10px;\r\n        height: 30px;\r\n        background: #ffa500;\r\n    }\r\n    .tabinputsinglebox {\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        max-width: 250px;\r\n        margin-right: 10px;\r\n\r\n        .tabinputsinglelabel {\r\n            width: 100px;\r\n            font-size: 12px;\r\n        }\r\n        .tabbtnsinglebox {\r\n            height: 30px;\r\n            margin-bottom: 4px;\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}