{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDoc\\form-dialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDoc\\form-dialog.vue", "mtime": 1750226125043}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["form-dialog.vue"], "names": [], "mappings": ";AA2HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "form-dialog.vue", "sourceRoot": "src/views/SOP/sopDoc", "sourcesContent": ["<template>\n  <div class=\"sop-doc-form\">\n    <el-dialog \n      :title=\"dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')\" \n      :visible.sync=\"dialogVisible\" \n      width=\"700px\"\n      :close-on-click-modal=\"false\" \n      :modal-append-to-body=\"false\" \n      :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" :rules=\"rules\" label-width=\"100px\">\n        <div class=\"form-body\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"12\" v-if=\"opertype === 2\">\n              <el-form-item label=\"主键\">{{dialogForm.id}}</el-form-item>\n            </el-col>\n\n            <el-col :span=\"24\">\n              <el-form-item label=\"所属目录\" prop=\"dirId\">\n                <tree-select\n                  v-model=\"dialogForm.dirId\"\n                  :data=\"treeData\"\n                  :props=\"{\n                    children: 'children',\n                    label: 'name',\n                    value: 'id'\n                  }\"\n                  @change=\"handleDirChange\"\n                  placeholder=\"请选择所属目录\">\n                </tree-select>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"文档编码\" prop=\"docCode\">\n                <el-input v-model=\"dialogForm.docCode\" placeholder=\"请输入文档编码\"></el-input>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"文档版本\" prop=\"docVersion\">\n                <el-input v-model=\"dialogForm.docVersion\" placeholder=\"请输入文档版本\"></el-input>\n              </el-form-item>\n            </el-col>\n\n            <!-- <el-col :span=\"12\">\n              <el-form-item label=\"是否有效\" prop=\"docStatus\">\n                <el-radio-group v-model=\"dialogForm.docStatus\">\n                  <el-radio :label=\"1\">有效</el-radio>\n                  <el-radio :label=\"0\">无效</el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </el-col> -->\n\n            <el-col :span=\"24\">\n              <el-form-item label=\"文件上传\" prop=\"docList\">\n                <div class=\"upload-box\">\n                  <el-upload\n                    class=\"upload-demo\"\n                    :action=\"uploadUrl\"\n                    :on-success=\"handleUploadSuccess\"\n                    :on-error=\"handleUploadError\"\n                    :before-upload=\"beforeUpload\"\n                    :file-list=\"fileList\"\n                    :limit=\"1\">\n                    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload2\">点击上传</el-button>\n                    <div slot=\"tip\" class=\"el-upload__tip\">只能上传pdf/doc/docx文件</div>\n                  </el-upload>\n                </div>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"24\" v-if=\"fileList.length\">\n              <el-form-item label=\"文件信息\">\n                <el-table :data=\"dialogForm.docList\" border>\n                  <el-table-column prop=\"docName\" label=\"文件名\" width=\"180\"></el-table-column>\n                  <el-table-column prop=\"docCode\" label=\"编码\" width=\"120\">\n                    <template slot-scope=\"scope\">\n                      <el-input v-model=\"scope.row.docCode\" size=\"small\"></el-input>\n                    </template>\n                  </el-table-column>\n                  <el-table-column prop=\"docVersion\" label=\"版本\" width=\"100\">\n                    <template slot-scope=\"scope\">\n                      <el-input v-model=\"scope.row.docVersion\" size=\"small\"></el-input>\n                    </template>\n                  </el-table-column>\n                  <el-table-column prop=\"docStatus\" label=\"状态\" width=\"100\">\n                    <template slot-scope=\"scope\">\n                      <el-select v-model=\"scope.row.docStatus\" size=\"small\">\n                        <el-option label=\"有效\" :value=\"1\"></el-option>\n                        <el-option label=\"无效\" :value=\"0\"></el-option>\n                      </el-select>\n                    </template>\n                  </el-table-column>\n                  <el-table-column label=\"操作\" width=\"80\">\n                    <template slot-scope=\"scope\">\n                      <el-button type=\"text\" size=\"small\" @click=\"removeFile(scope.$index)\">删除</el-button>\n                    </template>\n                  </el-table-column>\n                </el-table>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <div class=\"btn-group\">\n          <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n          <el-button \n            type=\"primary\" \n            size=\"small\" \n            v-loading=\"formLoading\" \n            :disabled=\"formLoading || !dialogForm.docList.length\"\n            @click=\"submitForm\">\n            确 定\n          </el-button>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getSopDocDetail,\n  saveSopDocForm,\n  batchAddSopDoc\n} from \"@/api/SOP/sopDoc\";\nimport { getSopDirTree } from \"@/api/SOP/sopDir\";\nimport { configUrl } from '@/config'\nimport TreeSelect from '../components/tree-select'\n\nexport default {\n  name: 'FormDialog',\n  components: {\n    TreeSelect\n  },\n  props: {\n    treeData: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      dialogForm: {\n        id: undefined,\n        dirId: '',\n        docCode: '',\n        docVersion: '',\n        docStatus: 1, // 默认有效\n        deleted: 0,\n        docList: [] // 存储多个文档记录\n      },\n      rules: {\n        dirId: [\n          { required: true, message: '请选择所属目录', trigger: 'change' }\n        ],\n        docCode: [\n          { required: true, message: '请输入文档编码', trigger: 'blur' }\n        ],\n        docVersion: [\n          { required: true, message: '请输入文档版本', trigger: 'blur' }\n        ],\n        docList: [\n          { required: true, message: '请上传文档文件', trigger: 'change' }\n        ]\n      },\n      dialogVisible: false,\n      formLoading: false,\n      opertype: 1, // 1-新增 2-编辑\n      dirIdOptions: [], // 目录树选项\n      docStatusOptions: [], // 状态选项\n      deletedOptions: [], // 是否生效选项\n      fileList: [],\n      uploadUrl: `${configUrl[process.env.VUE_APP_SERVE].baseURL_DFM}/api/SopDoc/Upload`\n    }\n  },\n  mounted() {\n    this.getDictData()\n  },\n  methods: {\n    async getDictData() {\n      try {\n        this.docStatusOptions = await this.$getNewDataDictionary('docStatus')\n      } catch (err) {\n        console.error('获取字典数据失败:', err)\n        this.$message.error('获取字典数据失败')\n      }\n    },\n    async submitForm() {\n      try {\n        await this.$refs.dialogForm.validate()\n        this.formLoading = true\n\n        // 使用批量保存接口\n        const res = await batchAddSopDoc(this.dialogForm.docList)\n        if (res.success) {\n          this.$message.success(res.msg || '保存成功')\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        } else {\n          this.$message.error(res.msg || '保存失败')\n        }\n      } catch (err) {\n        if (err === false) return // 表单验证失败\n        console.error('保存失败:', err)\n        this.$message.error('保存失败')\n      } finally {\n        this.formLoading = false\n      }\n    },\n    show(data) {\n      this.opertype = data.ID ? 2 : 1\n      this.dialogForm = {\n        id: undefined,\n        dirId: '',\n        docCode: '',\n        docVersion: '',\n        docStatus: 1,\n        deleted: 0,\n        docList: []\n      }\n      this.fileList = []\n      this.dialogVisible = true\n      this.$nextTick(async () => {\n        if (data.ID) {\n          await this.getDialogDetail(data.ID)\n        }\n      })\n    },\n    async getDialogDetail(id) {\n      try {\n        const res = await getSopDocDetail(id)\n        if (res.success) {\n          this.dialogForm = res.response || {}\n          if (this.dialogForm.fileUuid) {\n            this.fileList = [{\n              name: this.dialogForm.docName,\n              url: this.dialogForm.filePath\n            }]\n          }\n        } else {\n          this.$message.error(res.msg || '获取详情失败')\n        }\n      } catch (err) {\n        console.error('获取详情失败:', err)\n        this.$message.error('获取详情失败')\n      }\n    },\n    handleUploadSuccess(res, file) {\n      if (res.success) {\n        // 清空之前的文档记录\n        this.dialogForm.docList = []\n        // 创建新的文档记录\n        const docRecord = {\n          dirId: this.dialogForm.dirId,\n          docName: file.name,\n          docCode: this.dialogForm.docCode || '', // 使用表单默认值或空字符串\n          docVersion: this.dialogForm.docVersion || '',\n          fileUuid: res.response,\n          filePath: this.dialogForm.filePath,\n          fileSize: file.size,\n          docStatus: this.dialogForm.docStatus || 1,\n          deleted: 0\n        }\n        this.dialogForm.docList.push(docRecord)\n        this.$message.success('上传成功')\n      } else {\n        this.$message.error(res.msg || '上传失败')\n      }\n    },\n\n    // 删除文件\n    removeFile(index) {\n      this.dialogForm.docList.splice(index, 1)\n      this.fileList = []\n    },\n    getFullPath(nodeId) {\n      const path = [];\n      const findPath = (data, targetId) => {\n        for (let node of data) {\n          if (node.id === targetId) {\n            path.unshift(node.name);\n            return true;\n          }\n          if (node.children && findPath(node.children, targetId)) {\n            path.unshift(node.name);\n            return true;\n          }\n        }\n        return false;\n      };\n      findPath(this.treeData, nodeId);\n      return path.join('/');\n    },\n    handleDirChange(node) {\n      // 当目录选择改变时，更新路径\n      this.dialogForm.filePath = this.getFullPath(node.id);\n    },\n    handleUploadError(err) {\n      console.error('文件上传失败:', err)\n      this.$message.error('文件上传失败')\n    },\n    beforeUpload(file) {\n      const validTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']\n      if (!validTypes.includes(file.type)) {\n        this.$message.error('只能上传PDF/DOC/DOCX格式文件!')\n        return false\n      }\n      return true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.sop-doc-form {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n\n    .el-dialog__header {\n      padding: 15px 20px;\n      border-bottom: 1px solid #ebeef5;\n      margin: 0;\n    }\n    \n    .el-dialog__body {\n      padding: 20px;\n    }\n\n    .el-dialog__footer {\n      padding: 15px 20px;\n      border-top: 1px solid #ebeef5;\n      background-color: #f9fafb;\n\n      .btn-group {\n        display: flex;\n        justify-content: flex-end;\n        gap: 12px;\n      }\n    }\n  }\n\n  .form-body {\n    padding: 15px 0;\n\n    .el-form-item {\n      margin-bottom: 20px;\n      \n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      .el-form-item__content {\n        line-height: 32px;\n      }\n    }\n\n    .el-radio-group {\n      display: flex;\n      align-items: center;\n      gap: 20px;\n\n      .el-radio {\n        margin-right: 0;\n        \n        :deep(.el-radio__label) {\n          padding-left: 8px;\n        }\n      }\n    }\n\n    .upload-box {\n      width: 100%;\n      padding: 20px;\n      border: 1px dashed #dcdfe6;\n      border-radius: 4px;\n      background-color: #fafafa;\n      text-align: center;\n      \n      :deep(.el-upload) {\n        .el-button {\n          padding: 8px 15px;\n          margin-bottom: 10px;\n          font-size: 13px;\n          \n          i {\n            margin-right: 4px;\n          }\n        }\n\n        .el-upload__tip {\n          color: #909399;\n          font-size: 12px;\n          line-height: 1.4;\n        }\n      }\n\n      :deep(.el-upload-list) {\n        text-align: left;\n        padding: 0 10px;\n        margin-top: 10px;\n\n        .el-upload-list__item {\n          transition: all 0.3s;\n          \n          &:hover {\n            background-color: #f5f7fa;\n          }\n        }\n      }\n    }\n  }\n\n  // 输入框统一样式\n  :deep(.el-input),\n  :deep(.el-select),\n  :deep(.el-tree-select) {\n    width: 100%;\n    .el-input__inner {\n      line-height: 32px;\n      height: 32px;\n    }\n  }\n}\n</style>\n"]}]}