{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\api\\SOP\\sopPermApply.js", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\api\\SOP\\sopPermApply.js", "mtime": 1750218908818}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\eslint-loader\\index.js", "mtime": 1743379020994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlsL3JlcXVlc3QnOwppbXBvcnQgeyBjb25maWdVcmwgfSBmcm9tICdAL2NvbmZpZyc7CmNvbnN0IGJhc2VVUkwgPSBjb25maWdVcmxbcHJvY2Vzcy5lbnYuVlVFX0FQUF9TRVJWRV0uYmFzZVVSTF9ERk07Ci8qKgogKiDmnYPpmZDnlLPor7flrqHmoLjooajliIbpobXmn6Xor6IKICogQHBhcmFtIHvmn6Xor6LmnaHku7Z9IGRhdGEKICovCgpleHBvcnQgZnVuY3Rpb24gZ2V0U29wUGVybUFwcGx5TGlzdChkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiBiYXNlVVJMICsgJy9hcGkvU29wUGVybUFwcGx5L0dldFBhZ2VMaXN0JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YQogIH0pOwp9Ci8qKgogKiDkv53lrZjmnYPpmZDnlLPor7flrqHmoLjooagKICogQHBhcmFtIGRhdGEKICovCgpleHBvcnQgZnVuY3Rpb24gc2F2ZVNvcFBlcm1BcHBseUZvcm0oZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogYmFzZVVSTCArICcvYXBpL1NvcFBlcm1BcHBseS9TYXZlRm9ybScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGEKICB9KTsKfQovKioKICog6I635Y+W5p2D6ZmQ55Sz6K+35a6h5qC46KGo6K+m5oOFCiAqIEBwYXJhbSB7SWR9CiAqLwoKZXhwb3J0IGZ1bmN0aW9uIGdldFNvcFBlcm1BcHBseURldGFpbChpZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogYmFzZVVSTCArICcvYXBpL1NvcFBlcm1BcHBseS9HZXRFbnRpdHknLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBpZAogIH0pOwp9Ci8qKgogKiDliKDpmaTmnYPpmZDnlLPor7flrqHmoLjooagKICogQHBhcmFtIHvkuLvplK59IGRhdGEKICovCgpleHBvcnQgZnVuY3Rpb24gZGVsU29wUGVybUFwcGx5KGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IGJhc2VVUkwgKyAnL2FwaS9Tb3BQZXJtQXBwbHkvRGVsZXRlJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YQogIH0pOwp9"}, {"version": 3, "names": ["request", "configUrl", "baseURL", "process", "env", "VUE_APP_SERVE", "baseURL_DFM", "getSopPermApplyList", "data", "url", "method", "saveSopPermApplyForm", "getSopPermApplyDetail", "id", "delSopPermApply"], "sources": ["C:/work/syngentagroup/SEFA_XZD/SEFA.UI/sefa-application-business/src/api/SOP/sopPermApply.js"], "sourcesContent": ["import request from '@/util/request'\nimport { configUrl } from '@/config'\nconst baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM\n\n/**\n * 权限申请审核表分页查询\n * @param {查询条件} data\n */\nexport function getSopPermApplyList(data) {\n    return request({\n        url: baseURL + '/api/SopPermApply/GetPageList',\n        method: 'post',\n        data\n    })\n}\n\n/**\n * 保存权限申请审核表\n * @param data\n */\nexport function saveSopPermApplyForm(data) {\n    return request({\n        url: baseURL + '/api/SopPermApply/SaveForm',\n        method: 'post',\n        data\n    })\n}\n\n/**\n * 获取权限申请审核表详情\n * @param {Id}\n */\nexport function getSopPermApplyDetail(id) {\n    return request({\n        url: baseURL + '/api/SopPermApply/GetEntity',\n        method: 'post',\n        data: id\n    })\n}\n\n/**\n * 删除权限申请审核表\n * @param {主键} data\n */\nexport function delSopPermApply(data) {\n    return request({\n        url: baseURL + '/api/SopPermApply/Delete',\n        method: 'post',\n        data\n    })\n}\n\n\n"], "mappings": "AAAA,OAAOA,OAAP,MAAoB,gBAApB;AACA,SAASC,SAAT,QAA0B,UAA1B;AACA,MAAMC,OAAO,GAAGD,SAAS,CAACE,OAAO,CAACC,GAAR,CAAYC,aAAb,CAAT,CAAqCC,WAArD;AAEA;AACA;AACA;AACA;;AACA,OAAO,SAASC,mBAAT,CAA6BC,IAA7B,EAAmC;EACtC,OAAOR,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,+BADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF;EAHW,CAAD,CAAd;AAKH;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASG,oBAAT,CAA8BH,IAA9B,EAAoC;EACvC,OAAOR,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,4BADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF;EAHW,CAAD,CAAd;AAKH;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASI,qBAAT,CAA+BC,EAA/B,EAAmC;EACtC,OAAOb,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,6BADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF,IAAI,EAAEK;EAHK,CAAD,CAAd;AAKH;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASC,eAAT,CAAyBN,IAAzB,EAA+B;EAClC,OAAOR,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,0BADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF;EAHW,CAAD,CAAd;AAKH"}]}