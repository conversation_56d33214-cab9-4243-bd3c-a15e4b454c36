﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;
using SEFA.Base.Model.BASE;

namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///SOP文档主表
    ///</summary>
    [SugarTable("DFM_B_SOP_DOC")]
    public class SopDocEntity : EntityBase
    {
        public SopDocEntity()
        {
        }

        /// <summary>
        /// Desc:所属目录ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DIR_ID")]
        public string DirId { get; set; }

        /// <summary>
        /// Desc:文档名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DOC_NAME")]
        public string DocName { get; set; }

        /// <summary>
        /// Desc:文档编码(唯一)
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DOC_CODE")]
        public string DocCode { get; set; }
        
        /// <summary>
        /// Desc:文档关键词
        /// </summary>
        [SugarColumn(ColumnName = "DOC_KEY_WORDS")]
        public string DocKeyWords { get; set; }
        /// <summary>
        /// Desc:Minio文件UUID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "FILE_UUID")]
        public string FileUuid { get; set; }

        /// <summary>
        /// Desc:文件存储路径
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "FILE_PATH")]
        public string FilePath { get; set; }

        /// <summary>
        /// Desc:文件大小(字节)
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FILE_SIZE")]
        public long? FileSize { get; set; }

        /// <summary>
        /// Desc:状态(1-生效 2-失效 3-审核中)
        /// Default:1
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DOC_STATUS")]
        public byte? DocStatus { get; set; }

        /// <summary>
        /// Desc:删除标记(0-未删 1-已删)
        /// Default:0
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int? Deleted { get; set; }

        /// <summary>
        /// Desc:文档版本
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DOC_VERSION")]
        public string DocVersion { get; set; }
        
        /// <summary>
        /// Desc:文档描述
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DOC_COMMENT")]
        public string DocComment { get; set; }
    }
}