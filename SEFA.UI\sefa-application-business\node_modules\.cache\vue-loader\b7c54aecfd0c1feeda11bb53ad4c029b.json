{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDocSearch\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDocSearch\\index.vue", "mtime": 1750296294573}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAoGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopDocSearch", "sourcesContent": ["<template>\n  <div class=\"root usemystyle\">\n    <div class=\"root-layout\" v-loading=\"initLoading\">\n      <div class=\"root-content\">\n        <div class=\"InventorySearchBox\">\n          <div class=\"search-form\">\n            <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n              <div class=\"form-content\">\n                <div class=\"search-area\">\n                  <div class=\"search-row\">\n                    <el-form-item label=\"名称\" prop=\"docName\" label-width=\"40px\" class=\"search-form-item\">\n                      <el-input v-model=\"searchForm.docName\" placeholder=\"输入名称\" clearable size=\"small\">\n                      </el-input>\n                    </el-form-item>\n                    <el-form-item label=\"编码\" prop=\"docCode\" label-width=\"40px\" class=\"search-form-item\">\n                      <el-input v-model=\"searchForm.docCode\" placeholder=\"输入编码\" clearable size=\"small\"></el-input>\n                    </el-form-item>\n                    <el-form-item label=\"版本\" prop=\"docVersion\" label-width=\"40px\" class=\"search-form-item\">\n                      <el-input v-model=\"searchForm.docVersion\" placeholder=\"输入版本\" clearable size=\"small\"></el-input>\n                    </el-form-item>\n                    <el-form-item label=\"状态\" prop=\"docStatus\" label-width=\"40px\" class=\"search-form-item\">\n                      <el-select v-model=\"searchForm.docStatus\" placeholder=\"选择\" clearable size=\"small\">\n                        <el-option label=\"有效\" :value=\"1\"></el-option>\n                        <el-option label=\"无效\" :value=\"0\"></el-option>\n                      </el-select>\n                    </el-form-item>\n                    <div class=\"action-buttons\">\n                      <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn\" size=\"small\">查询</el-button>\n                      <el-button icon=\"el-icon-refresh\" @click=\"resetForm\" size=\"small\">重置</el-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </el-form>\n          </div>\n        </div>\n        <div class=\"root-main\">\n          <el-table class=\"mt-3\"\n                    :height=\"700\"\n                    border\n                    v-loading=\"tableLoading\"\n                    :data=\"tableData\"\n                    style=\"width: 100%; border-radius: 4px;\"\n                    :empty-text=\"'暂无数据'\">\n            <el-table-column\n              type=\"index\"\n              label=\"序号\"\n              width=\"50\"\n              align=\"center\">\n            </el-table-column>\n            <el-table-column v-for=\"(item) in tableName\"\n                             :default-sort=\"{prop: item.value, order: 'descending'}\"\n                             :key=\"item.value\"\n                             :prop=\"item.value\"\n                             :label=\"typeof item.text === 'function' ? item.text() : item.text\"\n                             :width=\"item.width\"\n                             :align=\"item.alignType || 'center'\"\n                             sortable\n                             show-overflow-tooltip>\n              <template slot-scope=\"scope\">\n                <template v-if=\"item.value === 'FileSize'\">\n                  {{ formatFileSize(scope.row[item.value]) }}\n                </template>\n                <template v-else-if=\"item.value === 'DocStatus'\">\n                  <el-tag :type=\"getStatusType(scope.row[item.value])\" size=\"small\">\n                    {{ formatStatus(scope.row[item.value]) }}\n                  </el-tag>\n                </template>\n                <template v-else>\n                  {{ scope.row[item.value] }}\n                </template>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"operation\" :min-width=\"120\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"text\" @click=\"handleDownload(scope.row)\">{{ $t('SOP.Preview') }}</el-button>\n                <el-button size=\"mini\" type=\"text\" @click=\"handleDownload(scope.row)\">{{ $t('GLOBAL.Download') }}</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </div>\n      <div class=\"root-footer\">\n        <el-pagination\n            class=\"mt-3\"\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n            :current-page=\"searchForm.pageIndex\"\n            :page-sizes=\"[10,20, 50, 100,500]\"\n            :page-size=\"searchForm.pageSize\"\n            layout=\"->,total, sizes, prev, pager, next, jumper\"\n            :total=\"total\"\n            background\n        ></el-pagination>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss'\nimport {\n    getSopDocList, downloadSopDoc\n} from \"@/api/SOP/sopDoc\";\nimport { sopDocColumns } from '@/columns/SOP/sopDoc.js';\n\nexport default {\n  name: 'index.vue',\n  components: {\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n        docName: '',\n        docCode: '',\n        docVersion: '',\n        docStatus: ''\n      },\n      total: 0,\n      tableData: [],\n      tableName: sopDocColumns,\n      tableLoading: false,\n      initLoading: false\n    }\n  },\n  async mounted() {\n    try {\n      this.initLoading = true\n      await this.getTableData()\n    } catch (err) {\n      console.error('页面初始化失败:', err)\n      this.$message.error('页面初始化失败，请刷新重试')\n    } finally {\n      this.initLoading = false\n    }\n  },\n  beforeDestroy() {\n    window.onresize = null\n  },\n  methods: {\n    // 格式化文件大小\n    formatFileSize(size) {\n      if (!size) return '0 B'\n      const units = ['B', 'KB', 'MB', 'GB', 'TB']\n      let index = 0\n      let fileSize = parseFloat(size)\n      while (fileSize >= 1024 && index < units.length - 1) {\n        fileSize /= 1024\n        index++\n      }\n      return `${fileSize.toFixed(2)} ${units[index]}`\n    },\n\n    // 获取状态对应的类型\n    getStatusType(status) {\n      switch (status) {\n        case 1: return 'success'\n        case 2: return 'warning'\n        case 0: return 'info'\n        default: return ''\n      }\n    },\n\n    // 格式化状态\n    formatStatus(status) {\n      switch (status) {\n        case 1: return this.$t('SOP.StatusValid')\n        case 2: return this.$t('SOP.AuditPending')\n        case 0: return this.$t('SOP.StatusInvalid')\n        default: return this.$t('GLOBAL._WZ')\n      }\n    },\n\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    resetForm() {\n      this.searchForm = {\n        pageIndex: 1,\n        pageSize: 20,\n        docName: '',\n        docCode: '',\n        docVersion: '',\n        docStatus: ''\n      }\n      this.getTableData()\n    },\n\n    async getTableData() {\n      this.tableLoading = true\n      try {\n        const res = await getSopDocList(this.searchForm)\n        if (res.success) {\n          this.tableData = res.response.data || []\n          this.total = res.response.dataCount || 0\n        } else {\n          this.$message.error(res.msg || '获取数据失败')\n        }\n      } catch (err) {\n        console.error('获取表格数据失败:', err)\n        this.$message.error('获取数据失败')\n        throw err\n      } finally {\n        this.tableLoading = false\n      }\n    },\n\n    // 处理文件下载\n    async handleDownload(row) {\n      try {\n        const res = await downloadSopDoc(row.FileUuid)\n        // 创建下载链接\n        const blob = new Blob([res], { type: res.type })\n        const link = document.createElement('a')\n        link.href = window.URL.createObjectURL(blob)\n        link.download = row.DocName\n        link.click()\n        window.URL.revokeObjectURL(link.href)\n      } catch (err) {\n        console.error('文件下载失败:', err)\n        this.$message.error('文件下载失败')\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.search-form-item {\n  .el-form-item__content {\n    width: 180px; // 统一宽度为180px\n  }\n}\n\n.root-content {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .InventorySearchBox {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .search-form {\n      :deep(.el-form) {\n        .el-form-item--small.el-form-item {\n          margin-bottom: 0;\n        }\n      }\n\n      .form-content {\n        padding: 4px;\n\n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .el-form-item {\n              margin: 0;\n              flex: none;\n\n              .el-form-item__label {\n                padding-right: 4px;\n                line-height: 26px;\n                font-size: 12px;\n              }\n\n              .el-form-item__content {\n                line-height: 26px;\n\n                .el-input,\n                .el-select {\n                  :deep(.el-input__inner) {\n                    height: 26px;\n                    line-height: 26px;\n                    padding: 0 8px;\n                    font-size: 12px;\n                  }\n\n                  :deep(.el-input-group__append) {\n                    padding: 0;\n                    .el-button {\n                      padding: 0 10px;\n                      height: 26px;\n                      border: none;\n                    }\n                  }\n                }\n              }\n            }\n\n            .el-button {\n              height: 26px;\n              padding: 0 10px;\n              font-size: 12px;\n              margin-left: 4px;\n\n              [class^=\"el-icon-\"] {\n                margin-right: 3px;\n                font-size: 12px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .el-table {\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n}\n\n.root-footer {\n  padding: 10px;\n  background-color: #fff;\n  border-top: 1px solid #e4e7ed;\n}\n\n// 响应式设计\n@media (max-width: 1200px) {\n  .search-row {\n    .el-form-item {\n      margin-bottom: 10px;\n    }\n  }\n}\n</style>"]}]}