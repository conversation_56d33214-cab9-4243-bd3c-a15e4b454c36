{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDoc\\form-dialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDoc\\form-dialog.vue", "mtime": 1750226125043}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA2HA,SACAA,eADA,EAEAC,cAFA,EAGAC,cAHA,QAIA,kBAJA;AAKA;AACA;AACA;AAEA;EACAC,kBADA;EAEAC;IACAC;EADA,CAFA;EAKAC;IACAC;MACAC,WADA;MAEAC;IAFA;EADA,CALA;;EAWAC;IACA;MACAC;QACAC,aADA;QAEAC,SAFA;QAGAC,WAHA;QAIAC,cAJA;QAKAC,YALA;QAKA;QACAC,UANA;QAOAC,WAPA,CAOA;;MAPA,CADA;MAUAC;QACAN,QACA;UAAAO;UAAAC;UAAAC;QAAA,CADA,CADA;QAIAR,UACA;UAAAM;UAAAC;UAAAC;QAAA,CADA,CAJA;QAOAP,aACA;UAAAK;UAAAC;UAAAC;QAAA,CADA,CAPA;QAUAJ,UACA;UAAAE;UAAAC;UAAAC;QAAA,CADA;MAVA,CAVA;MAwBAC,oBAxBA;MAyBAC,kBAzBA;MA0BAC,WA1BA;MA0BA;MACAC,gBA3BA;MA2BA;MACAC,oBA5BA;MA4BA;MACAC,kBA7BA;MA6BA;MACAC,YA9BA;MA+BAC;IA/BA;EAiCA,CA7CA;;EA8CAC;IACA;EACA,CAhDA;;EAiDAC;IACA;MACA;QACA;MACA,CAFA,CAEA;QACAC;QACA;MACA;IACA,CARA;;IASA;MACA;QACA;QACA,wBAFA,CAIA;;QACA;;QACA;UACA;UACA;UACA;QACA,CAJA,MAIA;UACA;QACA;MACA,CAbA,CAaA;QACA,0BADA,CACA;;QACAA;QACA;MACA,CAjBA,SAiBA;QACA;MACA;IACA,CA9BA;;IA+BAC;MACA;MACA;QACAtB,aADA;QAEAC,SAFA;QAGAC,WAHA;QAIAC,cAJA;QAKAC,YALA;QAMAC,UANA;QAOAC;MAPA;MASA;MACA;MACA;QACA;UACA;QACA;MACA,CAJA;IAKA,CAjDA;;IAkDA;MACA;QACA;;QACA;UACA;;UACA;YACA;cACAf,6BADA;cAEAgC;YAFA;UAIA;QACA,CARA,MAQA;UACA;QACA;MACA,CAbA,CAaA;QACAF;QACA;MACA;IACA,CApEA;;IAqEAG;MACA;QACA;QACA,6BAFA,CAGA;;QACA;UACAvB,4BADA;UAEAwB,kBAFA;UAGAvB,sCAHA;UAGA;UACAC,4CAJA;UAKAuB,sBALA;UAMAC,kCANA;UAOAC,mBAPA;UAQAxB,yCARA;UASAC;QATA;QAWA;QACA;MACA,CAjBA,MAiBA;QACA;MACA;IACA,CA1FA;;IA4FA;IACAwB;MACA;MACA;IACA,CAhGA;;IAiGAC;MACA;;MACA;QACA;UACA;YACAC;YACA;UACA;;UACA;YACAA;YACA;UACA;QACA;;QACA;MACA,CAZA;;MAaAC;MACA;IACA,CAlHA;;IAmHAC;MACA;MACA;IACA,CAtHA;;IAuHAC;MACAb;MACA;IACA,CA1HA;;IA2HAc;MACA;;MACA;QACA;QACA;MACA;;MACA;IACA;;EAlIA;AAjDA", "names": ["getSopDocDetail", "saveSopDocForm", "batchAddSopDoc", "name", "components", "TreeSelect", "props", "treeData", "type", "default", "data", "dialogForm", "id", "dirId", "docCode", "docVersion", "doc<PERSON><PERSON>us", "deleted", "docList", "rules", "required", "message", "trigger", "dialogVisible", "formLoading", "opertype", "dirIdOptions", "docStatusOptions", "deletedOptions", "fileList", "uploadUrl", "mounted", "methods", "console", "show", "url", "handleUploadSuccess", "doc<PERSON>ame", "fileUuid", "filePath", "fileSize", "removeFile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path", "<PERSON><PERSON><PERSON>", "handleDirChange", "handleUploadError", "beforeUpload"], "sourceRoot": "src/views/SOP/sopDoc", "sources": ["form-dialog.vue"], "sourcesContent": ["<template>\n  <div class=\"sop-doc-form\">\n    <el-dialog \n      :title=\"dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')\" \n      :visible.sync=\"dialogVisible\" \n      width=\"700px\"\n      :close-on-click-modal=\"false\" \n      :modal-append-to-body=\"false\" \n      :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" :rules=\"rules\" label-width=\"100px\">\n        <div class=\"form-body\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"12\" v-if=\"opertype === 2\">\n              <el-form-item label=\"主键\">{{dialogForm.id}}</el-form-item>\n            </el-col>\n\n            <el-col :span=\"24\">\n              <el-form-item label=\"所属目录\" prop=\"dirId\">\n                <tree-select\n                  v-model=\"dialogForm.dirId\"\n                  :data=\"treeData\"\n                  :props=\"{\n                    children: 'children',\n                    label: 'name',\n                    value: 'id'\n                  }\"\n                  @change=\"handleDirChange\"\n                  placeholder=\"请选择所属目录\">\n                </tree-select>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"文档编码\" prop=\"docCode\">\n                <el-input v-model=\"dialogForm.docCode\" placeholder=\"请输入文档编码\"></el-input>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"文档版本\" prop=\"docVersion\">\n                <el-input v-model=\"dialogForm.docVersion\" placeholder=\"请输入文档版本\"></el-input>\n              </el-form-item>\n            </el-col>\n\n            <!-- <el-col :span=\"12\">\n              <el-form-item label=\"是否有效\" prop=\"docStatus\">\n                <el-radio-group v-model=\"dialogForm.docStatus\">\n                  <el-radio :label=\"1\">有效</el-radio>\n                  <el-radio :label=\"0\">无效</el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </el-col> -->\n\n            <el-col :span=\"24\">\n              <el-form-item label=\"文件上传\" prop=\"docList\">\n                <div class=\"upload-box\">\n                  <el-upload\n                    class=\"upload-demo\"\n                    :action=\"uploadUrl\"\n                    :on-success=\"handleUploadSuccess\"\n                    :on-error=\"handleUploadError\"\n                    :before-upload=\"beforeUpload\"\n                    :file-list=\"fileList\"\n                    :limit=\"1\">\n                    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload2\">点击上传</el-button>\n                    <div slot=\"tip\" class=\"el-upload__tip\">只能上传pdf/doc/docx文件</div>\n                  </el-upload>\n                </div>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"24\" v-if=\"fileList.length\">\n              <el-form-item label=\"文件信息\">\n                <el-table :data=\"dialogForm.docList\" border>\n                  <el-table-column prop=\"docName\" label=\"文件名\" width=\"180\"></el-table-column>\n                  <el-table-column prop=\"docCode\" label=\"编码\" width=\"120\">\n                    <template slot-scope=\"scope\">\n                      <el-input v-model=\"scope.row.docCode\" size=\"small\"></el-input>\n                    </template>\n                  </el-table-column>\n                  <el-table-column prop=\"docVersion\" label=\"版本\" width=\"100\">\n                    <template slot-scope=\"scope\">\n                      <el-input v-model=\"scope.row.docVersion\" size=\"small\"></el-input>\n                    </template>\n                  </el-table-column>\n                  <el-table-column prop=\"docStatus\" label=\"状态\" width=\"100\">\n                    <template slot-scope=\"scope\">\n                      <el-select v-model=\"scope.row.docStatus\" size=\"small\">\n                        <el-option label=\"有效\" :value=\"1\"></el-option>\n                        <el-option label=\"无效\" :value=\"0\"></el-option>\n                      </el-select>\n                    </template>\n                  </el-table-column>\n                  <el-table-column label=\"操作\" width=\"80\">\n                    <template slot-scope=\"scope\">\n                      <el-button type=\"text\" size=\"small\" @click=\"removeFile(scope.$index)\">删除</el-button>\n                    </template>\n                  </el-table-column>\n                </el-table>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <div class=\"btn-group\">\n          <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n          <el-button \n            type=\"primary\" \n            size=\"small\" \n            v-loading=\"formLoading\" \n            :disabled=\"formLoading || !dialogForm.docList.length\"\n            @click=\"submitForm\">\n            确 定\n          </el-button>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getSopDocDetail,\n  saveSopDocForm,\n  batchAddSopDoc\n} from \"@/api/SOP/sopDoc\";\nimport { getSopDirTree } from \"@/api/SOP/sopDir\";\nimport { configUrl } from '@/config'\nimport TreeSelect from '../components/tree-select'\n\nexport default {\n  name: 'FormDialog',\n  components: {\n    TreeSelect\n  },\n  props: {\n    treeData: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      dialogForm: {\n        id: undefined,\n        dirId: '',\n        docCode: '',\n        docVersion: '',\n        docStatus: 1, // 默认有效\n        deleted: 0,\n        docList: [] // 存储多个文档记录\n      },\n      rules: {\n        dirId: [\n          { required: true, message: '请选择所属目录', trigger: 'change' }\n        ],\n        docCode: [\n          { required: true, message: '请输入文档编码', trigger: 'blur' }\n        ],\n        docVersion: [\n          { required: true, message: '请输入文档版本', trigger: 'blur' }\n        ],\n        docList: [\n          { required: true, message: '请上传文档文件', trigger: 'change' }\n        ]\n      },\n      dialogVisible: false,\n      formLoading: false,\n      opertype: 1, // 1-新增 2-编辑\n      dirIdOptions: [], // 目录树选项\n      docStatusOptions: [], // 状态选项\n      deletedOptions: [], // 是否生效选项\n      fileList: [],\n      uploadUrl: `${configUrl[process.env.VUE_APP_SERVE].baseURL_DFM}/api/SopDoc/Upload`\n    }\n  },\n  mounted() {\n    this.getDictData()\n  },\n  methods: {\n    async getDictData() {\n      try {\n        this.docStatusOptions = await this.$getNewDataDictionary('docStatus')\n      } catch (err) {\n        console.error('获取字典数据失败:', err)\n        this.$message.error('获取字典数据失败')\n      }\n    },\n    async submitForm() {\n      try {\n        await this.$refs.dialogForm.validate()\n        this.formLoading = true\n\n        // 使用批量保存接口\n        const res = await batchAddSopDoc(this.dialogForm.docList)\n        if (res.success) {\n          this.$message.success(res.msg || '保存成功')\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        } else {\n          this.$message.error(res.msg || '保存失败')\n        }\n      } catch (err) {\n        if (err === false) return // 表单验证失败\n        console.error('保存失败:', err)\n        this.$message.error('保存失败')\n      } finally {\n        this.formLoading = false\n      }\n    },\n    show(data) {\n      this.opertype = data.ID ? 2 : 1\n      this.dialogForm = {\n        id: undefined,\n        dirId: '',\n        docCode: '',\n        docVersion: '',\n        docStatus: 1,\n        deleted: 0,\n        docList: []\n      }\n      this.fileList = []\n      this.dialogVisible = true\n      this.$nextTick(async () => {\n        if (data.ID) {\n          await this.getDialogDetail(data.ID)\n        }\n      })\n    },\n    async getDialogDetail(id) {\n      try {\n        const res = await getSopDocDetail(id)\n        if (res.success) {\n          this.dialogForm = res.response || {}\n          if (this.dialogForm.fileUuid) {\n            this.fileList = [{\n              name: this.dialogForm.docName,\n              url: this.dialogForm.filePath\n            }]\n          }\n        } else {\n          this.$message.error(res.msg || '获取详情失败')\n        }\n      } catch (err) {\n        console.error('获取详情失败:', err)\n        this.$message.error('获取详情失败')\n      }\n    },\n    handleUploadSuccess(res, file) {\n      if (res.success) {\n        // 清空之前的文档记录\n        this.dialogForm.docList = []\n        // 创建新的文档记录\n        const docRecord = {\n          dirId: this.dialogForm.dirId,\n          docName: file.name,\n          docCode: this.dialogForm.docCode || '', // 使用表单默认值或空字符串\n          docVersion: this.dialogForm.docVersion || '',\n          fileUuid: res.response,\n          filePath: this.dialogForm.filePath,\n          fileSize: file.size,\n          docStatus: this.dialogForm.docStatus || 1,\n          deleted: 0\n        }\n        this.dialogForm.docList.push(docRecord)\n        this.$message.success('上传成功')\n      } else {\n        this.$message.error(res.msg || '上传失败')\n      }\n    },\n\n    // 删除文件\n    removeFile(index) {\n      this.dialogForm.docList.splice(index, 1)\n      this.fileList = []\n    },\n    getFullPath(nodeId) {\n      const path = [];\n      const findPath = (data, targetId) => {\n        for (let node of data) {\n          if (node.id === targetId) {\n            path.unshift(node.name);\n            return true;\n          }\n          if (node.children && findPath(node.children, targetId)) {\n            path.unshift(node.name);\n            return true;\n          }\n        }\n        return false;\n      };\n      findPath(this.treeData, nodeId);\n      return path.join('/');\n    },\n    handleDirChange(node) {\n      // 当目录选择改变时，更新路径\n      this.dialogForm.filePath = this.getFullPath(node.id);\n    },\n    handleUploadError(err) {\n      console.error('文件上传失败:', err)\n      this.$message.error('文件上传失败')\n    },\n    beforeUpload(file) {\n      const validTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']\n      if (!validTypes.includes(file.type)) {\n        this.$message.error('只能上传PDF/DOC/DOCX格式文件!')\n        return false\n      }\n      return true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.sop-doc-form {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n\n    .el-dialog__header {\n      padding: 15px 20px;\n      border-bottom: 1px solid #ebeef5;\n      margin: 0;\n    }\n    \n    .el-dialog__body {\n      padding: 20px;\n    }\n\n    .el-dialog__footer {\n      padding: 15px 20px;\n      border-top: 1px solid #ebeef5;\n      background-color: #f9fafb;\n\n      .btn-group {\n        display: flex;\n        justify-content: flex-end;\n        gap: 12px;\n      }\n    }\n  }\n\n  .form-body {\n    padding: 15px 0;\n\n    .el-form-item {\n      margin-bottom: 20px;\n      \n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      .el-form-item__content {\n        line-height: 32px;\n      }\n    }\n\n    .el-radio-group {\n      display: flex;\n      align-items: center;\n      gap: 20px;\n\n      .el-radio {\n        margin-right: 0;\n        \n        :deep(.el-radio__label) {\n          padding-left: 8px;\n        }\n      }\n    }\n\n    .upload-box {\n      width: 100%;\n      padding: 20px;\n      border: 1px dashed #dcdfe6;\n      border-radius: 4px;\n      background-color: #fafafa;\n      text-align: center;\n      \n      :deep(.el-upload) {\n        .el-button {\n          padding: 8px 15px;\n          margin-bottom: 10px;\n          font-size: 13px;\n          \n          i {\n            margin-right: 4px;\n          }\n        }\n\n        .el-upload__tip {\n          color: #909399;\n          font-size: 12px;\n          line-height: 1.4;\n        }\n      }\n\n      :deep(.el-upload-list) {\n        text-align: left;\n        padding: 0 10px;\n        margin-top: 10px;\n\n        .el-upload-list__item {\n          transition: all 0.3s;\n          \n          &:hover {\n            background-color: #f5f7fa;\n          }\n        }\n      }\n    }\n  }\n\n  // 输入框统一样式\n  :deep(.el-input),\n  :deep(.el-select),\n  :deep(.el-tree-select) {\n    width: 100%;\n    .el-input__inner {\n      line-height: 32px;\n      height: 32px;\n    }\n  }\n}\n</style>\n"]}]}