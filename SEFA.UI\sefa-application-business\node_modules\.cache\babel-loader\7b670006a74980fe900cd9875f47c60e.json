{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\auth\\Login.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\auth\\Login.vue", "mtime": 1750254216324}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA4CA;AACA;AAEA;AACA;EACAA,UADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACAC,cADA;MAEAC,gBAFA;MAGAC;QACAC,YADA;QAEAC,YAFA;QAGAC;MAHA,CAHA;MAQAC;QACAH,wDADA;QAEAC;MAFA,CARA;MAYAG,iBAZA;MAaAC;IAbA;EAeA,CArBA;;EAsBAC;IACAC;MACAC;IACA;;EAHA,CAtBA;;EA2BAC;IACA;EACA,CA7BA;;EA8BAC;IACA;IACAC;MACAC;MACA;MACA;MACAC;IACA,CAPA;;IAQAC;MACA;MACA;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;;MAEA;QACAL;QACA;MACA;;MAEA,6BATA,CAUA;;MACA,2CAXA,CAYA;;MACA;QAAAM;QAAAC;MAAA,kBAbA,CAcA;MAEA;;MACA;QACAR;QACA;QACA;QACA;MACA,CALA,MAKA;QACA;QACA;MACA,CAHA,MAGA,CACA;MACA;IACA,CApCA;;IAqCAS;MACA;QACA;MACA;IACA,CAzCA;;IA0CAC;MACA;MACA,YACAC,QADA,CACA,OADA,EACA,cADA,EAEAC,IAFA,CAEA;QACA;MACA,CAJA,EAKAC,KALA,CAKA;QACAZ;UACAa,UADA;UAEAC,YAFA;UAGAC;QAHA;;QAKA;MACA,CAZA;IAaA,CAzDA;;IA0DAC;MACA,YACAN,QADA,CACA,eADA,EAEAC,IAFA,CAEA;QACA,qBADA,CAEA;;QACA;QACA;QACA;UAAAM;UAAAC;QAAA;UAAAD;QAAA;QACA;QACA;MACA,CAVA,EAWAL,KAXA,CAWA;QACA;MACA,CAbA;IAcA,CAzEA;;IA0EAO;MACApB;IACA,CA5EA;;IA6EAqB;;EA7EA;AA9BA", "names": ["name", "components", "LocaleSwitch", "data", "loading", "formValid", "formModel", "username", "password", "token", "formRule", "redirectPath", "queryPar", "watch", "$route", "location", "mounted", "methods", "goDomainEntry", "console", "window", "isOuterChain", "redirect", "par", "code", "loginType", "_DLZH", "_DLMM", "handleLogin", "loginPar", "dispatch", "then", "catch", "show", "text", "color", "handlePermission", "path", "query", "handleRegister", "handleSocialLogin"], "sourceRoot": "src/views/auth", "sources": ["Login.vue"], "sourcesContent": ["<template>\r\n  <div class=\"\">\r\n    <v-img class=\"img\" src=\"/static/defaultbg_2.png\" alt=\"\"></v-img>\r\n\r\n    <v-row class=\"page-login float\" fill-height>\r\n      <v-col :cols=\"12\">\r\n        <div class=\"logo1\">\r\n          <v-img class=\"float-left mx-15\" src=\"/static/Schneider_logo.png\" width=\"260px\" style=\"position: relative;margin-top: 0vh;\"></v-img>\r\n          <h2 class=\"float-right mx-15\" style=\"color: white; line-height: 82px;\">\r\n            <div style=\"margin-left: 2vw\">数字化工厂管理平台</div>\r\n          </h2>\r\n        </div>\r\n        <v-card class=\"d-flex flex-row mt-4\">\r\n          <v-img class=\"loginimg\" lazy-src=\"/static/Slogo_left_2.png\" src=\"/static/Slogo_left_2.png\"></v-img>\r\n          <v-card-text>\r\n            <v-form ref=\"form\" v-model=\"formValid\" class=\"ma-10\" width=\"500\" lazy-validation>\r\n              <v-text-field v-model=\"formModel.username\" append-icon=\"mdi-account\" autocomplete=\"off\"\r\n                            name=\"login\" autofocus :label=\"$t('username')\" :placeholder=\"$t('username')\" type=\"text\"\r\n                            required outlined :rules=\"formRule.username\" />\r\n              <v-text-field v-model=\"formModel.password\" append-icon=\"mdi-lock\" autocomplete=\"off\"\r\n                            name=\"password\" :label=\"$t('password')\" :placeholder=\"$t('password')\" type=\"password\"\r\n                            :rules=\"formRule.password\" required outlined @keyup.enter=\"handleLogin\" />\r\n              <v-btn large color=\"primary\" style=\"width: 100%\" :loading=\"loading\" @click=\"handleLogin\">\r\n                {{ $t('login') }}\r\n              </v-btn>\r\n            </v-form>\r\n            <div class=\"lang\">\r\n              <v-btn text color=\"primary\" @click=\"goDomainEntry()\">{{ $t('GLOBAL._YDL') }}</v-btn>\r\n              <LocaleSwitch />\r\n            </div>\r\n          </v-card-text>\r\n        </v-card>\r\n        <div class=\"copy\" align-center>CopyRight&copy;2022施耐德电气（中国）有限公司</div>\r\n      </v-col>\r\n    </v-row>\r\n    <v-row class=\"img my-0\" fill-height></v-row>\r\n    <!-- <div class=\"loading-box\">\r\n        <a-spin tip=\"系统正在初始化中...\" :spinning=\"loading\">\r\n        </a-spin>\r\n    </div> -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nconst name = 'page-login';\r\nimport { configUrl } from '@/config';\r\n\r\nconst SSO_URL = configUrl[process.env.VUE_APP_SERVE].SSO_URL;\r\nexport default {\r\n  name: name,\r\n  components: {\r\n    LocaleSwitch: () => import('@/components/locale/LocaleSwitch')\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      formValid: false,\r\n      formModel: {\r\n        username: '',\r\n        password: '',\r\n        token: ''\r\n      },\r\n      formRule: {\r\n        username: [v => !!v || this.$t('rule.requiredUsername')],\r\n        password: [v => !!v || this.$t('rule.requiredPassword')]\r\n      },\r\n      redirectPath: '/',\r\n      queryPar: {}\r\n    };\r\n  },\r\n  watch: {\r\n    $route() {\r\n      location.reload();\r\n    }\r\n  },\r\n  mounted() {\r\n    this.isOuterChain();\r\n  },\r\n  methods: {\r\n    // 跳转使用域登录\r\n    goDomainEntry() {\r\n      console.log(\"host\", window.document.location.host)\r\n      const callback = window.location.origin;\r\n      const url = `https://sso.aac.com/login.aspx?url=${decodeURIComponent(callback)}`;\r\n      window.location.href = url\r\n    },\r\n    isOuterChain() {\r\n      const o = this.$route.query;\r\n      const { redirect, par, code, loginType } = o;\r\n\r\n      if (loginType && loginType.toLowerCase() === 'sso') {\r\n        window.location.href = SSO_URL\r\n        return false\r\n      }\r\n\r\n      this.redirectPath = redirect;\r\n      // queryPar：外链跳转过来带的参数\r\n      this.queryPar = par ? JSON.parse(par) : {};\r\n      //_DLZH： 自动登录账号；_DLMM： 自动登录密码\r\n      const { _DLZH, _DLMM } = this.queryPar;\r\n      // this.$nextTick(()=>{\r\n\r\n      // })\r\n      if (_DLZH && _DLMM) {\r\n        console.log(_DLZH, _DLMM);\r\n        this.formModel.username = _DLZH;\r\n        this.formModel.password = _DLMM;\r\n        this.loginPar();\r\n      } else if (code) {\r\n        this.formModel.token = code\r\n        this.loginPar()\r\n      } else {\r\n        // this.goDomainEntry()\r\n      }\r\n    },\r\n    handleLogin() {\r\n      if (this.$refs.form.validate()) {\r\n        this.loginPar();\r\n      }\r\n    },\r\n    loginPar() {\r\n      this.loading = true;\r\n      this.$store\r\n          .dispatch('login', this.formModel)\r\n          .then(() => {\r\n            this.handlePermission();\r\n          })\r\n          .catch(() => {\r\n            window._VMA.$emit('SHOW_SNACKBAR', {\r\n              show: true,\r\n              text: '登录失败',\r\n              color: 'error'\r\n            });\r\n            this.loading = false;\r\n          });\r\n    },\r\n    handlePermission() {\r\n      this.$store\r\n          .dispatch('getPermission')\r\n          .then(() => {\r\n            this.loading = false;\r\n            // 登录成功，去掉地址栏账号密码\r\n            delete this.queryPar._DLZH;\r\n            delete this.queryPar._DLMM;\r\n            const route = this.redirectPath ? { path: this.redirectPath, query: this.queryPar } : { path: '/' };\r\n            this.$store.commit('SETFLAG', true)\r\n            this.$router.push(route);\r\n          })\r\n          .catch(() => {\r\n            this.$router.push('/404');\r\n          });\r\n    },\r\n    handleRegister() {\r\n      console.log(this);\r\n    },\r\n    handleSocialLogin() { }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-login {\r\n  position: relative;\r\n  min-width: 920px;\r\n  max-width: 920px;\r\n  margin: -221px auto;\r\n\r\n  // .loginimg {\r\n  //     .v-responsive__content {\r\n  //         background: rgba(255, 255, 255, 0.4) !important;\r\n  //     }\r\n  // }\r\n  .lang {\r\n    float: right;\r\n  }\r\n\r\n  .copy {\r\n    text-align: center;\r\n    margin-top: 48px;\r\n    color: #aaa;\r\n  }\r\n\r\n  .logo1 {\r\n    position: absolute;\r\n    top: -74px;\r\n  }\r\n}\r\n\r\n.img {\r\n  height: calc(50vh - 16px);\r\n}\r\n\r\n.loading-box {\r\n  position: fixed;\r\n  width: 100%;\r\n  height: 100%;\r\n  top: 0;\r\n  left: 0;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  // background: #fff;\r\n\r\n  // #3dcd58\r\n  ::v-deep .ant-spin-text {\r\n    color: #3dcd58;\r\n  }\r\n\r\n  ::v-deep .ant-spin-dot.ant-spin-dot-spin {\r\n    i {\r\n      background-color: #3dcd58;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}