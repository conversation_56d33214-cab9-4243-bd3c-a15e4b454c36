{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\productionManagement\\ResourceDefinition\\components\\drawer.vue?vue&type=style&index=0&id=9e399076&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\productionManagement\\ResourceDefinition\\components\\drawer.vue", "mtime": 1750254216359}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZHJhd2VyIHsNCiAgOmRlZXAoLmVsLWRyYXdlcl9fYm9keSkgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNGRkZGRkY7DQogICAgb3ZlcmZsb3cteTogaGlkZGVuDQogIH0NCg0KICA6ZGVlcCguZWwtZm9ybS0taW5saW5lKSB7DQogICAgaGVpZ2h0OiAzMnB4Ow0KICB9DQoNCiAgLnRpdGxlLWJveCB7DQogICAgZm9udC1zaXplOiAxOHB4Ow0KICAgIGNvbG9yOiAjOTA5Mzk5Ow0KICB9DQoNCiAgLm10NSB7DQogICAgbWFyZ2luLXRvcDogNXB4Ow0KICB9DQoNCiAgLnBkNSB7DQogICAgcGFkZGluZzogNXB4Ow0KICB9DQoNCiAgLnRhYmxlLWJveCB7DQogICAgcGFkZGluZzogMCAxMHB4Ow0KDQogICAgLmNvbWJpbmF0aW9uIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIH0NCg0KICAgIGkgew0KICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7DQogICAgICBmb250LXNpemU6IDE1cHggIWltcG9ydGFudDsNCiAgICAgIGNvbG9yOiAjNjdjMjNhOw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["drawer.vue"], "names": [], "mappings": ";AA6PA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "drawer.vue", "sourceRoot": "src/views/productionManagement/ResourceDefinition/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-drawer class=\"drawer\" :visible.sync=\"drawer\" :direction=\"'rtl'\" :before-close=\"handleClose\" size=\"50%\"\r\n      :with-header=\"!showSales\">\r\n      <div slot=\"title\" class=\"title-box\">\r\n        <span>{{ `${currentRow.Name} | ${currentRow.Code}` }}</span>\r\n      </div>\r\n      <section v-if=\"!showSales\">\r\n        <el-tabs v-model=\"tabIndex\" type=\"border-card\" @tab-click=\"changeTabs\">\r\n          <el-tab-pane label=\"工段\" name=\"1\"></el-tab-pane>\r\n          <el-tab-pane label=\"工序\" name=\"2\"></el-tab-pane>\r\n          <el-tab-pane label=\"工序与设备\" name=\"3\"></el-tab-pane>\r\n        </el-tabs>\r\n        <div class=\"InventorySearchBox mt5\">\r\n          <div class=\"searchbox pd5\">\r\n            <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\r\n              <el-form-item :label=\"$t('GLOBAL._SSL')\">\r\n                <el-input clearable v-model=\"searchForm.Key\"></el-input>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button icon=\"el-icon-search\" @click=\"getSearchBtn\">{{ $t('GLOBAL._CX') }}</el-button>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">{{\r\n                  $t('GLOBAL._XZ') }}\r\n                </el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n        </div>\r\n        <div class=\"table-box\">\r\n          <el-table v-loading=\"loading\" :data=\"tableData\" element-loading-text=\"拼命加载中\"\r\n            element-loading-spinner=\"el-icon-loading\" style=\"width: 100%\" height=\"83vh\">\r\n            <el-table-column v-for=\"(item, index) in tableName\" :key=\"index\" :prop=\"item.field\" :label=\"item.label\">\r\n              <template slot-scope=\"scope\">\r\n                <div v-if=\"item.field === 'SegmentName'\" class=\"combination\">\r\n                  <i v-if=\"tabIndex === '2'\" class=\"el-icon-document\" @click=\"showSalesView(scope.row)\"></i>\r\n                  <span>\r\n                   {{ scope.row.SegmentName }}\r\n                  </span>\r\n                </div>\r\n                <span v-else> {{ scope.row[item.field] }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._BJ') }}</el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </section>\r\n      <section v-else>\r\n        <SalesContainer ref=\"sales\" @show-sales=\"showSales = false\" :data=\"salesData\" @show-dialog=\"showSalesDialog\" />\r\n      </section>\r\n    </el-drawer>\r\n    <FormDialogOperation @saveForm=\"refreshTableData\" ref=\"operation\" />\r\n    <FromDialogPhase @saveForm=\"refreshTableData\" ref=\"phase\" />\r\n    <FromDialogPhaseMachine @saveForm=\"refreshTableData\" ref=\"phaseMachine\" />\r\n    <ForemDialogSales ref=\"salesDialog\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getSapSegmentList, getSapSegment, getSapSegmentEquipmentList, deleteOperation, deleteSapSegmentEquipment } from '@/api/productionManagement/Formula';\r\nimport FormDialogOperation from './form-dialog-operation.vue'\r\nimport FromDialogPhase from './form-dialog-phase.vue';\r\nimport FromDialogPhaseMachine from './form-dialog-phaseMachine'\r\nimport SalesContainer from './salesContainer.vue'\r\nimport ForemDialogSales from './form-dialog-sales.vue'\r\nexport default {\r\n  name: 'drawer',\r\n  components: {\r\n    FormDialogOperation,\r\n    FromDialogPhase,\r\n    FromDialogPhaseMachine,\r\n    SalesContainer,\r\n    ForemDialogSales\r\n  },\r\n  data() {\r\n    return {\r\n      searchForm: {},\r\n      tableData: [],\r\n      tableName: [],\r\n      loading: false,\r\n      drawer: false,\r\n      tabIndex: '1',\r\n      sapEquipmentId: 0,\r\n      hansObjOperation: this.$t('Formula.Resouce_Operation'),\r\n      hansObjPhase: this.$t('Formula.Resouce_Phase'),\r\n      hansObjPhaseMachine: this.$t('Formula.Resouce_Phase_Machine'),\r\n      parentId: 0,\r\n      currentRow: {},\r\n      showSales: false,\r\n      salesData: {}\r\n    }\r\n  },\r\n  methods: {\r\n    showSalesView(row) {\r\n      this.salesData = row\r\n      this.showSales = true\r\n      this.$nextTick(_ => {\r\n        this.$refs.sales.getTableData()\r\n      })\r\n    },\r\n    show(data) {\r\n      this.tabIndex = '1'\r\n      this.sapEquipmentId = data.ID\r\n      this.currentRow = data\r\n      this.getOperationData()\r\n      this.initTableHead(this.hansObjOperation)\r\n      this.drawer = true\r\n    },\r\n    handleClose() {\r\n      this.drawer = false\r\n    },\r\n    changeTabs() {\r\n      switch (this.tabIndex) {\r\n        case '1':\r\n          this.initTableHead(this.hansObjOperation)\r\n          this.getOperationData()\r\n          break;\r\n        case '2':\r\n          this.initTableHead(this.hansObjPhase)\r\n          this.getPhaseData()\r\n          break;\r\n        case '3':\r\n          this.initTableHead(this.hansObjPhaseMachine)\r\n          this.getPhaseMechineData()\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    showDialog(val) {\r\n      switch (this.tabIndex) {\r\n        case '1':\r\n          val.SapEquipmentId = this.sapEquipmentId\r\n          this.$refs.operation.show(val)\r\n          break;\r\n        case '2':\r\n          val.SapEquipmentId = this.sapEquipmentId\r\n          this.$refs.phase.show(val)\r\n          break;\r\n        case '3':\r\n          val.SapEquipmentId = this.sapEquipmentId\r\n          this.$refs.phaseMachine.show(val)\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    async delRow({ ID }) {\r\n      this.$confirms({\r\n        title: this.$t('GLOBAL._TS'),\r\n        message: this.$t('GLOBAL._COMFIRM'),\r\n        confirmText: this.$t('GLOBAL._QD'),\r\n        cancelText: this.$t('GLOBAL._QX')\r\n      }).then(() => {\r\n        switch (this.tabIndex) {\r\n          case '1':\r\n            deleteOperation([ID]).then(({ msg }) => {\r\n              this.$message.success(msg)\r\n              this.getOperationData()\r\n            })\r\n            break;\r\n          case '2':\r\n            deleteOperation([ID]).then(({ msg }) => {\r\n              this.$message.success(msg)\r\n              this.getPhaseData()\r\n            })\r\n            break;\r\n          case '3':\r\n            deleteSapSegmentEquipment([ID]).then(({ msg }) => {\r\n              this.$message.success(msg)\r\n              this.getPhaseMechineData()\r\n            })\r\n            break;\r\n          default:\r\n            break;\r\n        }\r\n      }).catch(err => {\r\n        console.log(err);\r\n      });\r\n\r\n    },\r\n    // emit刷新数据\r\n    refreshTableData() {\r\n      switch (this.tabIndex) {\r\n        case '1':\r\n          this.getOperationData()\r\n          break;\r\n        case '2':\r\n          this.getPhaseData()\r\n          break;\r\n        case '3':\r\n          this.getPhaseMechineData()\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    // 获取数据\r\n    async getOperationData() {\r\n      this.loading = true\r\n      const { response } = await getSapSegmentList({\r\n        Level: 1,\r\n        SapEquipmentId: this.sapEquipmentId,\r\n        ...this.searchForm\r\n      })\r\n      this.loading = false\r\n      this.tableData = response\r\n    },\r\n    async getPhaseData() {\r\n      this.loading = true\r\n      const { response } = await getSapSegmentList({\r\n        Level: 2,\r\n        SapEquipmentId: this.sapEquipmentId,\r\n        ...this.searchForm\r\n      })\r\n      this.loading = false\r\n      this.tableData = response\r\n    },\r\n    async getPhaseMechineData() {\r\n      this.loading = true\r\n      const { response } = await getSapSegmentEquipmentList({\r\n        SapEquipmentId: this.sapEquipmentId,\r\n        ...this.searchForm\r\n      })\r\n      this.loading = false\r\n      this.tableData = response\r\n    },\r\n    // 初始化表头\r\n    initTableHead(obj) {\r\n      this.tableName = []\r\n      for (let key in obj) {\r\n        this.tableName.push({ field: key, label: obj[key] })\r\n      }\r\n    },\r\n    // 搜索\r\n    getSearchBtn() {\r\n      this.refreshTableData()\r\n    },\r\n    showSalesDialog(row) {\r\n      // console.log(row);\r\n      this.$refs.salesDialog.show(row)\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.drawer {\r\n  :deep(.el-drawer__body) {\r\n    background-color: #FFFFFF;\r\n    overflow-y: hidden\r\n  }\r\n\r\n  :deep(.el-form--inline) {\r\n    height: 32px;\r\n  }\r\n\r\n  .title-box {\r\n    font-size: 18px;\r\n    color: #909399;\r\n  }\r\n\r\n  .mt5 {\r\n    margin-top: 5px;\r\n  }\r\n\r\n  .pd5 {\r\n    padding: 5px;\r\n  }\r\n\r\n  .table-box {\r\n    padding: 0 10px;\r\n\r\n    .combination {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n\r\n    i {\r\n      margin-right: 5px;\r\n      font-size: 15px !important;\r\n      color: #67c23a;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}