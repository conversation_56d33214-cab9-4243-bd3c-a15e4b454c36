{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\components\\POManagement.vue?vue&type=style&index=0&id=bd3f6e92&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\components\\POManagement.vue", "mtime": 1750254216282}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["POManagement.vue"], "names": [], "mappings": ";AAozCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "POManagement.vue", "sourceRoot": "src/views/Producting/Overview/components", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle POManagement\">\r\n        <div class=\"subsubtabs\">\r\n            <el-tabs v-model=\"activeName\" type=\"border-card\" @tab-click=\"handleClick\">\r\n                <el-tab-pane :label=\"$t('Overview.AvailableOrders') + `(${Availablenum})`\" name=\"Available\">\r\n                    <div class=\"InventorySearchBox\">\r\n                        <div class=\"searchbox\">\r\n                            <div class=\"datebox\">\r\n                                <div class=\"datepickbox\">\r\n                                    <el-date-picker\r\n                                        v-model=\"timepicker\"\r\n                                        type=\"daterange\"\r\n                                        value-format=\"yyyy-MM-dd\"\r\n                                        range-separator=\"-\"\r\n                                        :start-placeholder=\"$t('DFM_RL._KSRQ')\"\r\n                                        :end-placeholder=\"$t('DFM_RL._JSRQ')\"\r\n                                    ></el-date-picker>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"inputformbox\" :style=\"{ width: item.width }\" v-for=\"(item, index) in searchlist\" :key=\"index\">\r\n                                <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\" :myid=\"item.id\" :placeholder=\"item.name\"></el-input>\r\n                                <el-select :style=\"{ width: item.width }\" v-model=\"item.value\" v-if=\"item.type == 'select'\" :myid=\"item.id\" :placeholder=\"item.name\">\r\n                                    <el-option v-for=\"(it, ind) in item.option\" :key=\"ind\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                                </el-select>\r\n                            </div>\r\n                            <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                            <el-button size=\"small\" style=\"margin-left: 5px\" icon=\"el-icon-s-help\" @click=\"getempty()\">{{ this.$t('GLOBAL._CZ') }}</el-button>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"tablebox\">\r\n                        <el-table border :data=\"AvailablePOManagemenList\" style=\"width: 100%\" height=\"520\">\r\n                            <el-table-column\r\n                                v-for=\"(item, index) in header\"\r\n                                :key=\"index\"\r\n                                :align=\"item.align\"\r\n                                :prop=\"item.prop ? item.prop : item.value\"\r\n                                :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                                :width=\"item.width\"\r\n                            >\r\n                                <template slot-scope=\"scope\">\r\n                                    <span v-if=\"scope.column.property == 'operate'\">\r\n                                        <el-button\r\n                                            size=\"mini\"\r\n                                            class=\"operatebtn\"\r\n                                            v-if=\"scope.row.ExecutionStatus == null || scope.row.ExecutionStatus == 3\"\r\n                                            @click=\"startOrder(scope)\"\r\n                                            icon=\"el-icon-video-play\"\r\n                                        >\r\n                                            {{ $t('Overview.start') }}\r\n                                        </el-button>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'PlanStartTime'\">{{ $dayjs(scope.row.PlanStartTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                                    <span v-else-if=\"scope.column.property == 'PlanEndTime'\">{{ $dayjs(scope.row.PlanEndTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                                    <span v-else-if=\"scope.column.property == 'Segment'\">\r\n                                        <div>{{ scope.row.SegmentCode }}</div>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'IsHavePreservative'\">\r\n                                        <i :class=\"scope.row[item.value] === '1' ? 'el-icon-star-on' : ''\"></i>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'LineNominalSpeed'\">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>\r\n                                    <span v-else>{{ scope.row[item.prop] }}</span>\r\n                                </template>\r\n                            </el-table-column>\r\n                        </el-table>\r\n                    </div>\r\n                </el-tab-pane>\r\n                <el-tab-pane :label=\"$t('Overview.ActiveOrders') + `(${Activenum})`\" name=\"Active\">\r\n                    <div class=\"InventorySearchBox\">\r\n                        <div class=\"searchbox\">\r\n                            <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                            <el-button\r\n                                class=\"tablebtn\"\r\n                                :disabled=\"tablechooselist > 0 ? false : true\"\r\n                                size=\"small\"\r\n                                @click=\"stopBtn()\"\r\n                                style=\"margin-left: 5px; width: 12vh\"\r\n                                icon=\"el-icon-circle-close\"\r\n                            >\r\n                                {{ this.$t('Overview.Stop') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <!-- <el-button class=\"tablebtn\" @click=\"holdBtn()\" :disabled=\"tablechooselist > 0 ? false : true\" size=\"small\" style=\"margin-left: 5px; width: 12vh\" icon=\"el-icon-video-pause\">\r\n                                {{ this.$t('Overview.Hold') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <el-button\r\n                                class=\"tablebtn\"\r\n                                @click=\"ResumeBtn()\"\r\n                                :disabled=\"tablechooselist > 0 ? false : true\"\r\n                                size=\"small\"\r\n                                style=\"margin-left: 5px; width: 14vh\"\r\n                                icon=\"el-icon-video-play\"\r\n                            >\r\n                                {{ this.$t('Overview.Resume') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button> -->\r\n                            <el-button class=\"tablebtn\" @click=\"updateBtn()\" :disabled=\"tablechooselist > 0 ? false : true\" size=\"small\" style=\"margin-left: 5px; width: 16vh\" icon=\"el-icon-setting\">\r\n                                {{ this.$t('Overview.UpdateOrder') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <el-button\r\n                                class=\"tablebtn\"\r\n                                @click=\"updateRemarkBtn()\"\r\n                                :disabled=\"tablechooselist > 0 ? false : true\"\r\n                                size=\"small\"\r\n                                style=\"margin-left: 5px; width: 16vh\"\r\n                                icon=\"el-icon-setting\"\r\n                            >\r\n                                {{ this.$t('Overview.UpdateRemark') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <!-- <el-button\r\n                                class=\"tablebtn\"\r\n                                @click=\"startNextBatchBtn()\"\r\n                                :disabled=\"tablechooselist > 0 && Number(selectTabelData.Number) < selectTabelData.BatchCount ? false : true\"\r\n                                size=\"small\"\r\n                                style=\"margin-left: 5px; width: 16vh\"\r\n                                icon=\"el-icon-setting\"\r\n                            >\r\n                                {{ this.$t('Overview.NextBatch') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <el-button class=\"tablebtn\" @click=\"AutoReport()\" :disabled=\"tablechooselist > 0 ? false : true\" size=\"small\" style=\"margin-left: 5px; width: 16vh\" icon=\"el-icon-setting\">\r\n                                {{ this.$t('Overview.AutoReport') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button> -->\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"tablebox\">\r\n                        <el-table border :data=\"ActivePOManagemenList\" ref=\"ActiveTable\" highlight-current-row @current-change=\"handleSelectionChange\" style=\"width: 100%\" height=\"520\">\r\n                            <!-- <el-table-column type=\"selection\" width=\"55\" fixed=\"left\"></el-table-column> -->\r\n                            <el-table-column\r\n                                v-for=\"(item, index) in Activeheader\"\r\n                                :key=\"index\"\r\n                                :align=\"item.align\"\r\n                                :prop=\"item.prop ? item.prop : item.value\"\r\n                                :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                                :width=\"item.width\"\r\n                            >\r\n                                <template slot-scope=\"scope\">\r\n                                    <span v-if=\"scope.column.property == 'ProcessOrder'\">\r\n                                        <div>{{ scope.row.ProcessOrder }}({{ scope.row.Number }})</div>\r\n                                    </span>\r\n                                    <!-- <span v-else-if=\"scope.column.property == 'Status'\">\r\n                                        {{ getExecutionhStatus(scope.row.Status) }}\r\n                                    </span> -->\r\n                                    <!-- <span v-else-if=\"scope.column.property == 'LineNominalSpeed'\">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>-->\r\n                                    <span v-else>{{ scope.row[item.prop] }}</span> \r\n                                </template>\r\n                            </el-table-column>\r\n                        </el-table>\r\n                    </div>\r\n                </el-tab-pane>\r\n\r\n                <el-tab-pane :label=\"$t('Overview.History')\" name=\"History\">\r\n                    <div class=\"InventorySearchBox\">\r\n                        <div class=\"searchbox\">\r\n                            <div class=\"datebox\">\r\n                                <div class=\"datepickbox\">\r\n                                    <el-date-picker\r\n                                        v-model=\"timepicker\"\r\n                                        type=\"daterange\"\r\n                                        value-format=\"yyyy-MM-dd\"\r\n                                        range-separator=\"-\"\r\n                                        :start-placeholder=\"$t('DFM_RL._KSRQ')\"\r\n                                        :end-placeholder=\"$t('DFM_RL._JSRQ')\"\r\n                                    ></el-date-picker>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"inputformbox\" :style=\"{ width: item.width }\" v-for=\"(item, index) in searchlist\" :key=\"index\">\r\n                                <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\" :myid=\"item.id\" :placeholder=\"item.name\"></el-input>\r\n                            </div>\r\n                            <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                            <el-button size=\"small\" style=\"margin-left: 5px\" icon=\"el-icon-s-help\" @click=\"getempty()\">{{ this.$t('GLOBAL._CZ') }}</el-button>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"tablebox\">\r\n                        <el-table border :data=\"HistroyList\" style=\"width: 100%\" height=\"520\">\r\n                            <el-table-column\r\n                                v-for=\"(item, index) in Historyheader\"\r\n                                :key=\"index\"\r\n                                :align=\"item.align\"\r\n                                :prop=\"item.prop ? item.prop : item.value\"\r\n                                :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                                :width=\"item.width\"\r\n                            >\r\n                                <template slot-scope=\"scope\">\r\n                                    <span v-if=\"scope.column.property == 'Material'\">\r\n                                        <div>{{ scope.row.MaterialCode }}</div>\r\n                                        <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'SAP'\">\r\n                                        <div>{{ scope.row.SegmentCode }}</div>\r\n                                        <div style=\"color: #808080\">{{ scope.row.SegmentName }}</div>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'Status'\">\r\n                                        {{ getExecutionhStatus(scope.row.Status) }}\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'NominalSpeed'\">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>\r\n                                    <span v-else>{{ scope.row[item.prop] }}</span>\r\n                                </template>\r\n                            </el-table-column>\r\n                        </el-table>\r\n                    </div>\r\n                </el-tab-pane>\r\n            </el-tabs>\r\n            <div class=\"paginationbox\">\r\n                <el-pagination\r\n                    @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageOptions.page\"\r\n                    :page-sizes=\"pageOptions.pageSizeitems\"\r\n                    :page-size=\"pageOptions.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next\"\r\n                    :total=\"pageOptions.total\"\r\n                    background\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Overview.StartOrder')\" id=\"Startdialog\" :visible.sync=\"StartModel\" :width=\"IsPack == '0' ? '650px' : '650px'\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">\r\n                    {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.StartOrder') }}\r\n                    <div class=\"dialogsubtitlebox\" style=\"display: inline\">{{ chooseItem.ProcessOrder }}</div>\r\n                </div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailboxtitle\">\r\n                    {{ chooseItem.MaterialCode }}-{{ chooseItem.MaterialName }}\r\n                    <el-tag class=\"splitdetailboxtitleTag\" size=\"small\" v-if=\"Activenum != 0\">{{ ActivePOManagemenList[0] ? ActivePOManagemenList[0].ProcessOrder : '' }}</el-tag>\r\n                </div>\r\n                <div class=\"detailsnote\" v-if=\"runningCode != '' && !chooseItem.isResume\">\r\n                    {{ $t('Overview.Note1') }}\r\n                    <span style=\"font-weight: 600\">{{ runningCode }}</span>\r\n                    {{ $t('Overview.Note2') }}\r\n                </div>\r\n                <div style=\"display: flex\">\r\n                    <div :style=\"{ width: IsPack == '0' ? '100%' : '100%' }\">\r\n                        <div class=\"dialogdetailbox\" v-for=\"(item, index) in Startlist\" :key=\"index\">\r\n                            <div class=\"dialogdetailsinglelabel\" :style=\"{ width: item.type == 'BatchCode' ? '20%' : '20%' }\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                            <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: item.type == 'BatchCode' || item.type == 'checkBox' ? '400px' : '77%' }\">\r\n                                <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                               \r\n                                <div v-else-if=\"item.type == 'BatchCode'\" style=\"display: flex\">\r\n                                    <el-input v-model=\"item.value\"></el-input>\r\n                                    <el-input v-model=\"item.value2\" disabled></el-input>\r\n                                    <el-input v-model=\"item.value3\"></el-input>\r\n                                    <el-button\r\n                                        class=\"tablebtn\"\r\n                                        @click=\"getBatchCode()\"\r\n                                        size=\"mini\"\r\n                                        style=\"margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff\"\r\n                                        icon=\"el-icon-refresh\"\r\n                                    ></el-button>\r\n                                </div>\r\n                                <el-select clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                                    <el-option v-for=\"it in item.option\" :key=\"it.ID\" :label=\"it.Number\" :value=\"it.ID\"></el-option>\r\n                                </el-select>\r\n                                <el-date-picker\r\n                                    @change=\"GetDate(item.id)\"\r\n                                    v-else-if=\"item.type == 'date'\"\r\n                                    value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                                    :disabled=\"item.disabled\"\r\n                                    v-model=\"item.value\"\r\n                                    type=\"datetime\"\r\n                                ></el-date-picker>\r\n                                <span v-else-if=\"item.id == 'TargetQuantity'\">{{ chooseItem.TargetQuantity }}{{ chooseItem.Unit1 }}</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button style=\"float: left\" v-if=\"chooseItem.isResume\">\r\n                    {{ $t('Overview.bottleneck') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" :disabled=\"IsDifferent\" icon=\"el-icon-video-play\" @click=\"ProducedStart()\">\r\n                    {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.Start') }}\r\n                </el-button>\r\n                <el-button @click=\"StartModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog id=\"Stopdialog\" :visible.sync=\"stopModel\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">\r\n                    {{ $t('Overview.StopNote') }}\r\n                </div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailboxtitle\">\r\n                    {{ selectTabelData != {} ? selectTabelData.MaterialName + '-' + selectTabelData.MaterialCode : '' }}\r\n                    <el-tag class=\"splitdetailboxtitleTag\" size=\"small\">{{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</el-tag>\r\n                </div>\r\n                <div class=\"dialogdetailbox\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ $t('Overview.EndTime') }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-date-picker disabled v-model=\"EndTime\" type=\"datetime\"></el-date-picker>\r\n                    </div>\r\n                </div>\r\n                <div v-if=\"isComplete == true && selectTabelData.NeedQARelease == '1'\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Completelist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <el-select @change=\"getData2(item)\" v-model=\"item.value\" clearable filterable :disabled=\"item.id == 'ProduceStatus'\" v-if=\"item.type == 'select'\">\r\n                                <el-option v-for=\"(it, ind) in item.options\" :key=\"ind\" :label=\"it.label\" :value=\"it.key\"></el-option>\r\n                            </el-select>\r\n                            <span v-else>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <div style=\"float: left\">\r\n                    <el-checkbox v-model=\"isComplete\">{{ $t('Overview.CompletePO') }}</el-checkbox>\r\n                </div>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-circle-close\" @click=\"StopProduced()\">\r\n                    {{ $t('Overview.Stop') }}\r\n                </el-button>\r\n                <el-button @click=\"stopModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog id=\"Holddialog\" :visible.sync=\"HoldModel\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">\r\n                    {{ $t('Overview.HoldNote') }}\r\n                </div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailboxtitle\">\r\n                    {{ selectTabelData != {} ? selectTabelData.MaterialName + '-' + selectTabelData.MaterialCode : '' }}\r\n                    <el-tag class=\"splitdetailboxtitleTag\" size=\"small\">{{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</el-tag>\r\n                </div>\r\n                <div class=\"dialogdetailbox\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ $t('Overview.EndTime') }} *</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-date-picker disabled v-model=\"EndTime\" type=\"datetime\"></el-date-picker>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <div style=\"float: left\">\r\n                    <el-checkbox v-model=\"isComplete\">{{ $t('Overview.CompletePO') }}</el-checkbox>\r\n                </div>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-video-pause\" @click=\"HoldProduced()\">\r\n                    {{ $t('Overview.Hold') }}\r\n                </el-button>\r\n                <el-button @click=\"HoldModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog id=\"Updatedialog\" :visible.sync=\"UpdateModel\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">{{ $t('Overview.UpdateNote') }} {{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Updatelist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <span>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Updateinputlist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: item.type == 'BatchCode' ? '20%' : '20%' }\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: item.type == 'BatchCode' ? '400px' : '77%' }\">\r\n                            <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n\r\n                            <div v-else-if=\"item.type == 'BatchCode'\" style=\"display: flex\">\r\n                                <el-input v-model=\"item.value\"></el-input>\r\n                                <el-input v-model=\"item.value2\" disabled></el-input>\r\n                                <el-input v-model=\"item.value3\"></el-input>\r\n                                <el-button\r\n                                    :disabled=\"Updateinputlist[0].value == ''\"\r\n                                    class=\"tablebtn\"\r\n                                    @click=\"getBatchCode2()\"\r\n                                    size=\"mini\"\r\n                                    style=\"margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff\"\r\n                                    icon=\"el-icon-refresh\"\r\n                                ></el-button>\r\n                            </div>\r\n                            <el-select clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                                <el-option v-for=\"it in item.option\" :key=\"it.ID\" :label=\"it.BatchCode\" :value=\"it.ID\"></el-option>\r\n                            </el-select>\r\n                            <el-date-picker v-else-if=\"item.type == 'date'\" :type=\"item.datetype\" v-model=\"item.value\"></el-date-picker>\r\n                            <!-- <el-date-picker @change=\"GetDate2(item.id)\" v-else-if=\"item.type == 'date'\" :type=\"item.datetype\" v-model=\"item.value\"></el-date-picker> -->\r\n                            <span v-else>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-setting\" @click=\"UpdateProduced()\">\r\n                    {{ $t('Overview.UpdateOrder') }}\r\n                </el-button>\r\n                <el-button @click=\"UpdateModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog id=\"Updatedialog2\" :visible.sync=\"UpdateRemark\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">{{ $t('Overview.UpdateNote') }} {{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Updatelist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <span>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" key=\"remark\">\r\n                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: '20%' }\">{{ $t('Overview.Comments') }}</div>\r\n                        <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: '77%' }\">\r\n                            <el-input v-model=\"Remark\"></el-input>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-setting\" @click=\"UpdateOrderRemark()\">\r\n                    {{ $t('Overview.UpdateRemark') }}\r\n                </el-button>\r\n                <el-button @click=\"UpdateRemark = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { POManagemenAvailable, POManagemenActive, POManagemenHistory } from '@/columns/factoryPlant/tableHeaders';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { GetDataTreeList, GetDataItemList } from '@/api/factoryPlant/process.js';\r\nimport {\r\n    GetProcessOrderView,\r\n    GetProcessOrderView2,\r\n    GetBBatchListView,\r\n    GetBatchCode,\r\n    PoProducedStart,\r\n    PoProducedStop,\r\n    PoProducedHold,\r\n    PoProducedResume,\r\n    PoProducedUpdatePo,\r\n    AutoReport,\r\n    UpdateOrderRemark,\r\n    PoExecutionHistroy,\r\n    GetRunOrder,\r\n    GetEquipmentProcessOrderView,\r\n    StartNextBatch\r\n} from '@/api/Inventory/Overview.js';\r\nimport moment from 'moment';\r\nimport { ConsoleLogger } from '@microsoft/signalr/dist/esm/Utils';\r\n\r\nexport default {\r\n    name: 'POManagement',\r\n\r\n    data() {\r\n        return {\r\n            Completelist: [\r\n                {\r\n                    label: this.$t('POList.PlanQty'),\r\n                    id: 'PlanQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.ActualQty'),\r\n                    id: 'ActualQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.ProduceStatus'),\r\n                    id: 'ProduceStatus',\r\n                    require: true,\r\n                    type: 'select',\r\n                    options: [\r\n                        {\r\n                            key: 'NotComplete',\r\n                            label: this.$t('POList.NotComplete')\r\n                        },\r\n                        {\r\n                            key: 'OverComplete',\r\n                            label: this.$t('POList.OverComplete')\r\n                        },\r\n                        {\r\n                            key: 'CompleteAtOnce',\r\n                            label: this.$t('POList.CompleteAtOnce')\r\n                        }\r\n                    ],\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.Reason'),\r\n                    id: 'Reason',\r\n                    type: 'select',\r\n                    require: true,\r\n                    options: [],\r\n                    value: ''\r\n                }\r\n            ],\r\n            viewtitle: '',\r\n            timepicker: [moment(new Date()).format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')],\r\n            tableId: 'PRO_POManagement',\r\n            runningCode: '',\r\n            header: POManagemenAvailable,\r\n            Activeheader: POManagemenActive,\r\n            Historyheader: POManagemenHistory,\r\n            ActivePOManagemenList: [],\r\n            Activenum: 0,\r\n            AvailablePOManagemenList: [],\r\n            Availablenum: 0,\r\n            HistroyList: [],\r\n            Histroynum: 0,\r\n            searchlist: [\r\n                {\r\n                    type: 'input',\r\n                    name: this.$t('Overview.QuickSearch'),\r\n                    id: 'QuickSearch',\r\n                    value: ''\r\n                }\r\n            ],\r\n            pageOptions: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            activeName: 'Available',\r\n            StartModel: false,\r\n            Startlist: [\r\n                {\r\n                    label: this.$t('Overview.StartTime'),\r\n                    id: 'StartTime',\r\n                    value: '',\r\n                    disabled: true,\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.Batch'),\r\n                    id: 'BatchId',\r\n                    value: '',\r\n                    require: true,\r\n                    type: 'select',\r\n                    option: []\r\n                },\r\n                {\r\n                    label: this.$t('Overview.BatchCode'),\r\n                    id: 'LotCode',\r\n                    value: '',\r\n                    value2: '',\r\n                    value3: '',\r\n                    require: true,\r\n                    type: 'BatchCode'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ExpirationDate'),\r\n                    id: 'ExpirationDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date',\r\n                    disabled: true\r\n                },\r\n                {\r\n                    label: this.$t('Overview.TargetQuantity'),\r\n                    id: 'TargetQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Overview.CrewSize'),\r\n                    id: 'CrewSize',\r\n                    value: '',\r\n                    type: 'input'\r\n                },\r\n                {\r\n                    label: '',\r\n                    id: 'IsUpdateLtxt',\r\n                    value: false,\r\n                    type: 'checkBox'\r\n                }\r\n            ],\r\n            chooseItem: {\r\n                ProcessOrder: '',\r\n                isResume: false,\r\n                TargetQuantity: '',\r\n                Unit1: '',\r\n                MaterialCode: '',\r\n                MaterialName: ''\r\n            },\r\n            tablechooselist: 0,\r\n            selectTabelData: {},\r\n            stopModel: false,\r\n            EndTime: new Date(),\r\n            isComplete: false,\r\n            HoldModel: false,\r\n            UpdateModel: false,\r\n            UpdateRemark: false,\r\n            Updatelist: [\r\n                {\r\n                    label: this.$t('Overview.Material'),\r\n                    value: '',\r\n                    id: 'Material'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.TargetQuantity'),\r\n                    value: '',\r\n                    id: 'TargetQuantity'\r\n                }\r\n            ],\r\n            Updateinputlist: [\r\n                {\r\n                    label: this.$t('Overview.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    value: '',\r\n                    require: true,\r\n                    type: 'date',\r\n                    datetype: 'datetime'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.BatchCode'),\r\n                    id: 'LotCode',\r\n                    value: '',\r\n                    value2: '',\r\n                    value3: '',\r\n                    require: true,\r\n                    type: 'BatchCode'\r\n                },\r\n                // {\r\n                //     label: this.$t('Overview.DefaultBatchCode'),\r\n                //     id: 'DefaultBatchCode',\r\n                //     value: ''\r\n                // },\r\n\r\n                {\r\n                    label: this.$t('Overview.ExpirationDate'),\r\n                    id: 'ExpirationDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date',\r\n                    datetype: 'datetime'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.Comments'),\r\n                    id: 'Comments',\r\n                    require: false,\r\n                    value: '',\r\n                    type: 'input'\r\n                }\r\n            ],\r\n            Remark: '',\r\n            ExecutionhStatus: [],\r\n            EquipmentId: '',\r\n            EquipmentCode: '',\r\n            IsPack: '1',\r\n            Text1: '',\r\n            Text2: '',\r\n            productionId: '',\r\n            isEdit: false,\r\n            IsDifferent: false,\r\n            ReasonList: []\r\n        };\r\n    },\r\n    methods: {\r\n        getEquipmentModal(item, Equipmentitem) {\r\n            this.productionId = item.ProductionOrderId;\r\n            this.EquipmentId = item.ID;\r\n            this.EquipmentCode = item.EquipmentCode;\r\n            // if (Equipmentitem) {\r\n            // } else {\r\n            //     // this.EquipmentId = '';\r\n            //     // this.EquipmentCode = '';\r\n            // }\r\n            this.getStatus();\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        },\r\n        async getStatus() {\r\n            this.ReasonList = [];\r\n            this.Completelist[2].options.forEach(async item => {\r\n                let p = {\r\n                    ItemCode: item.key\r\n                };\r\n                const res = await GetDataTreeList(p);\r\n                res.response.data.forEach(item1 => {\r\n                    this.ReasonList.push(item1);\r\n                });\r\n            });\r\n        },\r\n        getReasonName(key1, key2) {\r\n            if (key1 === null || key1 === '' || key2 === null || key2 === '') {\r\n                return '';\r\n            }\r\n            let name = key2;\r\n            //console.log(this._i18n.locale);\r\n            if (this._i18n.locale == 'en') {\r\n                return name;\r\n            }\r\n            this.ReasonList.forEach(item => {\r\n                if (item.ItemCode === key1 && item.ItemValue === key2) {\r\n                    name = item.ItemName;\r\n                }\r\n            });\r\n            //console.log(name);\r\n            return name;\r\n        },\r\n        calculateDate(days) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + days);\r\n            return this.formatDate(date);\r\n        },\r\n        async getStauts() {\r\n            let params2 = {\r\n                ItemCode: 'ProductionExecutionStatus'\r\n            };\r\n            const res2 = await GetDataItemList(params2);\r\n            let data2 = res2.response;\r\n            this.ExecutionhStatus = data2;\r\n        },\r\n        getExecutionhStatus(val) {\r\n            if (val) {\r\n                let name = '';\r\n                this.ExecutionhStatus.forEach(item => {\r\n                    if (item.ItemValue == val) {\r\n                        name = item.ItemName;\r\n                    }\r\n                });\r\n                return name;\r\n            }\r\n        },\r\n        getsearch() {\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.getStauts();\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        },\r\n        getempty() {\r\n            this.QuickSearch = '';\r\n            this.timepicker = [];\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.searchlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        },\r\n        async getPoExecutionHistroy() {\r\n            if (this.timepicker == null) {\r\n                this.timepicker = [];\r\n            }\r\n            let params = {\r\n                Key: this.searchlist[0].value,\r\n                RunEquipmentId: this.EquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize,\r\n                StartTime: this.timepicker[0],\r\n                EndTime: this.timepicker[1] == undefined ? '' : this.timepicker[1] + ' 23:59:59'\r\n            };\r\n            let res = await PoExecutionHistroy(params);\r\n            this.HistroyList = res.response.data;\r\n            this.Histroynum = res.response.dataCount;\r\n            if (this.activeName == 'History') {\r\n                this.pageOptions.total = this.Histroynum;\r\n            }\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n        },\r\n        async getBatchList() {\r\n            let params = {\r\n                PoSegmentRequirementId: this.chooseItem.ID\r\n            };\r\n            let res = await GetBBatchListView(params);\r\n            this.Startlist[1].option = res.response;\r\n            this.Startlist[1].value = this.Startlist[1].option[0].ID;\r\n            this.StartModel = true;\r\n        },\r\n        async getData2(item) {\r\n            if (item.id == 'ProduceStatus') {\r\n                if (item.value != '') {\r\n                    let res = await this.$getNewDataDictionary(item.value);\r\n                    //let res = this.ReasonList.find(x=>x.ItemCode = item.value)\r\n                    // console.log(res);\r\n                    let data = res;\r\n                    if (data.length > 0) {\r\n                        data.forEach(item1 => {\r\n                            item1.key = item1.ItemValue;\r\n                            // console.log(this._i18n.locale);\r\n                            item1.label = this._i18n.locale === 'en' ? item1.ItemValue : item1.ItemName;\r\n                        });\r\n                    }\r\n                    this.Completelist[3].options = data;\r\n                    this.Completelist[3].value = '';\r\n                }\r\n            }\r\n        },\r\n        async getLtext(id) {\r\n            if (this.IsPack === '0') {\r\n                this.IsDifferent = false;\r\n                let params = {\r\n                    id: id\r\n                };\r\n                // let r = await GetCookOrderLtexts(params);\r\n                // if (r.response.length == 2) {\r\n                //     if (r.msg === '长文本不一致！') {\r\n                //         this.IsDifferent = true;\r\n                //     }\r\n                //     this.Text1 = r.response[0].ProcessData;\r\n                //     this.Text2 = r.response[1].ProcessData;\r\n                // }\r\n            }\r\n        },\r\n        async getBatchCode() {\r\n            let date = moment(this.Startlist[3].value).format('YYYY-MM-DD HH:mm:ss');\r\n            let p = {\r\n                LineCode: this.Startlist[2].value,\r\n                equipmentCode: this.EquipmentCode,\r\n                productionDate: date,\r\n                productionId: this.chooseItem.ProductionOrderId\r\n            };\r\n            let res = await GetBatchCode(p);\r\n            if (res.response == null) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'warning'\r\n                });\r\n            } else {\r\n                this.Startlist[2].value = res.response.substring(0, 2);\r\n                this.Startlist[2].value2 = res.response.substring(2, 5);\r\n            }\r\n        },\r\n        async getBatchCode2() {\r\n            let date = moment(this.Updateinputlist[0].value).format('YYYY-MM-DD HH:mm:ss');\r\n            let p = {\r\n                LineCode: this.Updateinputlist[1].value,\r\n                equipmentCode: this.EquipmentCode,\r\n                productionDate: date,\r\n                productionId: this.selectTabelData.ProductionOrderId\r\n            };\r\n            let res = await GetBatchCode(p);\r\n            if (res.response == null) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'warning'\r\n                });\r\n            } else {\r\n                this.Updateinputlist[1].value = res.response.substring(0, 2);\r\n                this.Updateinputlist[1].value2 = res.response.substring(2, 5);\r\n            }\r\n        },\r\n        async GetProcessOrderView() {\r\n            if (this.timepicker == null) {\r\n                this.timepicker = [];\r\n            }\r\n            let params = {\r\n                Search: this.searchlist[0].value,\r\n                EquipmentId: this.EquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize,\r\n                StartTime: this.timepicker[0],\r\n                EndTime: this.timepicker[1] == undefined ? '' : this.timepicker[1] + ' 23:59:59'\r\n            };\r\n            let res = await GetProcessOrderView(params);\r\n            this.AvailablePOManagemenList = res.response.data;\r\n            this.Availablenum = res.response.dataCount;\r\n            let params2 = {\r\n                Search: this.searchlist[0].value,\r\n                EquipmentId: this.EquipmentId,\r\n                ExecutionStatus: ['1', '5'],\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res2 = await GetProcessOrderView2(params2);\r\n            this.ActivePOManagemenList = res2.response.data;\r\n            this.Activenum = res2.response.dataCount;\r\n            if (this.activeName == 'Active') {\r\n                this.pageOptions.total = this.Activenum;\r\n            } else if (this.activeName == 'Available') {\r\n                this.pageOptions.total = this.Availablenum;\r\n            }\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            // this.getNumTofather();\r\n        },\r\n        // getNumTofather() {\r\n        //     this.$emit('getNum', this.ActivePOManagemenList);\r\n        // },\r\n        changePagination() {\r\n            let el2 = document.getElementsByClassName(`el-select-dropdown__item`);\r\n            for (let i = 0; i < el2.length; i++) {\r\n                el2[i].innerHTML = el2[i].innerHTML.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            }\r\n        },\r\n        async AutoReport() {\r\n            let res = await AutoReport('', 'reportType=Consume&&equipmentCode=' + this.selectTabelData.EquipmentId);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        async UpdateProduced() {\r\n            let flag = this.Updateinputlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                ExecutionId: this.selectTabelData.ExecutionId,\r\n                LotCode: '',\r\n                ProductionDate: '',\r\n                ExpirationDate: ''\r\n            };\r\n            this.Updateinputlist.forEach(item => {\r\n                if (item.id == 'LotCode') {\r\n                    params[item.id] = item.value + item.value2 + item.value3;\r\n                } else {\r\n                    params[item.id] = item.value;\r\n                }\r\n            });\r\n            params.ExpirationDate = moment(params.ExpirationDate).format('YYYY-MM-DD HH:mm:ss');\r\n            if (params.LotCode.length > 10) {\r\n                Message({\r\n                    message: `${this.$t('Overview.BatchCodeLong')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let res = await PoProducedUpdatePo(params);\r\n            this.GetProcessOrderView();\r\n            this.UpdateModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n\r\n        async UpdateOrderRemark() {\r\n            let params = {\r\n                ID: this.selectTabelData.ProductionOrderId,\r\n                Remark: this.Remark\r\n            };\r\n            let res = await UpdateOrderRemark(params);\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n            this.UpdateRemark = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        async HoldProduced() {\r\n            if (this.EndTime == null || this.EndTime == '') {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                ExecutionId: this.selectTabelData.ExecutionId,\r\n                EndTime: this.EndTime,\r\n                IsComplete: this.isComplete\r\n            };\r\n            let res = await PoProducedHold(params);\r\n            this.GetProcessOrderView();\r\n            this.HoldModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        ResumeBtn() {\r\n            this.startOrder(this.selectTabelData);\r\n        },\r\n        async StopProduced() {\r\n            if (this.EndTime == null || this.EndTime == '') {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            if (this.isComplete == true && this.selectTabelData.NeedQARelease == '1') {\r\n                let flag = this.Completelist.some(item => {\r\n                    if (item.require) {\r\n                        return item.value == '' || item.value == null;\r\n                    }\r\n                });\r\n                if (flag) {\r\n                    Message({\r\n                        message: `${this.$t('Inventory.ToOver')}`,\r\n                        type: 'warning'\r\n                    });\r\n                    return;\r\n                }\r\n            }\r\n            let params = {\r\n                ExecutionId: this.selectTabelData.ExecutionId,\r\n                EndTime: this.EndTime,\r\n                IsComplete: this.isComplete,\r\n                ProduceStatus: this.Completelist[2].value,\r\n                Reason: this.Completelist[3].value\r\n            };\r\n            let res = await PoProducedStop(params);\r\n            this.GetProcessOrderView();\r\n            this.$emit('loadProgress', this.EquipmentId);\r\n            this.stopModel = false;\r\n            if (res.msg.includes('需要取样')) {\r\n                MessageBox.alert(`${res.msg}`, '提示!', {\r\n                    confirmButtonText: '确定',\r\n                    callback: action => {}\r\n                });\r\n            } else {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            }\r\n        },\r\n        stopBtn() {\r\n            this.selectTabelData.Material = this.selectTabelData.MaterialName + '-' + this.selectTabelData.MaterialCode;\r\n            this.selectTabelData.PlanQuantity = this.selectTabelData.TargetQuantity + this.selectTabelData.Unit1;\r\n            this.selectTabelData.ActualQuantity = this.selectTabelData.ActualQty + this.selectTabelData.Unit1;\r\n            this.Completelist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            //console.log(this.Completelist[4]);\r\n            this.Completelist.forEach(item => {\r\n                for (let k in this.selectTabelData) {\r\n                    if (item.id == k) {\r\n                        item.value = this.selectTabelData[k];\r\n                    }\r\n                }\r\n            });\r\n            this.Completelist[3].options = [];\r\n            let PlanQuantity = Number(this.selectTabelData.TargetQuantity);\r\n            let ActualQuantity = Number(this.selectTabelData.ActualQty);\r\n            if (PlanQuantity > ActualQuantity) {\r\n                this.Completelist[2].value = 'NotComplete';\r\n            } else if (PlanQuantity == ActualQuantity) {\r\n                this.Completelist[2].value = 'CompleteAtOnce';\r\n            } else {\r\n                this.Completelist[2].value = 'OverComplete';\r\n            }\r\n            this.ReasonList.forEach(x => {\r\n                if (x.ItemCode == this.Completelist[2].value) {\r\n                    let res = this.Completelist[3].options.find(x1 => x1.ItemCode === this.Completelist[3].value && x1.ItemValue === x.ItemValue);\r\n                    if (res === null || res === undefined || typeof res === 'undefined') {\r\n                        x.key = x.ItemValue;\r\n                        x.label = this._i18n.locale === 'en' ? x.ItemValue : x.ItemName;\r\n                        this.Completelist[3].options.push(x);\r\n                    }\r\n                }\r\n            });\r\n            this.EndTime = new Date();\r\n            this.stopModel = true;\r\n        },\r\n        holdBtn() {\r\n            this.EndTime = new Date();\r\n            this.HoldModel = true;\r\n        },\r\n        updateBtn() {\r\n            this.Updateinputlist.forEach(item => {\r\n                item.value = '';\r\n                if (item.id == 'LotCode') {\r\n                    item.value2 = '';\r\n                    item.value3 = '';\r\n                }\r\n            });\r\n            this.Updatelist.forEach(item => {\r\n                item.value = this.selectTabelData[item.id];\r\n            });\r\n            this.Updatelist[1].value += this.selectTabelData.Unit1;\r\n            this.Updateinputlist[0].value = this.selectTabelData.StartTime;\r\n            //this.Updateinputlist[1].value2 = this.selectTabelData.BatchCode;\r\n            this.Updateinputlist[2].value = this.selectTabelData.ExpirationDate;\r\n            this.getBatchCode2();\r\n            this.UpdateModel = true;\r\n        },\r\n        updateRemarkBtn() {\r\n            this.Updatelist.forEach(item => {\r\n                item.value = this.selectTabelData[item.id];\r\n            });\r\n            this.Updatelist[1].value += this.selectTabelData.Unit1;\r\n            this.Remark = this.selectTabelData.Remark;\r\n            this.UpdateRemark = true;\r\n        },\r\n        async startNextBatchBtn() {\r\n            let params = {\r\n                ExecutionId: this.selectTabelData.ExecutionId,\r\n                Number: this.selectTabelData.Number,\r\n                EquipmentId: this.selectTabelData.EquipmentId\r\n            };\r\n            let res = await StartNextBatch(params);\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n            if (res.msg.includes('需要取样')) {\r\n                MessageBox.alert(`${res.msg}`, '提示!', {\r\n                    confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                    callback: action => {}\r\n                });\r\n            } else {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            }\r\n            // Message({\r\n            //     message: res.msg,\r\n            //     type: 'success'\r\n            // });\r\n        },\r\n        handleClick(key) {\r\n            this.QuickSearch = '';\r\n            this.timepicker = [];\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.$refs.ActiveTable.setCurrentRow(null);\r\n            this.searchlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            if (this.activeName == 'Active') {\r\n                this.pageOptions.total = this.Activenum;\r\n            } else if (this.activeName == 'Available') {\r\n                this.pageOptions.total = this.Availablenum;\r\n            } else {\r\n                this.pageOptions.total = this.Histroynum;\r\n            }\r\n        },\r\n        GetDate(id) {\r\n            if (id == 'ProductionDate') {\r\n                if (this.chooseItem.NeedQARelease == '1') {\r\n                    this.Startlist[4].value = this.addDays(this.Startlist[3].value, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);\r\n                } else {\r\n                    this.Startlist[4].value = this.addDays(this.chooseItem.PlanStartTime, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);\r\n                }\r\n            }\r\n        },\r\n        GetDate2(id) {\r\n            if (id == 'ProductionDate') {\r\n                if (this.selectTabelData.NeedQARelease == '1') {\r\n                    this.Updateinputlist[2].value = this.addDays(this.Updateinputlist[0].value, this.selectTabelData.Mhdhb, this.selectTabelData.Iprkz);\r\n                } else {\r\n                    this.Updateinputlist[2].value = this.addDays(this.selectTabelData.PlanStartTime, this.selectTabelData.Mhdhb, this.selectTabelData.Iprkz);\r\n                }\r\n            }\r\n        },\r\n        addDays(date, number, interval) {\r\n            const newDate1 = new Date(date);\r\n            const newDate = new Date(newDate1.getFullYear(), newDate1.getMonth(), newDate1.getDate());\r\n            this.DateAdd(interval, number, newDate);\r\n            newDate.setDate(newDate.getDate() + 1); // 增加一天\r\n            newDate.setSeconds(newDate.getSeconds() - 1); // 减去1秒\r\n            return newDate;\r\n        },\r\n        DateAdd(interval, number, date) {\r\n            switch (interval) {\r\n                case 'Y': {\r\n                    date.setFullYear(date.getFullYear() + number);\r\n                    return date;\r\n                }\r\n                case 'Q': {\r\n                    date.setMonth(date.getMonth() + number * 3);\r\n                    return date;\r\n                }\r\n                case 'M': {\r\n                    date.setMonth(date.getMonth() + number);\r\n                    return date;\r\n                }\r\n                case 'W': {\r\n                    date.setDate(date.getDate() + number * 7);\r\n                    return date;\r\n                }\r\n                case 'D': {\r\n                    date.setDate(date.getDate() + number);\r\n                    return date;\r\n                }\r\n                case 'h': {\r\n                    date.setHours(date.getHours() + number);\r\n                    return date;\r\n                }\r\n                case 'm': {\r\n                    date.setMinutes(date.getMinutes() + number);\r\n                    return date;\r\n                }\r\n                case 's': {\r\n                    date.setSeconds(date.getSeconds() + number);\r\n                    return date;\r\n                }\r\n                default: {\r\n                    date.setDate(date.getDate() + number);\r\n                    return date;\r\n                }\r\n            }\r\n        },\r\n        startOrder(item) {\r\n            if (item.row) {\r\n                this.IsPack = item.row.NeedQARelease;\r\n                //this.getLtext(item.row.ProductionOrderId);\r\n                this.chooseItem = item.row;\r\n                this.chooseItem.isResume = false;\r\n            } else {\r\n                this.chooseItem = item;\r\n                this.chooseItem.isResume = true;\r\n            }\r\n            this.MyGetRunOrder();\r\n            this.Startlist.forEach((item, index) => {\r\n                if (index == 0) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 3) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 2) {\r\n                    item.value = '';\r\n                    item.value2 = '';\r\n                    item.value3 = '';\r\n                } else if (item.id == 'IsUpdateLtxt') {\r\n                    item.value = false;\r\n                } else {\r\n                    item.value = '';\r\n                }\r\n            });\r\n            this.GetDate('ProductionDate');\r\n            this.getBatchList();\r\n            //this.getBatchCode();\r\n        },\r\n        async MyGetRunOrder() {\r\n            let res = await GetRunOrder('', this.EquipmentId);\r\n            this.runningCode = res.response;\r\n            if (this.runningCode == null) {\r\n                this.runningCode = '';\r\n            }\r\n        },\r\n        async ProducedStart() {\r\n            if (this.IsDifferent == true) {\r\n                this.$message.warning('工艺长文本对比不通过,不允许启动工单！');\r\n                return;\r\n            }\r\n            let flag = this.Startlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                SegmentId: this.chooseItem.SegmentId,\r\n                ProductionOrderId: this.chooseItem.ProductionOrderId,\r\n                PoSegmentRequirementId: this.chooseItem.ID,\r\n                BatchId: '',\r\n                LotCode: '',\r\n                EquipmentId: this.EquipmentId,\r\n                StartTime: '',\r\n                ProductionDate: '',\r\n                ExpirationDate: ''\r\n            };\r\n            if (this.chooseItem.isResume == true) {\r\n                params.ExecutionId = this.chooseItem.ExecutionId;\r\n            } else {\r\n                params.ExecutionId = '';\r\n            }\r\n            this.Startlist.forEach(item => {\r\n                if (item.id == 'LotCode') {\r\n                    params[item.id] = item.value + item.value2 + item.value3;\r\n                } else {\r\n                    params[item.id] = item.value;\r\n                }\r\n            });\r\n            //params.ExpirationDate = moment(params.ExpirationDate).format('YYYY-MM-DD HH:mm:ss');\r\n            // if (params.LotCode.length > 10) {\r\n            //     Message({\r\n            //         message: `${this.$t('Overview.BatchCodeLong')}`,\r\n            //         type: 'warning'\r\n            //     });\r\n            //     return;\r\n            // }\r\n            let res;\r\n            if (this.chooseItem.isResume == true) {\r\n                res = await PoProducedResume(params);\r\n            } else {\r\n                res = await PoProducedStart(params);\r\n            }\r\n            this.GetProcessOrderView();\r\n            this.$emit('loadProgress', this.EquipmentId);\r\n            this.StartModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        handleSelectionChange(val) {\r\n            if (val != null) {\r\n                this.selectTabelData = val;\r\n                this.selectTabelData.Material = this.selectTabelData.MaterialName + '-' + this.selectTabelData.MaterialCode;\r\n                this.tablechooselist = 1;\r\n            } else {\r\n                this.selectTabelData = {};\r\n                this.tablechooselist = 0;\r\n            }\r\n        },\r\n        handleSizeChange(val) {\r\n            this.pageOptions.pageSize = val;\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.pageOptions.page = val;\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.POManagement {\r\n    .searchboxtitle {\r\n        font-size: 1.7vh;\r\n        color: #767777;\r\n        padding-bottom: 5px;\r\n        margin-left: 10px;\r\n    }\r\n    .el-tabs {\r\n        height: 97%;\r\n    }\r\n    .subsubtabs {\r\n        .el-tabs--border-card {\r\n            border: 0 !important;\r\n            box-shadow: none !important;\r\n        }\r\n    }\r\n    .paginationbox {\r\n        height: 10vh;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin-top: 10px;\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 47%;\r\n            text-align: right;\r\n        }\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n    .splitdetailbox {\r\n        padding-bottom: 10px;\r\n        border: 1px solid #e8e8e8;\r\n        margin-bottom: 5px;\r\n        .splitdetailboxtitle {\r\n            background: #f5f5f5;\r\n            height: 3.5vh;\r\n            display: flex;\r\n            align-items: center;\r\n            padding-left: 5px;\r\n            font-size: 1.1rem;\r\n            color: #303133;\r\n        }\r\n        .detailsnote {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 0.9rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .detailsnote2 {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 1.2rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .detailsnote3 {\r\n            background-color: #f5fdec;\r\n            border-color: #faecd8;\r\n            color: hsl(135, 55%, 44%);\r\n            padding: 8px;\r\n            font-size: 1.2rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .splitdetailboxtitleTag {\r\n            margin-left: 5px;\r\n            background: #5cb85c;\r\n            color: #fff;\r\n            border-color: #5cb85c;\r\n        }\r\n    }\r\n}\r\n.el-dialog__body {\r\n    .el-input {\r\n        width: 250px !important;\r\n    }\r\n    .longwidthinput {\r\n        .el-input {\r\n            width: 400px !important;\r\n        }\r\n        .el-select {\r\n            width: 400px !important;\r\n        }\r\n    }\r\n    .el-select {\r\n        width: 250px !important;\r\n    }\r\n}\r\n</style>\r\n"]}]}