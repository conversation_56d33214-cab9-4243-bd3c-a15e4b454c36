{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\productionManagement\\ResourceDefinition\\components\\form-dialog-phase.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\productionManagement\\ResourceDefinition\\components\\form-dialog-phase.vue", "mtime": 1750254216363}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgc2F2ZVBoYXNlLCBnZXRTYXBTZWdtZW50TGlzdCB9IGZyb20gJ0AvYXBpL3Byb2R1Y3Rpb25NYW5hZ2VtZW50L0Zvcm11bGEnOwppbXBvcnQgeyBnZXRMYWJlbEZvcm1hdExpc3QgfSBmcm9tICJAL2FwaS9zeXN0ZW1NYW5hZ2VtZW50L2xhYmVsUHJpbnQiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ2FkZCcsCgogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkaWFsb2dGb3JtOiB7fSwKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGZvcm1Mb2FkaW5nOiBmYWxzZSwKICAgICAgc2FwRXF1aXBtZW50SWQ6IDAsCiAgICAgIFNhcFNlZ21lbnRzOiBbXSwKICAgICAgcnVsZXM6IHsKICAgICAgICBQYXJlbnRJZDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeW3peautScsCiAgICAgICAgICB0cmlnZ2VyOiAnY2hhbmdlJwogICAgICAgIH1dLAogICAgICAgIFNlZ21lbnRDb2RlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl57yW56CBJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dLAogICAgICAgIFNlZ21lbnROYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl5ZCN56ewJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKCiAgbW91bnRlZCgpIHt9LAoKICBtZXRob2RzOiB7CiAgICBzdWJtaXQoKSB7CiAgICAgIHRoaXMuZGlhbG9nRm9ybS5TYXBFcXVpcG1lbnRJZCA9IHRoaXMuc2FwRXF1aXBtZW50SWQ7CiAgICAgIHRoaXMuJHJlZnMuZGlhbG9nRm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBzYXZlUGhhc2UodGhpcy5kaWFsb2dGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2VzcyhyZXMubXNnKTsKICAgICAgICAgICAgdGhpcy4kZW1pdCgnc2F2ZUZvcm0nKTsKICAgICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKCiAgICBzaG93KGRhdGEpIHsKICAgICAgdGhpcy5kaWFsb2dGb3JtID0ge307CiAgICAgIHRoaXMuJHNldCh0aGlzLmRpYWxvZ0Zvcm0sICdJRCcsIGRhdGEuSUQpOwogICAgICB0aGlzLnNhcEVxdWlwbWVudElkID0gZGF0YS5TYXBFcXVpcG1lbnRJZDsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy4kbmV4dFRpY2soXyA9PiB7CiAgICAgICAgdGhpcy5nZXRPcGVyYXRpb25EYXRhKGRhdGEpOwoKICAgICAgICBpZiAoZGF0YS5JRCkgewogICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZGlhbG9nRm9ybSwgJ1BhcmVudElkJywgZGF0YS5QYXJlbnRJZCk7CiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5kaWFsb2dGb3JtLCAnU2VnbWVudENvZGUnLCBkYXRhLlNlZ21lbnRDb2RlKTsKICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmRpYWxvZ0Zvcm0sICdTZWdtZW50TmFtZScsIGRhdGEuU2VnbWVudE5hbWUpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAoKICAgIGdldE9wZXJhdGlvbkRhdGEoZGF0YSkgewogICAgICB0aGlzLlNhcFNlZ21lbnRzID0gW107CiAgICAgIGdldFNhcFNlZ21lbnRMaXN0KHsKICAgICAgICBMZXZlbDogMSwKICAgICAgICBTYXBFcXVpcG1lbnRJZDogdGhpcy5zYXBFcXVpcG1lbnRJZAogICAgICB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5TYXBTZWdtZW50cy5wdXNoKC4uLnJlcy5yZXNwb25zZSk7IC8vIHRoaXMuZGlhbG9nRm9ybSA9IE9iamVjdC5hc3NpZ24oe30sZGF0YSkKICAgICAgfSk7IC8vIGNvbnN0IHsgcmVzcG9uc2UgfSA9IGF3YWl0IGdldFNhcFNlZ21lbnRMaXN0KHsKICAgICAgLy8gICBMZXZlbDogMSwKICAgICAgLy8gICBTYXBFcXVpcG1lbnRJZDogdGhpcy5zYXBFcXVpcG1lbnRJZCwKICAgICAgLy8gfSkKICAgICAgLy8gdGhpcy5TYXBTZWdtZW50cyA9IHJlc3BvbnNlCiAgICB9LAoKICAgIHNlbGVjdENoYW5nZSgpIHsKICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsKICAgIH0KCiAgfQp9Ow=="}, {"version": 3, "mappings": "AA2BA;AACA;AACA;EACAA,WADA;;EAEAC;IACA;MACAC,cADA;MAEAC,oBAFA;MAGAC,kBAHA;MAIAC,iBAJA;MAKAC,eALA;MAMAC;QACAC,WACA;UAAAC;UAAAC;UAAAC;QAAA,CADA,CADA;QAIAC,cACA;UAAAH;UAAAC;UAAAC;QAAA,CADA,CAJA;QAOAE,cACA;UAAAJ;UAAAC;UAAAC;QAAA,CADA;MAPA;IANA;EAkBA,CArBA;;EAsBAG,WACA,CAvBA;;EAwBAC;IACAC;MACA;MACA;QACA;UACAC;YACA;YACA;YACA;UACA,CAJA;QAKA;MACA,CARA;IASA,CAZA;;IAaAC;MACA;MACA;MACA;MACA;MACA;QACA;;QACA;UAEA;UACA;UACA;QACA;MACA,CARA;IASA,CA3BA;;IA4BAC;MACA;MACAC;QAAAC;QAAAC;MAAA;QACA,uCADA,CAEA;MACA,CAHA,EAFA,CAMA;MACA;MACA;MACA;MACA;IACA,CAvCA;;IAwCAC;MACA;IACA;;EA1CA;AAxBA", "names": ["name", "data", "dialogForm", "dialogVisible", "formLoading", "sapEquipmentId", "SapSegments", "rules", "ParentId", "required", "message", "trigger", "SegmentCode", "SegmentName", "mounted", "methods", "submit", "savePhase", "show", "getOperationData", "getSapSegmentList", "Level", "SapEquipmentId", "selectChange"], "sourceRoot": "src/views/productionManagement/ResourceDefinition/components", "sources": ["form-dialog-phase.vue"], "sourcesContent": ["<template>\r\n  <el-dialog :title=\"dialogForm.ID ? '编辑' : '新增'\" :visible.sync=\"dialogVisible\" width=\"600px\"\r\n             :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\r\n             @close=\"dialogVisible = false\">\r\n    <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"80px\">\r\n      <el-form-item label=\"工段\" prop=\"ParentId\">\r\n        <el-select transfer=\"true\" :popper-append-to-body=\"false\" style=\"width: 100%\" v-model=\"dialogForm.ParentId\" @change=\"selectChange\" placeholder=\"请选择\">\r\n          <el-option v-for=\"(item) in SapSegments\" :key=\"item.id\" :label=\"item.SegmentName\" :value=\"item.ID\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"编码\" prop=\"SegmentCode\">\r\n        <el-input v-model=\"dialogForm.SegmentCode\" :maxlength=\"20\" placeholder=\"\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"名称\" prop=\"SegmentName\">\r\n        <el-input v-model=\"dialogForm.SegmentName\" :maxlength=\"20\" placeholder=\"\" />\r\n      </el-form-item>\r\n    </el-form>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\r\n      <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\"\r\n                 @click=\"submit()\">确定\r\n      </el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n<script>\r\nimport { savePhase, getSapSegmentList } from '@/api/productionManagement/Formula';\r\nimport {getLabelFormatList} from \"@/api/systemManagement/labelPrint\";\r\nexport default {\r\n  name: 'add',\r\n  data() {\r\n    return {\r\n      dialogForm: {},\r\n      dialogVisible: false,\r\n      formLoading: false,\r\n      sapEquipmentId: 0,\r\n      SapSegments: [],\r\n      rules: {\r\n        ParentId: [\r\n          { required: true, message: '请选择工段', trigger: 'change' }\r\n        ],\r\n        SegmentCode: [\r\n          { required: true, message: '请输入编码', trigger: 'blur' },\r\n        ],\r\n        SegmentName: [\r\n          { required: true, message: '请输入名称', trigger: 'blur' },\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.dialogForm.SapEquipmentId = this.sapEquipmentId\r\n      this.$refs.dialogForm.validate((valid) => {\r\n        if (valid) {\r\n          savePhase(this.dialogForm).then(res => {\r\n            this.$message.success(res.msg)\r\n            this.$emit('saveForm')\r\n            this.dialogVisible = false\r\n          })\r\n        }\r\n      });\r\n    },\r\n    show(data) {\r\n      this.dialogForm = {}\r\n      this.$set(this.dialogForm, 'ID', data.ID)\r\n      this.sapEquipmentId = data.SapEquipmentId\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.getOperationData(data)\r\n        if(data.ID){\r\n         \r\n          this.$set(this.dialogForm, 'ParentId', data.ParentId)\r\n          this.$set(this.dialogForm, 'SegmentCode', data.SegmentCode)\r\n          this.$set(this.dialogForm, 'SegmentName', data.SegmentName)\r\n        }\r\n      })\r\n    },\r\n    getOperationData(data) {\r\n      this.SapSegments = []\r\n      getSapSegmentList({Level: 1,SapEquipmentId: this.sapEquipmentId}).then(res => {\r\n        this.SapSegments.push(...res.response)\r\n        // this.dialogForm = Object.assign({},data)\r\n      })\r\n      // const { response } = await getSapSegmentList({\r\n      //   Level: 1,\r\n      //   SapEquipmentId: this.sapEquipmentId,\r\n      // })\r\n      // this.SapSegments = response\r\n    },\r\n    selectChange(){\r\n      this.$forceUpdate()\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}