﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;
using SEFA.Base.Model.BASE;

namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///文档/目录权限控制表
    ///</summary>
    [SugarTable("DFM_B_SOP_PERMISSION")]
    public class SopPermissionEntity : EntityBase
    {
        public SopPermissionEntity()
        {
        }

        /// <summary>
        /// Desc:授权对象ID(目录ID/文档ID)
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TARGET_ID")]
        public string TargetId { get; set; }

        /// <summary>
        /// Desc:对象类型(1-目录 2-文档)
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TARGET_TYPE")]
        public byte TargetType { get; set; }

        /// <summary>
        /// Desc:授权类型(1-角色 2-部门 3-用户)
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "GRANT_TYPE")]
        public byte GrantType { get; set; }

        /// <summary>
        /// Desc:被授权对象ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "GRANT_ID")]
        public string GrantId { get; set; }

        /// <summary>
        /// Desc:角色名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string RoleName { get; set; }

        /// <summary>
        /// Desc:权限级别(1-预览 2-下载 3-检索 4-上传 8-删除)
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PERM_LEVEL")]
        public string PermLevel { get; set; }

        /// <summary>
        /// Desc:删除标记(0-未删 1-已删)
        /// Default:0
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int? Deleted { get; set; }
    }
}