<template>
  <div>
    <el-drawer class="drawer" :visible.sync="drawer" :direction="'rtl'" :before-close="handleClose" size="50%"
      :with-header="!showSales">
      <div slot="title" class="title-box">
        <span>{{ `${currentRow.Name} | ${currentRow.Code}` }}</span>
      </div>
      <section v-if="!showSales">
        <el-tabs v-model="tabIndex" type="border-card" @tab-click="changeTabs">
          <el-tab-pane label="工段" name="1"></el-tab-pane>
          <el-tab-pane label="工序" name="2"></el-tab-pane>
          <el-tab-pane label="工序与设备" name="3"></el-tab-pane>
        </el-tabs>
        <div class="InventorySearchBox mt5">
          <div class="searchbox pd5">
            <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
              <el-form-item :label="$t('GLOBAL._SSL')">
                <el-input clearable v-model="searchForm.Key"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" @click="getSearchBtn">{{ $t('GLOBAL._CX') }}</el-button>
              </el-form-item>
              <el-form-item>
                <el-button size="small" type="success" icon="el-icon-circle-plus-outline" @click="showDialog({})">{{
                  $t('GLOBAL._XZ') }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="table-box">
          <el-table v-loading="loading" :data="tableData" element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading" style="width: 100%" height="83vh">
            <el-table-column v-for="(item, index) in tableName" :key="index" :prop="item.field" :label="item.label">
              <template slot-scope="scope">
                <div v-if="item.field === 'SegmentName'" class="combination">
                  <i v-if="tabIndex === '2'" class="el-icon-document" @click="showSalesView(scope.row)"></i>
                  <span>
                   {{ scope.row.SegmentName }}
                  </span>
                </div>
                <span v-else> {{ scope.row[item.field] }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._BJ') }}</el-button>
                <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._SC') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </section>
      <section v-else>
        <SalesContainer ref="sales" @show-sales="showSales = false" :data="salesData" @show-dialog="showSalesDialog" />
      </section>
    </el-drawer>
    <FormDialogOperation @saveForm="refreshTableData" ref="operation" />
    <FromDialogPhase @saveForm="refreshTableData" ref="phase" />
    <FromDialogPhaseMachine @saveForm="refreshTableData" ref="phaseMachine" />
    <ForemDialogSales ref="salesDialog" />
  </div>
</template>

<script>
import {getSapSegmentList, getSapSegment, getSapSegmentEquipmentList, deleteOperation, deleteSapSegmentEquipment } from '@/api/productionManagement/Formula';
import FormDialogOperation from './form-dialog-operation.vue'
import FromDialogPhase from './form-dialog-phase.vue';
import FromDialogPhaseMachine from './form-dialog-phaseMachine'
import SalesContainer from './salesContainer.vue'
import ForemDialogSales from './form-dialog-sales.vue'
export default {
  name: 'drawer',
  components: {
    FormDialogOperation,
    FromDialogPhase,
    FromDialogPhaseMachine,
    SalesContainer,
    ForemDialogSales
  },
  data() {
    return {
      searchForm: {},
      tableData: [],
      tableName: [],
      loading: false,
      drawer: false,
      tabIndex: '1',
      sapEquipmentId: 0,
      hansObjOperation: this.$t('Formula.Resouce_Operation'),
      hansObjPhase: this.$t('Formula.Resouce_Phase'),
      hansObjPhaseMachine: this.$t('Formula.Resouce_Phase_Machine'),
      parentId: 0,
      currentRow: {},
      showSales: false,
      salesData: {}
    }
  },
  methods: {
    showSalesView(row) {
      this.salesData = row
      this.showSales = true
      this.$nextTick(_ => {
        this.$refs.sales.getTableData()
      })
    },
    show(data) {
      this.tabIndex = '1'
      this.sapEquipmentId = data.ID
      this.currentRow = data
      this.getOperationData()
      this.initTableHead(this.hansObjOperation)
      this.drawer = true
    },
    handleClose() {
      this.drawer = false
    },
    changeTabs() {
      switch (this.tabIndex) {
        case '1':
          this.initTableHead(this.hansObjOperation)
          this.getOperationData()
          break;
        case '2':
          this.initTableHead(this.hansObjPhase)
          this.getPhaseData()
          break;
        case '3':
          this.initTableHead(this.hansObjPhaseMachine)
          this.getPhaseMechineData()
          break;
        default:
          break;
      }
    },
    showDialog(val) {
      switch (this.tabIndex) {
        case '1':
          val.SapEquipmentId = this.sapEquipmentId
          this.$refs.operation.show(val)
          break;
        case '2':
          val.SapEquipmentId = this.sapEquipmentId
          this.$refs.phase.show(val)
          break;
        case '3':
          val.SapEquipmentId = this.sapEquipmentId
          this.$refs.phaseMachine.show(val)
          break;
        default:
          break;
      }
    },
    async delRow({ ID }) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(() => {
        switch (this.tabIndex) {
          case '1':
            deleteOperation([ID]).then(({ msg }) => {
              this.$message.success(msg)
              this.getOperationData()
            })
            break;
          case '2':
            deleteOperation([ID]).then(({ msg }) => {
              this.$message.success(msg)
              this.getPhaseData()
            })
            break;
          case '3':
            deleteSapSegmentEquipment([ID]).then(({ msg }) => {
              this.$message.success(msg)
              this.getPhaseMechineData()
            })
            break;
          default:
            break;
        }
      }).catch(err => {
        console.log(err);
      });

    },
    // emit刷新数据
    refreshTableData() {
      switch (this.tabIndex) {
        case '1':
          this.getOperationData()
          break;
        case '2':
          this.getPhaseData()
          break;
        case '3':
          this.getPhaseMechineData()
          break;
        default:
          break;
      }
    },
    // 获取数据
    async getOperationData() {
      this.loading = true
      const { response } = await getSapSegmentList({
        Level: 1,
        SapEquipmentId: this.sapEquipmentId,
        ...this.searchForm
      })
      this.loading = false
      this.tableData = response
    },
    async getPhaseData() {
      this.loading = true
      const { response } = await getSapSegmentList({
        Level: 2,
        SapEquipmentId: this.sapEquipmentId,
        ...this.searchForm
      })
      this.loading = false
      this.tableData = response
    },
    async getPhaseMechineData() {
      this.loading = true
      const { response } = await getSapSegmentEquipmentList({
        SapEquipmentId: this.sapEquipmentId,
        ...this.searchForm
      })
      this.loading = false
      this.tableData = response
    },
    // 初始化表头
    initTableHead(obj) {
      this.tableName = []
      for (let key in obj) {
        this.tableName.push({ field: key, label: obj[key] })
      }
    },
    // 搜索
    getSearchBtn() {
      this.refreshTableData()
    },
    showSalesDialog(row) {
      // console.log(row);
      this.$refs.salesDialog.show(row)
    },
  }
}
</script>

<style lang="scss" scoped>
.drawer {
  :deep(.el-drawer__body) {
    background-color: #FFFFFF;
    overflow-y: hidden
  }

  :deep(.el-form--inline) {
    height: 32px;
  }

  .title-box {
    font-size: 18px;
    color: #909399;
  }

  .mt5 {
    margin-top: 5px;
  }

  .pd5 {
    padding: 5px;
  }

  .table-box {
    padding: 0 10px;

    .combination {
      display: flex;
      align-items: center;
    }

    i {
      margin-right: 5px;
      font-size: 15px !important;
      color: #67c23a;
    }
  }
}
</style>
