{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDoc\\form-dialog.vue?vue&type=style&index=0&id=36a0b27b&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDoc\\form-dialog.vue", "mtime": 1750226125043}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["form-dialog.vue"], "names": [], "mappings": ";AA6TA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "form-dialog.vue", "sourceRoot": "src/views/SOP/sopDoc", "sourcesContent": ["<template>\n  <div class=\"sop-doc-form\">\n    <el-dialog \n      :title=\"dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')\" \n      :visible.sync=\"dialogVisible\" \n      width=\"700px\"\n      :close-on-click-modal=\"false\" \n      :modal-append-to-body=\"false\" \n      :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" :rules=\"rules\" label-width=\"100px\">\n        <div class=\"form-body\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"12\" v-if=\"opertype === 2\">\n              <el-form-item label=\"主键\">{{dialogForm.id}}</el-form-item>\n            </el-col>\n\n            <el-col :span=\"24\">\n              <el-form-item label=\"所属目录\" prop=\"dirId\">\n                <tree-select\n                  v-model=\"dialogForm.dirId\"\n                  :data=\"treeData\"\n                  :props=\"{\n                    children: 'children',\n                    label: 'name',\n                    value: 'id'\n                  }\"\n                  @change=\"handleDirChange\"\n                  placeholder=\"请选择所属目录\">\n                </tree-select>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"文档编码\" prop=\"docCode\">\n                <el-input v-model=\"dialogForm.docCode\" placeholder=\"请输入文档编码\"></el-input>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"文档版本\" prop=\"docVersion\">\n                <el-input v-model=\"dialogForm.docVersion\" placeholder=\"请输入文档版本\"></el-input>\n              </el-form-item>\n            </el-col>\n\n            <!-- <el-col :span=\"12\">\n              <el-form-item label=\"是否有效\" prop=\"docStatus\">\n                <el-radio-group v-model=\"dialogForm.docStatus\">\n                  <el-radio :label=\"1\">有效</el-radio>\n                  <el-radio :label=\"0\">无效</el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </el-col> -->\n\n            <el-col :span=\"24\">\n              <el-form-item label=\"文件上传\" prop=\"docList\">\n                <div class=\"upload-box\">\n                  <el-upload\n                    class=\"upload-demo\"\n                    :action=\"uploadUrl\"\n                    :on-success=\"handleUploadSuccess\"\n                    :on-error=\"handleUploadError\"\n                    :before-upload=\"beforeUpload\"\n                    :file-list=\"fileList\"\n                    :limit=\"1\">\n                    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload2\">点击上传</el-button>\n                    <div slot=\"tip\" class=\"el-upload__tip\">只能上传pdf/doc/docx文件</div>\n                  </el-upload>\n                </div>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"24\" v-if=\"fileList.length\">\n              <el-form-item label=\"文件信息\">\n                <el-table :data=\"dialogForm.docList\" border>\n                  <el-table-column prop=\"docName\" label=\"文件名\" width=\"180\"></el-table-column>\n                  <el-table-column prop=\"docCode\" label=\"编码\" width=\"120\">\n                    <template slot-scope=\"scope\">\n                      <el-input v-model=\"scope.row.docCode\" size=\"small\"></el-input>\n                    </template>\n                  </el-table-column>\n                  <el-table-column prop=\"docVersion\" label=\"版本\" width=\"100\">\n                    <template slot-scope=\"scope\">\n                      <el-input v-model=\"scope.row.docVersion\" size=\"small\"></el-input>\n                    </template>\n                  </el-table-column>\n                  <el-table-column prop=\"docStatus\" label=\"状态\" width=\"100\">\n                    <template slot-scope=\"scope\">\n                      <el-select v-model=\"scope.row.docStatus\" size=\"small\">\n                        <el-option label=\"有效\" :value=\"1\"></el-option>\n                        <el-option label=\"无效\" :value=\"0\"></el-option>\n                      </el-select>\n                    </template>\n                  </el-table-column>\n                  <el-table-column label=\"操作\" width=\"80\">\n                    <template slot-scope=\"scope\">\n                      <el-button type=\"text\" size=\"small\" @click=\"removeFile(scope.$index)\">删除</el-button>\n                    </template>\n                  </el-table-column>\n                </el-table>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <div class=\"btn-group\">\n          <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n          <el-button \n            type=\"primary\" \n            size=\"small\" \n            v-loading=\"formLoading\" \n            :disabled=\"formLoading || !dialogForm.docList.length\"\n            @click=\"submitForm\">\n            确 定\n          </el-button>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getSopDocDetail,\n  saveSopDocForm,\n  batchAddSopDoc\n} from \"@/api/SOP/sopDoc\";\nimport { getSopDirTree } from \"@/api/SOP/sopDir\";\nimport { configUrl } from '@/config'\nimport TreeSelect from '../components/tree-select'\n\nexport default {\n  name: 'FormDialog',\n  components: {\n    TreeSelect\n  },\n  props: {\n    treeData: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      dialogForm: {\n        id: undefined,\n        dirId: '',\n        docCode: '',\n        docVersion: '',\n        docStatus: 1, // 默认有效\n        deleted: 0,\n        docList: [] // 存储多个文档记录\n      },\n      rules: {\n        dirId: [\n          { required: true, message: '请选择所属目录', trigger: 'change' }\n        ],\n        docCode: [\n          { required: true, message: '请输入文档编码', trigger: 'blur' }\n        ],\n        docVersion: [\n          { required: true, message: '请输入文档版本', trigger: 'blur' }\n        ],\n        docList: [\n          { required: true, message: '请上传文档文件', trigger: 'change' }\n        ]\n      },\n      dialogVisible: false,\n      formLoading: false,\n      opertype: 1, // 1-新增 2-编辑\n      dirIdOptions: [], // 目录树选项\n      docStatusOptions: [], // 状态选项\n      deletedOptions: [], // 是否生效选项\n      fileList: [],\n      uploadUrl: `${configUrl[process.env.VUE_APP_SERVE].baseURL_DFM}/api/SopDoc/Upload`\n    }\n  },\n  mounted() {\n    this.getDictData()\n  },\n  methods: {\n    async getDictData() {\n      try {\n        this.docStatusOptions = await this.$getNewDataDictionary('docStatus')\n      } catch (err) {\n        console.error('获取字典数据失败:', err)\n        this.$message.error('获取字典数据失败')\n      }\n    },\n    async submitForm() {\n      try {\n        await this.$refs.dialogForm.validate()\n        this.formLoading = true\n\n        // 使用批量保存接口\n        const res = await batchAddSopDoc(this.dialogForm.docList)\n        if (res.success) {\n          this.$message.success(res.msg || '保存成功')\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        } else {\n          this.$message.error(res.msg || '保存失败')\n        }\n      } catch (err) {\n        if (err === false) return // 表单验证失败\n        console.error('保存失败:', err)\n        this.$message.error('保存失败')\n      } finally {\n        this.formLoading = false\n      }\n    },\n    show(data) {\n      this.opertype = data.ID ? 2 : 1\n      this.dialogForm = {\n        id: undefined,\n        dirId: '',\n        docCode: '',\n        docVersion: '',\n        docStatus: 1,\n        deleted: 0,\n        docList: []\n      }\n      this.fileList = []\n      this.dialogVisible = true\n      this.$nextTick(async () => {\n        if (data.ID) {\n          await this.getDialogDetail(data.ID)\n        }\n      })\n    },\n    async getDialogDetail(id) {\n      try {\n        const res = await getSopDocDetail(id)\n        if (res.success) {\n          this.dialogForm = res.response || {}\n          if (this.dialogForm.fileUuid) {\n            this.fileList = [{\n              name: this.dialogForm.docName,\n              url: this.dialogForm.filePath\n            }]\n          }\n        } else {\n          this.$message.error(res.msg || '获取详情失败')\n        }\n      } catch (err) {\n        console.error('获取详情失败:', err)\n        this.$message.error('获取详情失败')\n      }\n    },\n    handleUploadSuccess(res, file) {\n      if (res.success) {\n        // 清空之前的文档记录\n        this.dialogForm.docList = []\n        // 创建新的文档记录\n        const docRecord = {\n          dirId: this.dialogForm.dirId,\n          docName: file.name,\n          docCode: this.dialogForm.docCode || '', // 使用表单默认值或空字符串\n          docVersion: this.dialogForm.docVersion || '',\n          fileUuid: res.response,\n          filePath: this.dialogForm.filePath,\n          fileSize: file.size,\n          docStatus: this.dialogForm.docStatus || 1,\n          deleted: 0\n        }\n        this.dialogForm.docList.push(docRecord)\n        this.$message.success('上传成功')\n      } else {\n        this.$message.error(res.msg || '上传失败')\n      }\n    },\n\n    // 删除文件\n    removeFile(index) {\n      this.dialogForm.docList.splice(index, 1)\n      this.fileList = []\n    },\n    getFullPath(nodeId) {\n      const path = [];\n      const findPath = (data, targetId) => {\n        for (let node of data) {\n          if (node.id === targetId) {\n            path.unshift(node.name);\n            return true;\n          }\n          if (node.children && findPath(node.children, targetId)) {\n            path.unshift(node.name);\n            return true;\n          }\n        }\n        return false;\n      };\n      findPath(this.treeData, nodeId);\n      return path.join('/');\n    },\n    handleDirChange(node) {\n      // 当目录选择改变时，更新路径\n      this.dialogForm.filePath = this.getFullPath(node.id);\n    },\n    handleUploadError(err) {\n      console.error('文件上传失败:', err)\n      this.$message.error('文件上传失败')\n    },\n    beforeUpload(file) {\n      const validTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']\n      if (!validTypes.includes(file.type)) {\n        this.$message.error('只能上传PDF/DOC/DOCX格式文件!')\n        return false\n      }\n      return true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.sop-doc-form {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n\n    .el-dialog__header {\n      padding: 15px 20px;\n      border-bottom: 1px solid #ebeef5;\n      margin: 0;\n    }\n    \n    .el-dialog__body {\n      padding: 20px;\n    }\n\n    .el-dialog__footer {\n      padding: 15px 20px;\n      border-top: 1px solid #ebeef5;\n      background-color: #f9fafb;\n\n      .btn-group {\n        display: flex;\n        justify-content: flex-end;\n        gap: 12px;\n      }\n    }\n  }\n\n  .form-body {\n    padding: 15px 0;\n\n    .el-form-item {\n      margin-bottom: 20px;\n      \n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      .el-form-item__content {\n        line-height: 32px;\n      }\n    }\n\n    .el-radio-group {\n      display: flex;\n      align-items: center;\n      gap: 20px;\n\n      .el-radio {\n        margin-right: 0;\n        \n        :deep(.el-radio__label) {\n          padding-left: 8px;\n        }\n      }\n    }\n\n    .upload-box {\n      width: 100%;\n      padding: 20px;\n      border: 1px dashed #dcdfe6;\n      border-radius: 4px;\n      background-color: #fafafa;\n      text-align: center;\n      \n      :deep(.el-upload) {\n        .el-button {\n          padding: 8px 15px;\n          margin-bottom: 10px;\n          font-size: 13px;\n          \n          i {\n            margin-right: 4px;\n          }\n        }\n\n        .el-upload__tip {\n          color: #909399;\n          font-size: 12px;\n          line-height: 1.4;\n        }\n      }\n\n      :deep(.el-upload-list) {\n        text-align: left;\n        padding: 0 10px;\n        margin-top: 10px;\n\n        .el-upload-list__item {\n          transition: all 0.3s;\n          \n          &:hover {\n            background-color: #f5f7fa;\n          }\n        }\n      }\n    }\n  }\n\n  // 输入框统一样式\n  :deep(.el-input),\n  :deep(.el-select),\n  :deep(.el-tree-select) {\n    width: 100%;\n    .el-input__inner {\n      line-height: 32px;\n      height: 32px;\n    }\n  }\n}\n</style>\n"]}]}