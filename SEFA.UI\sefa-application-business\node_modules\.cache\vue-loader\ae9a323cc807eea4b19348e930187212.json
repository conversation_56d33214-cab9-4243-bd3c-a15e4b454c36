{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopAudit\\index.vue?vue&type=style&index=0&id=ae45db02&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopAudit\\index.vue", "mtime": 1750296894458}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5lbC1mb3JtLWl0ZW0tLXNtYWxsLmVsLWZvcm0taXRlbSB7CiAgbWFyZ2luLWJvdHRvbTogMHB4Owp9Cgouc2VhcmNoLWZvcm0taXRlbSB7CiAgLmVsLWZvcm0taXRlbV9fY29udGVudCB7CiAgICB3aWR0aDogMTgwcHg7IC8vIOe7n+S4gOWuveW6puS4ujE4MHB4CiAgfQp9Cgoucm9vdC1oZWFkIHsKICAuc2VhcmNoLWZvcm0gewogICAgOmRlZXAoLmVsLWZvcm0pIHsKICAgICAgLmVsLWZvcm0taXRlbS0tc21hbGwuZWwtZm9ybS1pdGVtIHsKICAgICAgICBtYXJnaW4tYm90dG9tOiAwOwogICAgICB9CiAgICB9CgogICAgLmZvcm0tY29udGVudCB7CiAgICAgIHBhZGRpbmc6IDRweDsKCiAgICAgIC5zZWFyY2gtYXJlYSB7CiAgICAgICAgLnNlYXJjaC1yb3cgewogICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgICBnYXA6IDRweDsKCiAgICAgICAgICAuZWwtZm9ybS1pdGVtIHsKICAgICAgICAgICAgbWFyZ2luOiAwOwogICAgICAgICAgICBmbGV4OiBub25lOwoKICAgICAgICAgICAgLmVsLWZvcm0taXRlbV9fbGFiZWwgewogICAgICAgICAgICAgIHBhZGRpbmctcmlnaHQ6IDRweDsKICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMjZweDsKICAgICAgICAgICAgICBmb250LXNpemU6IDEycHg7CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC5lbC1mb3JtLWl0ZW1fX2NvbnRlbnQgewogICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAyNnB4OwoKICAgICAgICAgICAgICAuZWwtaW5wdXQsCiAgICAgICAgICAgICAgLmVsLXNlbGVjdCB7CiAgICAgICAgICAgICAgICA6ZGVlcCguZWwtaW5wdXRfX2lubmVyKSB7CiAgICAgICAgICAgICAgICAgIGhlaWdodDogMjZweDsKICAgICAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDI2cHg7CiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IDAgOHB4OwogICAgICAgICAgICAgICAgICBmb250LXNpemU6IDEycHg7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CgogICAgICAgICAgLmFjdGlvbi1idXR0b25zIHsKICAgICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgICAgZ2FwOiA0cHg7CiAgICAgICAgICAgIG1hcmdpbi1sZWZ0OiA0cHg7CgogICAgICAgICAgICAuZWwtYnV0dG9uIHsKICAgICAgICAgICAgICBoZWlnaHQ6IDI2cHg7CiAgICAgICAgICAgICAgcGFkZGluZzogMCAxMHB4OwogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsKCiAgICAgICAgICAgICAgW2NsYXNzXj0iZWwtaWNvbi0iXSB7CiAgICAgICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDNweDsKICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn0KCi5tdC04cCB7CiAgbWFyZ2luLXRvcDogOHB4Owp9CgoucGQtbGVmdCB7CiAgcGFkZGluZy1sZWZ0OiA1cHgKfQoKLm9wZXJhdGlvbi1idXR0b25zIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZ2FwOiAycHg7CgogIC5lbC1idXR0b24tLW1pbmkgewogICAgcGFkZGluZzogMnB4OwogICAgZm9udC1zaXplOiAxMXB4OwogICAgYm9yZGVyLXJhZGl1czogM3B4OwogICAgbWluLXdpZHRoOiBhdXRvOwogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA2WA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopAudit", "sourcesContent": ["<template>\n  <div class=\"root\">\n    <div class=\"root-head\">\n      <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n        <div class=\"form-content\">\n          <div class=\"search-area\">\n            <div class=\"search-row\">\n              <el-form-item :label=\"$t('SOP.DocName')\" prop=\"docName\" label-width=\"40px\" class=\"search-form-item\">\n                <el-input v-model=\"searchForm.docName\" :placeholder=\"$t('SOP.EnterDocName')\" clearable size=\"small\"></el-input>\n              </el-form-item>\n\n              <el-form-item :label=\"$t('SOP.DocCode')\" prop=\"docCode\" label-width=\"40px\" class=\"search-form-item\">\n                <el-input v-model=\"searchForm.docCode\" :placeholder=\"$t('SOP.EnterDocCode')\" clearable size=\"small\"></el-input>\n              </el-form-item>\n\n              <el-form-item :label=\"$t('SOP.UploadUser')\" prop=\"uploadUser\" label-width=\"40px\" class=\"search-form-item\">\n                <el-input v-model=\"searchForm.uploadUser\" :placeholder=\"$t('SOP.EnterUploadUser')\" clearable size=\"small\"></el-input>\n              </el-form-item>\n\n              <div class=\"action-buttons\">\n                <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn\" size=\"small\">{{ $t('GLOBAL._CX') }}</el-button>\n                <el-button icon=\"el-icon-refresh\" @click=\"resetForm\" size=\"small\">{{ $t('GLOBAL._CZ') }}</el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </el-form>\n    </div>\n    <div class=\"root-main\">\n      <el-table class=\"mt-3\"\n                :height=\"mainH\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n        <el-table-column\n          type=\"index\"\n          label=\"序号\"\n          width=\"50\"\n          align=\"center\">\n        </el-table-column>\n        <el-table-column v-for=\"(item) in tableName\"\n                         :default-sort=\"{prop: 'date', order: 'descending'}\"\n                         :key=\"item.value\"\n                         :prop=\"item.value\"\n                         :label=\"typeof item.text === 'function' ? item.text() : item.text\"\n                         :width=\"item.width\"\n                         :align=\"item.alignType || 'center'\"\n                         sortable\n                         show-overflow-tooltip\n        >\n          <template slot-scope=\"scope\">\n            <template v-if=\"item.value === 'FileSize'\">\n              {{ formatFileSize(getDocFieldValue(scope.row, item.value)) }}\n            </template>\n            <template v-else-if=\"item.value === 'DocStatus'\">\n              <el-tag :type=\"getStatusType(getDocFieldValue(scope.row, item.value))\" size=\"small\">\n                {{ formatStatus(getDocFieldValue(scope.row, item.value)) }}\n              </el-tag>\n            </template>\n            <template v-else-if=\"item.value === 'OperationType'\">\n              {{ formatOperationType(scope.row[item.value]) }}\n            </template>\n            <template v-else-if=\"item.value === 'AuditResult'\">\n              <el-tag :type=\"getAuditResultType(scope.row[item.value])\" size=\"small\">\n                {{ formatAuditResult(scope.row[item.value]) }}\n              </el-tag>\n            </template>\n            <template v-else-if=\"['DocName', 'DocCode', 'DocVersion', 'FilePath'].includes(item.value)\">\n              {{ getDocFieldValue(scope.row, item.value) }}\n            </template>\n            <template v-else>\n              {{ scope.row[item.value] }}\n            </template>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"operation\" width=\"180\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <div class=\"operation-buttons\">\n              <el-button size=\"mini\" type=\"primary\" @click=\"previewDoc(scope.row)\">{{ $t('SOP.Preview') }}</el-button>\n              <el-button size=\"mini\" type=\"success\" @click=\"approveAudit(scope.row)\"\n                         :disabled=\"scope.row.AuditResult !== null && scope.row.AuditResult !== 0\">{{ $t('SOP.Approve') }}</el-button>\n              <el-button size=\"mini\" type=\"danger\" @click=\"rejectAudit(scope.row)\"\n                         :disabled=\"scope.row.AuditResult !== null && scope.row.AuditResult !== 0\">{{ $t('SOP.Reject') }}</el-button>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    <div class=\"root-footer\">\n      <el-pagination\n          class=\"mt-3\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"searchForm.pageIndex\"\n          :page-sizes=\"[10,20, 50, 100,500]\"\n          :page-size=\"searchForm.pageSize\"\n          layout=\"->,total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          background\n      ></el-pagination>\n    </div>\n    <!-- 审核不通过原因对话框 -->\n    <el-dialog :title=\"$t('SOP.RejectReason')\" :visible.sync=\"rejectDialogVisible\" width=\"500px\"\n               :close-on-click-modal=\"false\" :close-on-press-escape=\"false\">\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" label-width=\"120px\">\n        <el-form-item :label=\"$t('SOP.RejectReason')\" prop=\"auditComment\"\n                      :rules=\"[{ required: true, message: $t('SOP.RejectReasonRequired'), trigger: 'blur' }]\">\n          <el-input type=\"textarea\" v-model=\"rejectForm.auditComment\"\n                    :placeholder=\"$t('SOP.EnterRejectReason')\" :rows=\"4\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rejectDialogVisible = false\">{{ $t('GLOBAL._QX') }}</el-button>\n        <el-button type=\"primary\" @click=\"confirmReject\">{{ $t('GLOBAL._QD') }}</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\nimport {\n    delSopAudit, getSopAuditList, saveSopAuditForm\n} from \"@/api/SOP/sopAudit\";\n\nimport { sopAuditColumn } from '@/api/SOP/sopAudit.js';\n\n\nexport default {\n  name: 'index.vue',\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n        docName: '',\n        docCode: '',\n        uploadUser: ''\n      },\n      total: 0,\n      tableData: [],\n      tableName: sopAuditColumn,\n      loading: false,\n      mainH: 0,\n      buttonOption:{\n        name:'SOP审核',\n        serveIp:'baseURL_DFM',\n        uploadUrl:'/api/SopAudit/ImportData', //导入\n        exportUrl:'/api/SopAudit/ExportData', //导出\n        DownLoadUrl:'/api/SopAudit/DownLoadTemplate', //下载模板\n      },\n      // 审核不通过原因对话框\n      rejectDialogVisible: false,\n      rejectForm: {\n        auditComment: ''\n      },\n      currentAuditRow: null\n    }\n  },\n  mounted() {\n    this.getTableData()\n    this.$nextTick(() => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    })\n    window.onresize = () => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    }\n  },\n  methods: {\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    resetForm() {\n      this.searchForm = {\n        pageIndex: 1,\n        pageSize: 20,\n        docName: '',\n        docCode: '',\n        uploadUser: ''\n      }\n      this.getTableData()\n    },\n\n    delRow(row) {\n      this.$confirms({\n        title: this.$t('GLOBAL._TS'),\n        message: this.$t('GLOBAL._COMFIRM'),\n        confirmText: this.$t('GLOBAL._QD'),\n        cancelText: this.$t('GLOBAL._QX')\n      }).then(async () => {\n        delSopAudit([row.ID]).then(res => {\n          this.$message.success(res.msg)\n          this.getTableData()\n        })\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n\n    getTableData() {\n      getSopAuditList(this.searchForm).then(res => {\n        this.tableData = res.response.data\n        this.total = res.response.dataCount\n      })\n    },\n\n    // 获取Doc字段中的文档信息\n    getDocFieldValue(row, fieldName) {\n      // 如果是文档相关字段，从Doc对象中获取\n      if (['DocName', 'DocCode', 'DocVersion', 'FilePath', 'FileSize', 'DocStatus'].includes(fieldName)) {\n        return row.Doc ? row.Doc[fieldName] : ''\n      }\n      // 其他字段直接从row中获取\n      return row[fieldName]\n    },\n\n    // 预览文档\n    previewDoc(row) {\n      // 这里可以实现文档预览功能\n      this.$message.info(this.$t('SOP.PreviewNotImplemented'))\n    },\n\n    // 通过审核\n    approveAudit(row) {\n      this.$confirm(this.$t('SOP.ConfirmApprove'), this.$t('GLOBAL._TS'), {\n        confirmButtonText: this.$t('GLOBAL._QD'),\n        cancelButtonText: this.$t('GLOBAL._QX'),\n        type: 'warning'\n      }).then(() => {\n        const auditData = {\n          ...row,\n          AuditResult: 2, // 审批通过\n          AuditUserId: this.$store.getters.name,\n          AuditComment: this.$t('SOP.AuditPassed')\n        }\n        saveSopAuditForm(auditData).then(res => {\n          this.$message.success(this.$t('SOP.ApproveSuccess'))\n          this.getTableData()\n        })\n      }).catch(() => {\n        this.$message.info(this.$t('GLOBAL._QXCZ'))\n      })\n    },\n\n    // 不通过审核\n    rejectAudit(row) {\n      this.currentAuditRow = row\n      this.rejectForm.auditComment = ''\n      this.rejectDialogVisible = true\n    },\n\n    // 确认不通过\n    confirmReject() {\n      this.$refs.rejectForm.validate((valid) => {\n        if (valid) {\n          const auditData = {\n            ...this.currentAuditRow,\n            AuditResult: 3, // 审批不通过\n            AuditUserId: this.$store.getters.name,\n            AuditComment: this.rejectForm.auditComment\n          }\n          saveSopAuditForm(auditData).then(res => {\n            this.$message.success(this.$t('SOP.RejectSuccess'))\n            this.rejectDialogVisible = false\n            this.getTableData()\n          })\n        }\n      })\n    },\n\n    // 格式化文件大小\n    formatFileSize(size) {\n      if (!size) return ''\n      const units = ['B', 'KB', 'MB', 'GB']\n      let index = 0\n      while (size >= 1024 && index < units.length - 1) {\n        size /= 1024\n        index++\n      }\n      return `${size.toFixed(2)} ${units[index]}`\n    },\n\n    // 格式化文档状态\n    formatStatus(status) {\n      const statusMap = {\n        1: this.$t('SOP.StatusValid'),\n        0: this.$t('SOP.StatusInvalid')\n      }\n      return statusMap[status] || this.$t('GLOBAL._WZ')\n    },\n\n    // 获取状态类型\n    getStatusType(status) {\n      return status === 1 ? 'success' : 'danger'\n    },\n\n    // 格式化操作类型\n    formatOperationType(type) {\n      const typeMap = {\n        1: this.$t('SOP.OperationCreate'),\n        2: this.$t('SOP.OperationModify'),\n        3: this.$t('SOP.OperationDelete')\n      }\n      return typeMap[type] || this.$t('GLOBAL._WZ')\n    },\n\n    // 格式化审核结果\n    formatAuditResult(result) {\n      const resultMap = {\n        0: this.$t('SOP.AuditPending'),\n        1: this.$t('SOP.AuditInProgress'),\n        2: this.$t('SOP.AuditApproved'),\n        3: this.$t('SOP.AuditRejected')\n      }\n      return resultMap[result] || this.$t('GLOBAL._WZ')\n    },\n\n    // 获取审核结果类型\n    getAuditResultType(result) {\n      const typeMap = {\n        0: 'warning',\n        1: 'primary',\n        2: 'success',\n        3: 'danger'\n      }\n      return typeMap[result] || 'info'\n    }\n  }\n}\n\n//<!-- 移到到src/local/en.json和zh-Hans.json -->\n//\"SopAudit\": {\n//    \"table\": {\n//        \"docId\": \"docId\",\n//        \"operationType\": \"operationType\",\n//        \"oldValue\": \"oldValue\",\n//        \"newValue\": \"newValue\",\n//        \"operatorId\": \"operatorId\",\n//        \"operateTime\": \"operateTime\",\n//        \"clientIp\": \"clientIp\",\n//        \"createdate\": \"createdate\",\n//        \"createuserid\": \"createuserid\",\n//        \"modifydate\": \"modifydate\",\n//        \"modifyuserid\": \"modifyuserid\",\n//        \"deleted\": \"deleted\",\n//    }\n//},\n</script>\n\n<style lang=\"scss\" scoped>\n.el-form-item--small.el-form-item {\n  margin-bottom: 0px;\n}\n\n.search-form-item {\n  .el-form-item__content {\n    width: 180px; // 统一宽度为180px\n  }\n}\n\n.root-head {\n  .search-form {\n    :deep(.el-form) {\n      .el-form-item--small.el-form-item {\n        margin-bottom: 0;\n      }\n    }\n\n    .form-content {\n      padding: 4px;\n\n      .search-area {\n        .search-row {\n          display: flex;\n          align-items: center;\n          gap: 4px;\n\n          .el-form-item {\n            margin: 0;\n            flex: none;\n\n            .el-form-item__label {\n              padding-right: 4px;\n              line-height: 26px;\n              font-size: 12px;\n            }\n\n            .el-form-item__content {\n              line-height: 26px;\n\n              .el-input,\n              .el-select {\n                :deep(.el-input__inner) {\n                  height: 26px;\n                  line-height: 26px;\n                  padding: 0 8px;\n                  font-size: 12px;\n                }\n              }\n            }\n          }\n\n          .action-buttons {\n            display: flex;\n            gap: 4px;\n            margin-left: 4px;\n\n            .el-button {\n              height: 26px;\n              padding: 0 10px;\n              font-size: 12px;\n\n              [class^=\"el-icon-\"] {\n                margin-right: 3px;\n                font-size: 12px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n.mt-8p {\n  margin-top: 8px;\n}\n\n.pd-left {\n  padding-left: 5px\n}\n\n.operation-buttons {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 2px;\n\n  .el-button--mini {\n    padding: 2px;\n    font-size: 11px;\n    border-radius: 3px;\n    min-width: auto;\n  }\n}\n</style>"]}]}