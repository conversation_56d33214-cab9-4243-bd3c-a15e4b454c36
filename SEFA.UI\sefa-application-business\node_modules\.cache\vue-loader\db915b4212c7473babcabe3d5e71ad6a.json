{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\factoryPlant\\physicalModelNew\\components\\physicalModelDialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\factoryPlant\\physicalModelNew\\components\\physicalModelDialog.vue", "mtime": 1750254216245}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["physicalModelDialog.vue"], "names": [], "mappings": ";AAkEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "physicalModelDialog.vue", "sourceRoot": "src/views/factoryPlant/physicalModelNew/components", "sourcesContent": ["<!-- add 新增 -->\r\n<!-- delete 批量删除 -->\r\n<!-- edit 修改 -->\r\n<template>\r\n    <v-dialog v-model=\"dialog\" persistent max-width=\"720px\">\r\n        <!-- 新增 -->\r\n        <v-card ref=\"form\">\r\n            <v-card-title class=\"d-flex text-h6 justify-space-between primary lighten-2\">\r\n                {{ $t('DFM_WLMX._XZMX') }}\r\n                <v-icon @click=\"closedialog\">mdi-close</v-icon>\r\n            </v-card-title>\r\n            <!-- 表单内容 -->\r\n            <v-card-text class=\"mt-7\">\r\n                <v-container>\r\n                    <v-form ref=\"addform\" v-model=\"valid\">\r\n                        <v-row>\r\n                            <v-col class=\"py-0 px-3\" cols=\"12\" sm=\"6\" md=\"6\">\r\n                                <treeselect v-model=\"ParentNameId.ParentId\" no-results-text=\"暂无数据\" :multiple=\"false\"\r\n                                    :placeholder=\"$t('DFM_WLMX._SJCD')\" :options=\"treeDatas\" :normalizer=\"normalizer\" />\r\n                            </v-col>\r\n                            <v-col class=\"py-0 px-3\" cols=\"12\" sm=\"6\" md=\"6\">\r\n                                <v-text-field ref=\"EquipmentName\" v-model=\"formModel.EquipmentName\"\r\n                                    :rules=\"[v => !!v || '名称不能为空']\" outlined dense :label=\"$t('DFM_WLMX._MC')\"\r\n                                    required></v-text-field>\r\n                            </v-col>\r\n                            <v-col class=\"py-0 px-3\" cols=\"12\" sm=\"6\" md=\"6\">\r\n                                <v-text-field ref=\"EquipmentCode\" v-model=\"formModel.EquipmentCode\"\r\n                                    :rules=\"[v => !!v || '编码不能为空']\" outlined dense :label=\"$t('DFM_WLMX._BM')\"\r\n                                    required></v-text-field>\r\n                            </v-col>\r\n                            <v-col class=\"py-0 px-3\" cols=\"12\" sm=\"6\" md=\"6\">\r\n                                <v-select v-model=\"formModel.Level\" :items=\"rootitems\" clearable dense outlined\r\n                                    :label=\"$t('DFM_WLMX._LX')\" @change=\"rootChange\"></v-select>\r\n                            </v-col>\r\n                            <v-col class=\"py-0 px-3\" cols=\"12\" sm=\"6\" md=\"6\">\r\n                                <v-text-field ref=\"SortNumber\" v-model=\"formModel.SortNumber\"\r\n                                    :rules=\"[v => !!v || '排序号不能为空']\" outlined dense label=\"排序号\"\r\n                                    required></v-text-field>\r\n                            </v-col>\r\n                            <v-col class=\"py-0 px-3\" cols=\"12\" sm=\"6\" md=\"6\">\r\n                                <v-radio-group v-model=\"formModel.Enabled\" row class=\"my-0\">\r\n                                    <template #label>\r\n                                        <div>是否启用</div>\r\n                                    </template>\r\n                                    <v-radio :label=\"$t('DFM_WLMX._GB')\" :value=\"0\"></v-radio>\r\n                                    <v-radio :label=\"$t('DFM_WLMX._QY')\" :value=\"1\"></v-radio>\r\n                                </v-radio-group>\r\n                            </v-col>\r\n                            <v-col class=\"py-0 px-3\" cols=\"12\">\r\n                                <v-textarea v-model=\"formModel.Remark\" rows=\"2\" outlined\r\n                                    :label=\"$t('DFM_WLMX._MS')\"></v-textarea>\r\n                            </v-col>\r\n                        </v-row>\r\n                    </v-form>\r\n                </v-container>\r\n            </v-card-text>\r\n            <v-card-actions class=\"lighten-3\">\r\n                <v-checkbox v-model=\"checkbox\" :label=\"$t('GLOBAL._QDBGBTC')\"></v-checkbox>\r\n                <v-spacer></v-spacer>\r\n                <v-btn color=\"primary\" @click=\"addSubmit\">{{ $t('GLOBAL._QD') }}</v-btn>\r\n                <v-btn @click=\"closedialog\">{{ $t('GLOBAL._GB') }}</v-btn>\r\n            </v-card-actions>\r\n        </v-card>\r\n    </v-dialog>\r\n</template>\r\n<script>\r\nimport { EquipmentSaveForm } from '@/api/factoryPlant/physicalModel.js';\r\n\r\nexport default {\r\n    name: 'DataDictionaryDialog',\r\n    props: {\r\n        parentId: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        treeDatas: {\r\n            type: Array,\r\n            default: () => []\r\n        },\r\n        rootitems: {\r\n            type: Array,\r\n            default: () => []\r\n        },\r\n        tableItem: {\r\n            type: Object,\r\n            default: () => { }\r\n        },\r\n        hasChildren: {\r\n            type: Object,\r\n            default: () => { }\r\n        }\r\n    },\r\n    data() {\r\n        return {\r\n            // 提交表单数据\r\n            valid: true,\r\n            dialog: false,\r\n            checkbox: true,\r\n            formModel: {\r\n                ParentId: null,\r\n                EquipmentCode: null,\r\n                EquipmentName: null,\r\n                Level: null,\r\n                Enabled: 1,\r\n                Remark: null,\r\n                SortNumber: 1\r\n            },\r\n            editformModel: {},\r\n            options: [],\r\n            normalizer(node) {\r\n                return {\r\n                    id: node.id,\r\n                    label: node.name,\r\n                    children: node.children\r\n                };\r\n            }\r\n        };\r\n    },\r\n    computed: {\r\n        ParentNameId() {\r\n            return {\r\n                ParentId: this.parentId\r\n            };\r\n        },\r\n    },\r\n    watch: {\r\n        dialog: {\r\n            handler(curVal) {\r\n                if (curVal && this.tableItem.ID) {\r\n                    this.formModel = {\r\n                        ParentName: this.tableItem.ParentName,\r\n                        ParentId: this.tableItem.ParentId == '0' ? null : this.tableItem.ParentId,\r\n                        EquipmentCode: this.tableItem.EquipmentCode,\r\n                        EquipmentName: this.tableItem.EquipmentName,\r\n                        Level: this.tableItem.Level,\r\n                        Enabled: this.tableItem.Enabled ? 1 : 0,\r\n                        Remark: this.tableItem.Remark,\r\n                        SortNumber: this.tableItem.SortNumber\r\n                    }\r\n                }\r\n            },\r\n            deep: true,\r\n            immediate: true\r\n        }\r\n    },\r\n    methods: {\r\n        // 初始化表单\r\n        initFrom() {\r\n            this.$refs.addform.reset();\r\n            // this.formModel = {\r\n            //     ParentId: null,\r\n            //     EquipmentCode: null,\r\n            //     EquipmentName: null,\r\n            //     Level: null,\r\n            //     Enabled: 1,\r\n            //     Remark: null\r\n            // }\r\n            this.$nextTick(() => {\r\n                this.formModel.Enabled = 1;\r\n            });\r\n        },\r\n        //新增\r\n        async addSubmit() {\r\n            console.log(this.hasChildren);\r\n            let fromvalidate = await this.$refs.addform.validate();\r\n            if (fromvalidate) {\r\n                let params = {\r\n                    ParentId: this.ParentNameId.ParentId || '0',\r\n                    EquipmentCode: this.formModel.EquipmentCode,\r\n                    EquipmentName: this.formModel.EquipmentName,\r\n                    Level: this.formModel.Level,\r\n                    Enabled: this.formModel.Enabled,\r\n                    Remark: this.formModel.Remark,\r\n                    SortNumber: this.formModel.SortNumber,\r\n                    ID: this.tableItem.ID ? this.tableItem.ID : ''\r\n                };\r\n                let res = await EquipmentSaveForm(params);\r\n                if (res.success) {\r\n                    this.$store.commit('SHOW_SNACKBAR', { text: '添加成功', color: 'success' });\r\n                    this.dialog = this.checkbox ? false : true;\r\n                    this.$emit('getdata')\r\n                    // this.$parent.$parent.GetEquipmentPageList();\r\n                    // this.$parent.$parent.GetEquipmentTree();\r\n                    this.initFrom();\r\n                }\r\n            }\r\n        },\r\n        //编辑\r\n        async editSubmit(type, item) {\r\n            if (type && type == 'Enabled') {\r\n                let params = {\r\n                    ParentId: item.ParentId,\r\n                    EquipmentCode: item.EquipmentCode,\r\n                    EquipmentName: item.EquipmentName,\r\n                    Level: item.Level,\r\n                    Enabled: item.Enabled ? 1 : 0,\r\n                    Remark: item.Remark,\r\n                    sortNumber: item.SortNumber,\r\n                    ID: item.ID\r\n                };\r\n                let res = await EquipmentSaveForm(params);\r\n                if (res.success) {\r\n                    this.$store.commit('SHOW_SNACKBAR', { text: '修改成功', color: 'success' });\r\n                }\r\n            } else {\r\n                console.log(this.tableItem);\r\n                let fromvalidate = await this.$refs.addform.validate();\r\n                if (fromvalidate) {\r\n                    let params = {\r\n                        ParentId: this.editformModel.ParentId,\r\n                        EquipmentCode: this.editformModel.EquipmentCode,\r\n                        EquipmentName: this.editformModel.EquipmentName,\r\n                        Level: this.editformModel.Level,\r\n                        Enabled: this.editformModel.Enabled,\r\n                        Remark: this.editformModel.Remark,\r\n                        ID: this.tableItem.ID\r\n                    };\r\n                    let res = await EquipmentSaveForm(params);\r\n                    if (res.success) {\r\n                        this.$store.commit('SHOW_SNACKBAR', { text: '修改成功', color: 'success' });\r\n                        this.dialog = this.checkbox ? false : true;\r\n                        this.$parent.$parent.GetEquipmentPageList();\r\n                        this.$parent.$parent.GetEquipmentTree();\r\n                        this.initFrom();\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        // 跟节点选择\r\n        rootChange(v) {\r\n            this.formModel.Level = v;\r\n        },\r\n        //关闭取消\r\n        closedialog() {\r\n            this.dialog = false;\r\n            this.$refs.addform.reset();\r\n            this.$nextTick(() => {\r\n                this.formModel.Enabled = 1 + '';\r\n            });\r\n        }\r\n    }\r\n};\r\n</script>\r\n"]}]}