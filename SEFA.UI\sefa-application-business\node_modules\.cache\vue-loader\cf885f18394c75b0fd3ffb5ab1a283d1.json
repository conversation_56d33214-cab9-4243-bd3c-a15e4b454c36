{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\WLPOlist\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\WLPOlist\\index.vue", "mtime": 1750254216308}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA+eA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/Producting/WLPOlist", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle PoList\">\r\n        <div class=\"InventorySearchBox\">\r\n            <div class=\"searchbox\">\r\n                <div class=\"datebox\">\r\n                    <div class=\"datepickbox\">\r\n                        <el-date-picker\r\n                            v-model=\"timepicker\"\r\n                            type=\"daterange\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            range-separator=\"-\"\r\n                            :start-placeholder=\"$t('DFM_RL._KSRQ')\"\r\n                            :end-placeholder=\"$t('DFM_RL._JSRQ')\"\r\n                        ></el-date-picker>\r\n                    </div>\r\n                </div>\r\n                <div class=\"inputformbox\" v-for=\"(item, index) in searchlist\" :key=\"index\">\r\n                    <v-text-field class=\"vueinput\" type=\"text\" v-if=\"item.type == 'input'\" v-model.trim=\"item.value\" dense outlined :label=\"item.name\" :placeholder=\"item.name\" />\r\n                    <el-select multiple filterable clearable v-model=\"item.value\" v-if=\"item.type == 'select'\" :myid=\"item.id\" :placeholder=\"item.name\">\r\n                        <el-option v-for=\"(it, ind) in item.option\" :key=\"ind\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                    </el-select>\r\n                    <el-checkbox v-if=\"item.type == 'checkbox'\" :myid=\"item.id\" v-model=\"item.value\">{{ item.name }}</el-checkbox>\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <el-input class=\"quickSearchinput\" :placeholder=\"$t('BatchPallets.QuickSearch')\" v-model=\"QuickSearch\">\r\n                    <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n                </el-input>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <el-button size=\"small\" style=\"margin-left: 5px\" icon=\"el-icon-s-help\" @click=\"getempty()\">{{ this.$t('GLOBAL._CZ') }}</el-button>\r\n                <el-button\r\n                    class=\"tablebtn\"\r\n                    v-has=\"'PRO_PREP_SHIFT'\"\r\n                    size=\"small\"\r\n                    :disabled=\"tablechooselist > 0 ? false : true\"\r\n                    style=\"width: 160px; margin-left: 5px\"\r\n                    icon=\"el-icon-plus\"\r\n                    @click=\"addNew()\"\r\n                >\r\n                    {{ this.$t('POList.AddMaterialPreShift') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" size=\"small\" :disabled=\"tablechooselist > 0 ? false : true\" style=\"width: 160px; margin-left: 5px\" icon=\"el-icon-plus\" @click=\"toRebuildBatch(true)\">\r\n                    {{ this.$t('POList.constructingbatches') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                </el-button>\r\n                <!-- <el-button class=\"tablebtn\" size=\"small\" :disabled=\"tablechooselist > 0 ? false : true\" style=\"width: 160px; margin-left: 5px\" icon=\"el-icon-plus\" @click=\"toBindPoRecipe(true)\">\r\n                    {{ this.$t('POList.BindPoRecipe') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                </el-button> -->\r\n                <!-- <el-button class=\"tablebtn\" size=\"small\" style=\"width: 160px; margin-left: 5px\" icon=\"el-icon-plus\" @click=\"ToThroatOutput()\">\r\n                    {{ this.$t('POList.ThroatOutput') }}\r\n                </el-button> -->\r\n            </div>\r\n        </div>\r\n        <div class=\"tablebox\">\r\n            <el-table border :data=\"tableList\" @selection-change=\"handleSelectionChange\" style=\"width: 100%\" height=\"700\">\r\n                <el-table-column type=\"selection\" width=\"55\" fixed=\"left\" :selectable=\"checkSelectable\"></el-table-column>\r\n                <el-table-column\r\n                    v-for=\"(item, index) in tableheader\"\r\n                    :fixed=\"item.fixed ? item.fixed : false\"\r\n                    :key=\"index\"\r\n                    sortable\r\n                    :align=\"item.align\"\r\n                    :prop=\"item.prop ? item.prop : item.value\"\r\n                    :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                    :width=\"item.width\"\r\n                >\r\n                    <template v-slot:header=\"scope\">\r\n                        <span v-if=\"item.icon\">\r\n                            <i :class=\"item.icon\"></i>\r\n                        </span>\r\n                        <span v-if=\"!item.icon\">{{ scope.column.label }}</span>\r\n                    </template>\r\n                    <template slot-scope=\"scope\">\r\n                        <i class=\"el-icon-document\" v-if=\"scope.column.property == 'detail'\" @click=\"detaildrawShow(scope.row)\"></i>\r\n                        <span v-else>\r\n                            <span v-if=\"scope.column.property == 'PlanStartTime'\">{{ $dayjs(scope.row.PlanStartTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'PlanEndTime'\">{{ $dayjs(scope.row.PlanEndTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                            <!-- <span v-else-if=\"scope.column.property == 'SapDate'\">{{ scope.row.SapDate == null ? '' : $dayjs(scope.row.SapDate).format('YYYY-MM-DD') }}</span> -->\r\n                          \r\n                            <span v-else-if=\"scope.column.property == 'Sequence'\">\r\n                                <div style=\"color: #808080;font-weight: 900;font-size: larger\">{{ scope.row.Sequence }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'PoStatus'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: `${getStatusColor(scope.row.PoStatus)}` }\">\r\n                                    {{ getStatusName(scope.row.PoStatus) }}\r\n                                </div>\r\n                            </span>\r\n                            <!-- <span style=\"display: flex\" v-else-if=\"scope.column.property == 'Execute'\">\r\n                                <div class=\"statusbox Execute1\">\r\n                                    {{ scope.row.RunningCount }}\r\n                                </div>\r\n                                <div class=\"statusbox Execute2\">\r\n                                    {{ scope.row.StopedCount }}\r\n                                </div>\r\n                                <div class=\"statusbox Execute3\">\r\n                                    {{ scope.row.ExecutionCount }}\r\n                                </div>\r\n                            </span> -->\r\n                            <!-- <span v-else-if=\"scope.column.property == 'Bezei'\">\r\n                                <div v-for=\"(each, index1) in scope.row[scope.column.property].split(';')\" :key=\"index1\" class=\"text-left\">{{ each }}</div>\r\n                            </span> -->\r\n                            <!-- <span v-else-if=\"scope.column.property == 'Count'\">\r\n                                {{ scope.row.Sequence + '/' + scope.row.Count }}\r\n                            </span> -->\r\n                            <!-- <span v-else-if=\"scope.column.property == 'IsHavePreservative'\">\r\n                                <i :class=\"scope.row[item.value] === '1' ? 'el-icon-star-on' : ''\"></i>\r\n                            </span> -->\r\n                            <span v-else-if=\"scope.column.property == 'QaStatus'\">\r\n                                <!-- <div class=\"statusbox\" :style=\"{ background: `${(scope.row.QaStatus=='通过'?'green':'yellow')}`}\">\r\n                                    {{ scope.row.QaStatus }}\r\n                                </div> -->\r\n                                <div class=\"qAstatusbox\" :style=\"{ background: scope.row.QaStatus === '通过' ? '#3DCD58' : '#FFA500' }\">{{ scope.row.QaStatus }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'operate'\" style=\"display: flex; justify-content: space-evenly\">\r\n                                <el-button class=\"operatebtn\" size=\"mini\" style=\"width: 70px\" icon=\"el-icon-full-screen\" @click=\"ScanOpen(scope.row)\">\r\n                                    {{ $t('Consume.Scan') }}\r\n                                </el-button>\r\n                                <el-button class=\"operatebtn\" size=\"mini\" style=\"width: 70px\" icon=\"el-icon-printer\" @click=\"toColos(scope.row)\">\r\n                                    {{ $t('Inventory.Print') }}\r\n                                </el-button>\r\n                                <el-button\r\n                                    class=\"operatebtn\"\r\n                                    size=\"mini\"\r\n                                    style=\"width: 70px\"\r\n                                    :disabled=\"scope.row.ExecutionId == '' || scope.row.ExecutionId == null\"\r\n                                    icon=\"el-icon-video-play\"\r\n                                    @click=\"ProduceOpen(scope.row)\"\r\n                                >\r\n                                    {{ $t('Consume.Produce') }}\r\n                                </el-button>\r\n                                <!-- <i class=\"el-icon-zoom-in\" @click=\"ScanOpen(scope.row)\" style=\"margin-right: 12px\"></i>\r\n                                <i class=\"el-icon-printer\" @click=\"toColos(scope.row)\"></i> -->\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'ActualQty'\">{{ scope.row[item.value] }}{{ scope.row.Unit }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'PlanQty'\">{{ scope.row[item.value] }}{{ scope.row.Unit }}</span>\r\n                            <!-- <span v-else-if=\"scope.column.property == 'ActualQty'\">{{ scope.row[item.value] }}{{ scope.row.Unit }}</span> -->\r\n                            <span v-else-if=\"scope.column.property == 'ProduceStatus'\">{{ scope.row[item.value] === null ? '' : $t(`POList.${scope.row[item.value]}`) }}</span>\r\n                            <!-- <span v-else-if=\"scope.column.property == 'Reason'\">{{ getReasonName(scope.row.ProduceStatus, scope.row.Reason) }}</span> -->\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n            <div class=\"paginationbox\">\r\n                <el-pagination\r\n                    @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageOptions.page\"\r\n                    :page-sizes=\"pageOptions.pageSizeitems\"\r\n                    :page-size=\"pageOptions.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next, jumper\"\r\n                    :total=\"pageOptions.total\"\r\n                    background\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Consume.Produce')\" id=\"Producedialog\" :visible.sync=\"ProduceModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Producelist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <span>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Produceinputlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input :disabled=\"item.disable\" onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'inputNumber'\" v-model=\"item.value\">\r\n                            <template slot=\"append\">{{ BtnObj.Unit1 }}</template>\r\n                        </el-input>\r\n                        <el-input :disabled=\"item.disable\" v-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                        <el-select :disabled=\"item.disable\" clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                            <el-option v-for=\"it in item.option\" :key=\"it.value\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                        </el-select>\r\n                        <el-date-picker :disabled=\"item.disable\" v-else-if=\"item.type == 'date'\" @change=\"GetDate(item.id)\" :type=\"item.datetype\" v-model=\"item.value\"></el-date-picker>\r\n                        <el-switch v-else-if=\"item.type == 'switch'\" v-model=\"item.value\" active-color=\"#3dcd58\" inactive-color=\"#ff4949\"></el-switch>\r\n                    </div>\r\n                </div>\r\n                <div class=\"dialogdetailbox\" v-if=\"Produceinputlist[5].value == true\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ $t('Consume.selectprinter') }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-select clearable v-model=\"selectprinter\" filterable>\r\n                            <el-option v-for=\"it in selectprinterOption\" :key=\"it.value\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                        </el-select>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-video-play\" @click=\"SaveProduce()\">\r\n                    {{ $t('Consume.Produce') }}\r\n                </el-button>\r\n                <el-button @click=\"ProduceModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-drawer size=\"80%\" :wrapperClosable=\"false\" :visible.sync=\"detailShow\" direction=\"rtl\">\r\n            <div slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"drawerTitlelabel\">\r\n                    <span style=\"font-size: 1.5rem; color: #494949; margin-right: 5px\">{{ detailobj.ProductionOrderNo }}</span>\r\n                    <span>{{ detailobj.Resource }}</span>\r\n                    |\r\n                    <span>{{ detailobj.MaterialCode }}</span>\r\n                    -\r\n                    <span>{{ detailobj.MaterialDescription }}</span>\r\n                    |\r\n                    <span>{{ detailobj.PlanQty }}</span>\r\n                    |\r\n                    <span style=\"width: 11vh; padding: 4px; margin-right: 0; display: inline-block; text-align: center\" class=\"statusbox\" :style=\"{ background: getStatusColor(detailobj.PoStatus) }\">\r\n                        {{ getStatusName(detailobj.PoStatus) }}\r\n                    </span>\r\n                    |\r\n                    <span style=\"color: #494949\">{{ detailobj.PlanStartTime }}</span>\r\n                    -\r\n                    <span style=\"color: #494949\">{{ detailobj.PlanEndTime }}</span>\r\n                </div>\r\n                <!-- <div class=\"drawEditBox\">\r\n                    <i class=\"el-icon-edit-outline\" @click=\"editShow()\"></i>\r\n                </div> -->\r\n            </div>\r\n            <div class=\"InventorySearchBox\" style=\"margin-bottom: 0px\">\r\n                <!-- <div class=\"InventorySearchBox\" style=\"margin-bottom: 0px\" v-if=\"Number(detailobj.PoStatus) <= 3\"> -->\r\n                <div class=\"searchbox\">\r\n                    <!-- <el-button size=\"small\" v-if=\"detailobj.PoStatus == '0'\" @click=\"toRelease()\" class=\"tablebtn\" style=\"margin-left: 5px\">\r\n                        {{ $t('POList.Release') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toComplete()\" v-if=\"detailobj.PoStatus == '1' || detailobj.PoStatus == '2' || detailobj.PoStatus == '3'\" class=\"tablebtn\" style=\"margin-left: 5px\">\r\n                        {{ $t('POList.Complete') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toRevokeRelease()\" v-if=\"detailobj.PoStatus == '1'\" class=\"tablebtn\" style=\"margin-left: 5px\">\r\n                        {{ $t('POList.RevokeRelease') }}\r\n                    </el-button> -->\r\n\r\n                    <el-button\r\n                        size=\"small\"\r\n                        @click=\"toRelease()\"\r\n                        v-if=\"(detailobj.PoStatus == '1' && detailobj.NeedQARelease == '0') || (detailobj.PoStatus == '7' && detailobj.NeedQARelease == '1')\"\r\n                        class=\"tablebtn\"\r\n                        style=\"margin-left: 5px\"\r\n                    >\r\n                        {{ $t('POList.Release') }}\r\n                    </el-button>\r\n                    <el-button\r\n                        size=\"small\"\r\n                        @click=\"toComplete()\"\r\n                        v-if=\"detailobj.PoStatus == '2' || detailobj.PoStatus == '5' || detailobj.PoStatus == '6'\"\r\n                        class=\"tablebtn\"\r\n                        style=\"margin-left: 5px; width: 170px\"\r\n                    >\r\n                        {{ $t('POList.Complete') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"Reopen()\" v-if=\"detailobj.PoStatus == '3'\" class=\"tablebtn\" style=\"margin-left: 5px\">\r\n                        {{ $t('POList.Reopen') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toWCSUpDate()\" v-if=\"detailobj.SendWcs == '1'\" class=\"tablebtn\" style=\"margin-left: 5px; width: 170px\">\r\n                        {{ $t('POList.WCSUpDate') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toRebuildBatch()\" v-if=\"detailobj.PoStatus == '2'\" class=\"tablebtn\" style=\"margin-left: 5px; width: 170px\">\r\n                        {{ $t('POList.constructingbatches') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toBindPoRecipe()\" class=\"tablebtn\" style=\"margin-left: 5px; width: 170px\">\r\n                        {{ $t('POList.BindPoRecipe') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n            <el-tabs v-model=\"activeName\" @tab-click=\"tabChange\" type=\"border-card\">\r\n                <el-tab-pane :label=\"this.$t('POList.Execute')\" name=\"Execute\">\r\n                    <execute :ProductionOrderNo=\"detailobj.ID\" ref=\"execute\"></execute>\r\n                </el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.batch')\" name=\"batch\">\r\n                    <batch :ProductionOrderNo=\"detailobj.ID\" ref=\"batch\"></batch>\r\n                </el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.consume')\" name=\"consume\"><consume :ProductionOrderNo=\"detailobj.ID\" ref=\"consume\"></consume></el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.produce')\" name=\"produce\"><produce :ProductionOrderNo=\"detailobj.ID\" ref=\"produce\"></produce></el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.parameter')\" name=\"parameter\"><parameter :ProductionOrderNo=\"detailobj.ID\" ref=\"parameter\"></parameter></el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.formula')\" name=\"formula\"><formula :ProductionOrderNo=\"detailobj.ID\" ref=\"formula\"></formula></el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.Processlongtext')\" name=\"Processlongtext\">\r\n                    <processlongtext :ProductionOrderNo=\"detailobj.ID\" :NeedQARelease=\"detailobj.NeedQARelease\" :PoStatus=\"detailobj.PoStatus\" ref=\"Processlongtext\"></processlongtext>\r\n                </el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.property')\" name=\"property\">\r\n                    <property :ProductionOrderNo=\"detailobj.ID\" ref=\"property\"></property>\r\n                </el-tab-pane>\r\n            </el-tabs>\r\n        </el-drawer>\r\n        <el-dialog :title=\"$t('POList.WCSUpDate')\" id=\"Editdialog\" :visible.sync=\"WCSModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in WCSinputlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500\">{{ item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'number'\" v-model=\"item.value\">\r\n                            <template slot=\"append\">{{ detailobj.Unit }}</template>\r\n                        </el-input>\r\n                        <el-input v-else-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                        <span v-else>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-check-outline\" @click=\"WCSSave()\">\r\n                    {{ $t('GLOBAL._QD') }}\r\n                </el-button>\r\n                <el-button @click=\"WCSModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n\r\n        <el-dialog :title=\"$t('POList.EditTitle')\" id=\"Editdialog\" :visible.sync=\"EditModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Editlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'input'\" v-model=\"item.value\">\r\n                            <template slot=\"append\">{{ $t('POList.KGHOUR') }}</template>\r\n                        </el-input>\r\n                        <span v-else>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Editinputlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500\">{{ item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'input'\" v-model=\"item.value\">\r\n                            <template slot=\"append\">{{ detailobj.Unit }}</template>\r\n                        </el-input>\r\n                        <el-select clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                            <el-option v-for=\"(it, ind) in item.options\" :key=\"ind\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                        </el-select>\r\n                        <el-input v-else-if=\"item.type == 'textArea'\" type=\"textarea\" autosize v-model=\"item.value\"></el-input>\r\n                        <span v-else>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-edit-outline\">\r\n                    {{ $t('POList.Edit') }}\r\n                </el-button>\r\n                <el-button @click=\"EditModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n\r\n        <el-dialog :title=\"$t('POList.AddMaterialPreShift')\" :visible.sync=\"ShiftListModal\">\r\n            <div class=\"dialogdetailbox\">\r\n                <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500\">{{ $t('POList.MaterialPreShift') }}*</div>\r\n                <div class=\"dialogdetailsinglevalue\">\r\n                    <el-select v-model=\"ShiftID\">\r\n                        <el-option v-for=\"it in ShiftList\" :key=\"it.ID\" :label=\"it.Name\" :value=\"it.ID\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"ShiftListModal = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-edit-outline\" @click=\"ShiftSave()\">\r\n                    {{ $t('GLOBAL._QR') }}\r\n                </el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog :title=\"$t('POList.Complete')\" :visible.sync=\"CompleteModel\" width=\"650px\">\r\n            <div class=\"dialogdetailbox\" v-for=\"(item, index) in Completelist\" :key=\"index\">\r\n                <div class=\"dialogdetailsinglelabel\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                <div class=\"dialogdetailsinglevalue\">\r\n                    <el-select @change=\"getData2(item)\" v-model=\"item.value\" clearable filterable v-if=\"item.type == 'select'\">\r\n                        <el-option v-for=\"(it, ind) in item.options\" :key=\"ind\" :label=\"it.label\" :value=\"it.key\"></el-option>\r\n                    </el-select>\r\n                    <span v-else>{{ item.value }}</span>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"CompleteModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-edit-outline\" @click=\"CompleteSave()\">\r\n                    {{ $t('GLOBAL._QR') }}\r\n                </el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog :title=\"$t('Inventory.Print')\" id=\"Startdialog\" :visible.sync=\"StartModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Startlist\" :key=\"index\" v-show=\"!item.notShow\">\r\n                    <div class=\"dialogdetailsinglelabel\" :style=\"{ width: item.type == 'BatchCode' ? '20%' : '20%' }\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                    <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: item.type == 'BatchCode' ? '400px' : '77%' }\">\r\n                        <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\" :disabled=\"item.disabled\"></el-input>\r\n                        <div v-else-if=\"item.type == 'BatchCode'\" style=\"display: flex\">\r\n                            <el-input @change=\"getQrCode()\" v-model=\"item.value\"></el-input>\r\n                            <el-input v-model=\"item.value2\" disabled></el-input>\r\n                            <el-input @change=\"getQrCode()\" v-model=\"item.value3\"></el-input>\r\n                            <el-button class=\"tablebtn\" @click=\"getBatchCode()\" size=\"mini\" style=\"margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff\" icon=\"el-icon-refresh\"></el-button>\r\n                        </div>\r\n                        <el-select @change=\"GetData()\" clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                            <el-option v-for=\"(it, ind) in item.options\" :key=\"ind\" :label=\"it.value\" :value=\"it.key\"></el-option>\r\n                        </el-select>\r\n                        <el-date-picker\r\n                            @change=\"GetData()\"\r\n                            v-else-if=\"item.type == 'date'\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                            :disabled=\"item.disabled\"\r\n                            v-model=\"item.value\"\r\n                            type=\"datetime\"\r\n                        ></el-date-picker>\r\n                        <span v-else>{{ chooseItem.TargetQuantity }}{{ chooseItem.Unit1 }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-video-play\" @click=\"SendColos()\">\r\n                    {{ $t('Overview.SendColos') }}\r\n                </el-button>\r\n                <el-button @click=\"StartModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog :title=\"$t('Consume.Scan')\" id=\"SSCCdialog\" :visible.sync=\"SSCCModel\" width=\"650px\">\r\n            <div style=\"display: flex\">\r\n                <div :style=\"'100%'\">\r\n                    <div class=\"dialogdetailbox\">\r\n                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: '20%' }\">{{ $t('Consume.SSCC') + ' *' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: '77%' }\">\r\n                            <el-input v-model=\"SSCCValue\" @keyup.enter.native=\"SearchSscc()\">\r\n                                <template slot=\"append\"><i slot=\"suffix\" class=\"el-icon-full-screen\" @click=\"SearchSscc()\"></i></template>\r\n                            </el-input>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"dialogdetailbox\">\r\n                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: '20%' }\">{{ $t('Overview.Batch') + ' *' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: '77%' }\">\r\n                            <el-select clearable v-model=\"BatchId\" filterable>\r\n                                <el-option v-for=\"it in SegmentBatchList\" :key=\"it.key\" :label=\"it.value\" :value=\"it.key\"></el-option>\r\n                            </el-select>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-refresh-left\" @click=\"ShowQRCode()\">\r\n                    {{ $t('Consume.Scan') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-zoom-in\" @click=\"SearchSscc()\">\r\n                    {{ $t('Consume.Search') }}\r\n                </el-button>\r\n                <el-button @click=\"SSCCModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <QRcode ref=\"QRcode\" @getQRcodesRes=\"getQRcodesRes\"></QRcode>\r\n        <el-dialog id=\"Consumedialog\" :visible.sync=\"ConsumeModel\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">{{ $t('Consume.Consume') }}</div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Consumelist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <span>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Consumeinputlist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <el-input type=\"number\" v-if=\"item.id == 'Quantity'\" v-model=\"item.value\">\r\n                                <template slot=\"append\">{{ ConsumeObj.ChangeUnit }}</template>\r\n                            </el-input>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-folder-checked\" @click=\"ConsumeSave()\">\r\n                    {{ $t('GLOBAL._BC') }}\r\n                </el-button>\r\n                <el-button @click=\"ConsumeModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog :title=\"$t('POList.ThroatOutput')\" :visible.sync=\"ThroatOutputModal\" width=\"500px\">\r\n            <div class=\"dialogdetailbox\" style=\"margin-top: 15px\">\r\n                <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500; width: 45%\">{{ $t('PackagingWorkOrder.Order_Sort.PlanStartTime') }}*</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: 100%\">\r\n                    <el-date-picker v-model=\"PlanStartTime\" type=\"datetime\" value-format=\"yyyy-MM-dd HH:mm:ss\" placeholder=\"\"></el-date-picker>\r\n                </div>\r\n            </div>\r\n            <div class=\"dialogdetailbox\">\r\n                <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500; width: 45%\">{{ $t('POList.SourceStroage') }}*</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: 100%\">\r\n                    <el-select v-model=\"Source\" filterable>\r\n                        <el-option v-for=\"it in SourceList\" :key=\"it.key\" :label=\"it.value\" :value=\"it.key\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"ThroatOutputModal = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-edit-outline\" @click=\"ThroatOutputSave()\">\r\n                    {{ $t('GLOBAL._QR') }}\r\n                </el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { WLPOlist } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetListViewList,\r\n    GetProductionUpdateShift,\r\n    GetProductionShiftSelect,\r\n    GetLastProcessData,\r\n    UpdateMaterialProcessDataStatus,\r\n    toSendOrderInfoToSS,\r\n    GettoRelease,\r\n    UpdatePoStatus,\r\n    RebuildBatch,\r\n    BindPoRecipe,\r\n    OperationPo,\r\n    MyGetEquipmentsByOrderId,\r\n    MySendLabelPrintToColos,\r\n    MyGetQrCode,\r\n    getDataDictionary,\r\n    GetSegmentBatchList\r\n} from '@/api/Producting/POlist.js';\r\nimport { GetPrinit2 } from '@/api/Inventory/common.js';\r\nimport { GetProduceOpen, GetBPEquipmentsSelect, GetBatchCode, ConsumeScanSSCC, ConsumeViewSave, getConsumeViewEntity, ProduceLocation, ProduceSave } from '@/api/Inventory/Overview.js';\r\nimport { GetDataTreeList } from '@/api/factoryPlant/process.js';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport moment from 'moment';\r\nimport { GetMSelectListClass, GetUnitList } from '@/api/Inventory/Inventory.js';\r\nexport default {\r\n    components: {\r\n        execute: () => import('./components/Execute'),\r\n        consume: () => import('./components/consume'),\r\n        batch: () => import('./components/batch'),\r\n        parameter: () => import('./components/parameter'),\r\n        formula: () => import('./components/formula'),\r\n        produce: () => import('./components/produce'),\r\n        processlongtext: () => import('./components/Processlongtext'),\r\n        property: () => import('./components/property')\r\n    },\r\n    data() {\r\n        return {\r\n            ThroatOutputModal: false,\r\n            PlanStartTime: null,\r\n            Source: '',\r\n            SourceList: [],\r\n            StartModel: false,\r\n            WCSModel: false,\r\n            WCSinputlist: [\r\n                {\r\n                    label: this.$t('POList.Num'),\r\n                    id: 'quantity',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'number'\r\n                },\r\n                {\r\n                    label: this.$t('TRACE_WGJC._PCH'),\r\n                    id: 'lotCode',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'input'\r\n                }\r\n            ],\r\n            Startlist: [\r\n                {\r\n                    notShow: true,\r\n                    label: this.$t('ANDON_BJZY.EquipmentName'),\r\n                    id: 'EquipmentId',\r\n                    value: '',\r\n                    // require: true,\r\n                    type: 'select',\r\n                    options: []\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.BatchCode'),\r\n                    id: 'LotCode',\r\n                    require: true,\r\n                    value: '',\r\n                    value2: '',\r\n                    value3: '',\r\n                    type: 'BatchCode'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.QrCode'),\r\n                    id: 'QrCode',\r\n                    disabled: true,\r\n                    value: '',\r\n                    type: 'input'\r\n                },\r\n                {\r\n                    label: this.$t('$vuetify.dataTable.INV_TPQD.PrintCount'),\r\n                    id: 'PrintCount',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'input'\r\n                }\r\n            ],\r\n            showFrom: true,\r\n            timepicker: [this.getDay(0), this.getDay(3)],\r\n            tableheader: WLPOlist,\r\n            tableId: 'PRO_POLIST',\r\n            QuickSearch: '',\r\n            pageOptions: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            searchlist: [\r\n                {\r\n                    type: 'select',\r\n                    name: this.$t('POList.Status'),\r\n                    id: 'Available',\r\n                    value: [],\r\n                    option: []\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'MaterialDescription',\r\n                    name: this.$t('POList.Material')\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'Resource',\r\n                    name: this.$t('POList.Source')\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'ProductionOrderNo',\r\n                    name: this.$t('POList.ProcessOrder')\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'Formula',\r\n                    name: this.$t('$vuetify.dataTable.PRO_POLIST.Formula')\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'SegmentCode',\r\n                    name: this.$t('POList.SegmentCode')\r\n                }\r\n            ],\r\n            tableList: [],\r\n            activeName: 'Execute',\r\n            detailShow: false,\r\n            detailobj: {},\r\n            ProduceModel: false,\r\n            BtnObj: {},\r\n            Producelist: [\r\n                {\r\n                    label: this.$t('Consume.ProcessOrder'),\r\n                    value: '',\r\n                    id: 'ProductionOrderNo'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Source'),\r\n                    value: '',\r\n                    id: 'Source'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Material'),\r\n                    value: '',\r\n                    id: 'MaterialDescription'\r\n                }\r\n            ],\r\n            Produceinputlist: [\r\n                {\r\n                    label: this.$t('Consume.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    value: '',\r\n                    type: 'date',\r\n                    datetype: 'datetime'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.ExpirationDate'),\r\n                    id: 'ExpirationDate',\r\n                    value: '',\r\n                    type: 'date',\r\n                    disable: true,\r\n                    datetype: 'datetime'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Batch'),\r\n                    id: 'BatchCode',\r\n                    value: '',\r\n                    disable: true,\r\n                    type: 'input'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Location'),\r\n                    id: 'Location',\r\n                    value: '',\r\n                    type: 'select',\r\n                    option: [],\r\n                    require: true\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Quantity'),\r\n                    id: 'Quantity',\r\n                    value: '',\r\n                    type: 'inputNumber',\r\n                    require: true\r\n                },\r\n                {\r\n                    label: this.$t('Consume.printlabel'),\r\n                    id: 'printlabel',\r\n                    value: false,\r\n                    type: 'switch'\r\n                }\r\n            ],\r\n            selectprinter: '',\r\n            selectprinterOption: [],\r\n            detailArr: [],\r\n            EditModel: false,\r\n            Editlist: [\r\n                {\r\n                    label: this.$t('POList.ProductionOrderNo'),\r\n                    value: '',\r\n                    id: 'ProductionOrderNo'\r\n                },\r\n                {\r\n                    label: this.$t('POList.Source'),\r\n                    value: '',\r\n                    id: 'Source'\r\n                },\r\n                {\r\n                    label: this.$t('POList.Material'),\r\n                    value: '',\r\n                    id: 'Material'\r\n                },\r\n                {\r\n                    label: this.$t('POList.MaterialVersion'),\r\n                    value: '',\r\n                    id: 'MaterialVersionNumber'\r\n                },\r\n                {\r\n                    label: this.$t('POList.PlanStartTime'),\r\n                    value: '',\r\n                    id: 'PlanStartTime'\r\n                },\r\n                {\r\n                    label: this.$t('POList.PlanEndTime'),\r\n                    value: '',\r\n                    id: 'PlanEndTime'\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    label: this.$t('POList.Speed'),\r\n                    value: '',\r\n                    id: 'Speed'\r\n                }\r\n            ],\r\n            Editinputlist: [\r\n                {\r\n                    label: this.$t('POList.Status'),\r\n                    id: 'Status',\r\n                    value: '',\r\n                    type: 'select',\r\n                    options: []\r\n                },\r\n                {\r\n                    label: this.$t('POList.Num'),\r\n                    id: 'Num',\r\n                    value: '',\r\n                    type: 'input'\r\n                }\r\n            ],\r\n            StatusList: [],\r\n            ReasonList: [],\r\n            ShiftList: [],\r\n            selectTabelData: [],\r\n            tablechooselist: 0,\r\n            ShiftID: '',\r\n            ShiftListModal: false,\r\n            checkRow: {},\r\n            CompleteModel: false,\r\n            SSCCModel: false,\r\n            SSCCValue: '',\r\n            BatchId: '',\r\n            ConsumeModel: false,\r\n            ConsumeObj: {},\r\n            SegmentBatchList: [],\r\n            Consumelist: [\r\n                {\r\n                    label: this.$t('Consume.ProcessOrder'),\r\n                    id: 'ProcessOrderNo',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Location'),\r\n                    id: 'Location',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Material'),\r\n                    id: 'Material',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Lot'),\r\n                    id: 'Batch',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.SSCC'),\r\n                    id: 'Sscc',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Quantity'),\r\n                    id: 'QuantitywithUnit',\r\n                    value: ''\r\n                }\r\n            ],\r\n            Consumeinputlist: [\r\n                {\r\n                    label: this.$t('Consume.Quantity'),\r\n                    value: '',\r\n                    id: 'Quantity',\r\n                    require: true\r\n                }\r\n            ],\r\n            detailList: [\r\n                {\r\n                    label: this.$t('Consume.Material'),\r\n                    value: '',\r\n                    id: 'Material'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.StorageBin'),\r\n                    value: '',\r\n                    id: 'StorageBin'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Required'),\r\n                    value: '',\r\n                    id: 'Quantity2'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Consumptions'),\r\n                    value: '',\r\n                    id: 'Quantity1'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Remaining'),\r\n                    value: '',\r\n                    id: 'Remaining'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Uom'),\r\n                    value: '',\r\n                    id: 'Unit1'\r\n                }\r\n            ],\r\n            Completelist: [\r\n                {\r\n                    label: this.$t('POList.Material'),\r\n                    id: 'Material',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.PlanQty'),\r\n                    id: 'PlanQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.ActualQty'),\r\n                    id: 'ActualQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.ProduceStatus'),\r\n                    id: 'ProduceStatus',\r\n                    require: true,\r\n                    type: 'select',\r\n                    options: [\r\n                        {\r\n                            key: 'NotComplete',\r\n                            label: this.$t('POList.NotComplete')\r\n                        },\r\n                        {\r\n                            key: 'OverComplete',\r\n                            label: this.$t('POList.OverComplete')\r\n                        },\r\n                        {\r\n                            key: 'CompleteAtOnce',\r\n                            label: this.$t('POList.CompleteAtOnce')\r\n                        }\r\n                    ],\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.Reason'),\r\n                    id: 'Reason',\r\n                    type: 'select',\r\n                    require: true,\r\n                    options: [],\r\n                    value: ''\r\n                }\r\n            ]\r\n        };\r\n    },\r\n    mounted() {\r\n        this.getSourceList();\r\n        this.getStatus();\r\n        this.GetShiftSelect();\r\n        this.getprintList();\r\n    },\r\n    methods: {\r\n        async Reopen() {\r\n            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_Reopen')}`, '', {\r\n                confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                type: 'warning'\r\n            }).then(async () => {\r\n                let params = {\r\n                    Id: this.detailobj.ID,\r\n                    Status: 6\r\n                };\r\n                let res = await UpdatePoStatus(params);\r\n                this.detailShow = false;\r\n                this.getPageList();\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            });\r\n        },\r\n        async ThroatOutputSave() {\r\n            if (this.Source == '' || this.PlanStartTime == '') {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                EquipmentId: this.Source,\r\n                ProductionDate: this.PlanStartTime\r\n            };\r\n            let res = await GetProduceOpen(params);\r\n            let data = res.response;\r\n            if (data == null) {\r\n                Message({\r\n                    message: `${this.$t('POList.NotFoundPO')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            } else {\r\n                this.ProduceOpen(data);\r\n                this.ThroatOutputModal = false;\r\n            }\r\n        },\r\n        async getSourceList() {\r\n            let res = await GetBPEquipmentsSelect();\r\n            this.SourceList = res.response;\r\n        },\r\n        ToThroatOutput() {\r\n            this.PlanStartTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n            this.Source = '';\r\n            this.ThroatOutputModal = true;\r\n        },\r\n        ShowQRCode() {\r\n            this.$refs.QRcode.getQRcode();\r\n        },\r\n        getQRcodesRes(val) {\r\n            this.SSCCValue = val.text;\r\n            this.SearchSscc();\r\n        },\r\n        async getprintList() {\r\n            let res = await GetPrinit2();\r\n            res.response.forEach(item => {\r\n                item.value = item.ID;\r\n                item.label = item.Code;\r\n                item.ItemName = item.Code;\r\n                item.ItemValue = item.ID;\r\n            });\r\n            this.selectprinterOption = res.response;\r\n        },\r\n        async SaveProduce() {\r\n            if (this.Produceinputlist[5].value === true && this.selectprinter === '') {\r\n                Message({\r\n                    message: `${this.$t('Inventory.PleaseSelectPrinter')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let flag = this.Produceinputlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                ExpirationDate: this.Produceinputlist[1].value,\r\n                ProductionDate: this.Produceinputlist[0].value,\r\n                BatchId: this.BtnObj.BatchId,\r\n                Location: this.Produceinputlist[3].value,\r\n                Quantity: this.Produceinputlist[4].value,\r\n                EquipmentId: this.BtnObj.RunEquipmentId,\r\n                ExecutionId: this.BtnObj.ExecutionId,\r\n                UnitId: this.BtnObj.UnitId,\r\n                IsPrint: this.Produceinputlist[5].value,\r\n                PrintId: this.selectprinter\r\n            };\r\n            let res = await ProduceSave(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.getPageList();\r\n            this.ProduceModel = false;\r\n        },\r\n        async ProduceOpen(row) {\r\n            this.Produceinputlist[3].value = '';\r\n            this.BtnObj = row;\r\n            let params = {\r\n                EquipmentId: row.RunEquipmentId\r\n            };\r\n            this.selectprinter = '';\r\n            let res = await ProduceLocation(params);\r\n            res.response.forEach(item => {\r\n                item.label = item.LocationName;\r\n                item.value = item.LocationId;\r\n                if (item.IsDefault == '1') {\r\n                    this.Produceinputlist[3].value = item.LocationId;\r\n                }\r\n            });\r\n            this.Produceinputlist[3].option = res.response;\r\n            if (this.selectprinterOption.length != 0) {\r\n                this.selectprinter = this.selectprinterOption[0].value;\r\n            }\r\n            for (let k in row) {\r\n                this.Producelist.forEach(item => {\r\n                    if (item.id == k) {\r\n                        if (k == 'MaterialDescription') {\r\n                            item.value = row.MaterialDescription + '-' + row.MaterialCode;\r\n                        } else {\r\n                            item.value = row[k];\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n            this.Produceinputlist.forEach(item => {\r\n                if (item.id == 'printlabel') {\r\n                    item.value = false;\r\n                } else if (item.id != 'Location') {\r\n                    item.value = '';\r\n                }\r\n            });\r\n            this.Produceinputlist[0].value = row.ProductionDate;\r\n            this.Produceinputlist[1].value = row.ExpirationDate;\r\n            this.Produceinputlist[2].value = row.BatchCode;\r\n            this.ProduceModel = true;\r\n        },\r\n        checkSelectable(row) {\r\n            if (row.PoStatus == '1' || row.PoStatus == '2') {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        },\r\n        toWCSUpDate() {\r\n            this.WCSinputlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            this.WCSModel = true;\r\n        },\r\n        toColos(row) {\r\n            this.checkRow = row;\r\n            this.Startlist.forEach((item, index) => {\r\n                if (index == 1) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 2) {\r\n                    item.value2 = '';\r\n                    item.value3 = '';\r\n                } else {\r\n                    item.value = '';\r\n                }\r\n            });\r\n            this.getBatchCode();\r\n            this.StartModel = true;\r\n            // this.getEquipmentList();\r\n        },\r\n        async WCSSave() {\r\n            let flag = this.WCSinputlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let obj = {};\r\n            this.WCSinputlist.forEach(item => {\r\n                obj[item.id] = item.value;\r\n            });\r\n            obj.actionType = 3;\r\n            obj.productionOrderId = this.detailobj.ID;\r\n            let res = await toSendOrderInfoToSS(obj);\r\n            this.getPageList();\r\n            this.WCSModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        // async getEquipmentList() {\r\n        //     let id = JSON.stringify(this.checkRow.ID);\r\n        //     let res = await MyGetEquipmentsByOrderId(id);\r\n        //     res.response.forEach(item => {\r\n        //         item.key = item.key + '|' + item.value;\r\n        //     });\r\n        //     this.Startlist[0].options = res.response;\r\n        //     this.StartModel = true;\r\n        // },\r\n        GetData() {\r\n            if (this.Startlist[1].value != '') {\r\n                this.getBatchCode();\r\n            }\r\n        },\r\n        ScanOpen(row) {\r\n            this.SSCCValue = '';\r\n            this.SSCCModel = true;\r\n            this.GetSegmentBatchList(row);\r\n        },\r\n        async SearchSscc(row) {\r\n            if (row) {\r\n                this.SSCCValue = row.Sscc;\r\n            }\r\n            if (this.SSCCValue === '' || this.SSCCValue === null) {\r\n                Message({\r\n                    message: `${this.$t('ConsumptionHistory.NonSSCC')}`,\r\n                    type: 'error'\r\n                });\r\n                return;\r\n            }\r\n            if (this.BatchId === '' || this.BatchId === null) {\r\n                Message({\r\n                    message: `${this.$t('ConsumptionHistory.NoBatchId')}`,\r\n                    type: 'error'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                Sscc: this.SSCCValue\r\n            };\r\n            let res = await ConsumeScanSSCC(params);\r\n            let data = res.response;\r\n            this.Id = data.ID;\r\n            if (data.Location2) {\r\n                data.Location = data.Location1Code + '-' + data.Location2;\r\n            }\r\n            if (data.MaterialCode) {\r\n                data.Material = data.MaterialName + '-' + data.MaterialCode;\r\n            }\r\n            if (data.Quantity) {\r\n                data.QuantitywithUnit = data.Quantity + ' ' + data.Unit1;\r\n            }\r\n            this.ConsumeObj = data;\r\n            for (let k in data) {\r\n                this.Consumelist.forEach(item => {\r\n                    if (item.id == k) {\r\n                        item.value = data[k];\r\n                    }\r\n                });\r\n            }\r\n            let v = this.ConsumeObj.ChangeUnit.toLowerCase() === 'g' ? 1000 : 1;\r\n            this.Consumeinputlist[0].value = data.Quantity * v;\r\n            //this.Consumeinputlist[0].value = data.Quantity;\r\n\r\n            await this.GetConsumeViewEntity(data.ID);\r\n\r\n            this.SSCCModel = false;\r\n            this.ConsumeModel = true;\r\n        },\r\n        async GetConsumeViewEntity(id) {\r\n            let res = await getConsumeViewEntity(id);\r\n            if (res) {\r\n                var obj = res.response;\r\n                if (obj.MaterialName) {\r\n                    obj.Material = obj.MaterialName + '-' + obj.MaterialCode;\r\n                }\r\n                if (obj.Quantity2) {\r\n                    obj.Remaining = (Number(obj.Quantity2) - Number(obj.Quantity1)).toFixed(2);\r\n                }\r\n                this.detailobj = obj;\r\n                for (let k in obj) {\r\n                    this.detailList.forEach(item => {\r\n                        if (item.id == k) {\r\n                            item.value = obj[k];\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        async ConsumeSave() {\r\n            let v = this.ConsumeObj.ChangeUnit.toLowerCase() === 'g' ? 1000 : 1;\r\n            if (this.Consumeinputlist[0].value == 0) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return false;\r\n            }\r\n            if (Number(this.Consumeinputlist[0].value) > Number(this.ConsumeObj.Quantity * v)) {\r\n                Message({\r\n                    message: `${this.$t('Consume.Over')}`,\r\n                    type: 'warning'\r\n                });\r\n                return false;\r\n            }\r\n            if (Number(this.Consumeinputlist[0].value) > Number(this.detailList[4].value)) {\r\n                Message({\r\n                    message: `${this.$t('Consume.Over2')}`,\r\n                    type: 'warning'\r\n                });\r\n            }\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                Sscc: this.SSCCValue,\r\n                Quantity: this.Consumeinputlist[0].value,\r\n                IsNotCheckRunOrder: true\r\n            };\r\n            let res = await ConsumeViewSave(params);\r\n            this.BatchId = '';\r\n            this.SSCCValue = '';\r\n            this.ConsumeModel = false;\r\n            this.Consumeinputlist[0].value = '';\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        async GetSegmentBatchList(row) {\r\n            this.BatchId = '';\r\n            this.SegmentBatchList = [];\r\n            let p = {\r\n                ProductionOrderId: row.ID\r\n            };\r\n            let res = await GetSegmentBatchList(p);\r\n            this.SegmentBatchList = res.response;\r\n        },\r\n        async getQrCode() {\r\n            let code = this.Startlist[2].value + this.Startlist[2].value2 + this.Startlist[2].value3;\r\n            let res = await MyGetQrCode('', this.checkRow.ID, code);\r\n            this.Startlist[3].value = res.response;\r\n        },\r\n        async SendColos() {\r\n            let flag = this.Startlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                poId: this.checkRow.ID,\r\n                batchCode: this.Startlist[2].value + this.Startlist[2].value2 + this.Startlist[2].value3,\r\n                count: this.Startlist[3].value\r\n            };\r\n            if (params.batchCode.length > 10) {\r\n                Message({\r\n                    message: `${this.$t('Overview.BatchCodeLong')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let res = await MySendLabelPrintToColos(params);\r\n            this.getPageList();\r\n            this.StartModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        async getBatchCode() {\r\n            let LineCode = this.checkRow.LineCode;\r\n            let productionId = this.checkRow.ID;\r\n            let date = moment(this.Startlist[1].value).format('YYYY-MM-DD HH:mm:ss');\r\n            let p = {\r\n                LineCode: this.Startlist[2].value,\r\n                equipmentCode: LineCode,\r\n                productionDate: date,\r\n                productionId: productionId\r\n            };\r\n            let res = await GetBatchCode(p);\r\n            if (res.response == null) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'warning'\r\n                });\r\n            } else {\r\n                this.Startlist[2].value = res.response.substring(0, 2);\r\n                this.Startlist[2].value2 = res.response.substring(2, 5);\r\n            }\r\n        },\r\n        async GetShiftSelect() {\r\n            let res = await GetProductionShiftSelect();\r\n            this.ShiftList = res.response;\r\n        },\r\n        handleSelectionChange(val) {\r\n            this.selectTabelData = val;\r\n            this.tablechooselist = val.length;\r\n        },\r\n        addNew() {\r\n            this.ShiftID = '';\r\n            this.ShiftListModal = true;\r\n        },\r\n        async ShiftSave() {\r\n            if (this.ShiftID == '') {\r\n                Message({\r\n                    message: this.$t('POList.CHECKMaterialPreShift'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let arr = this.selectTabelData.map(item => {\r\n                return item.ID;\r\n            });\r\n            let params = {\r\n                ShiftID: this.ShiftID,\r\n                ProIDS: arr\r\n            };\r\n            let res = await GetProductionUpdateShift(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.ShiftListModal = false;\r\n            this.getPageList();\r\n        },\r\n        async toRebuildBatch(isPl) {\r\n            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_GJPC')}`, '', {\r\n                confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                type: 'warning'\r\n            }).then(async () => {\r\n                let arr = [];\r\n                if (isPl) {\r\n                    arr = this.selectTabelData.map(item => {\r\n                        return item.ID;\r\n                    });\r\n                } else {\r\n                    arr = [this.detailobj.ID];\r\n                }\r\n                let res = await RebuildBatch(arr);\r\n                this.detailShow = false;\r\n                this.getPageList();\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            });\r\n        },\r\n        async toBindPoRecipe(isPl) {\r\n            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_BDPF')}`, '', {\r\n                confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                type: 'warning'\r\n            }).then(async () => {\r\n                let arr = [];\r\n                if (isPl) {\r\n                    arr = this.selectTabelData.map(item => {\r\n                        return item.ID;\r\n                    });\r\n                } else {\r\n                    arr = [this.detailobj.ID];\r\n                }\r\n                let res = await BindPoRecipe(arr);\r\n                this.detailShow = false;\r\n                this.getPageList();\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            });\r\n        },\r\n        async toRelease() {\r\n            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_SF')}`, '', {\r\n                confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                type: 'warning'\r\n            }).then(async () => {\r\n                let obj = {\r\n                    ID: this.detailobj.ID\r\n                };\r\n                if (this.detailobj.NeedQARelease == '0') {\r\n                    let parmas = {\r\n                        key: '2',\r\n                        body: JSON.stringify(obj)\r\n                    };\r\n                    let res = await OperationPo(parmas);\r\n                    this.getPageList();\r\n                    this.$refs[this.activeName].getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);\r\n                    Message({\r\n                        message: res.msg,\r\n                        type: 'success'\r\n                    });\r\n                } else {\r\n                    let parmas = {\r\n                        key: '5',\r\n                        body: JSON.stringify(obj)\r\n                    };\r\n                    let res = await OperationPo(parmas);\r\n                    this.getPageList();\r\n                    this.$refs[this.activeName].getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);\r\n                    Message({\r\n                        message: res.msg,\r\n                        type: 'success'\r\n                    });\r\n                }\r\n            });\r\n        },\r\n        async getData2(item) {\r\n            if (item.id == 'ProduceStatus') {\r\n                if (item.value != '') {\r\n                    let res = await this.$getNewDataDictionary(item.value);\r\n                    //let res = this.ReasonList.find(x=>x.ItemCode = item.value)\r\n                    // console.log(res);\r\n                    let data = res;\r\n                    if (data.length > 0) {\r\n                        data.forEach(item1 => {\r\n                            item1.key = item1.ItemValue;\r\n                            // console.log(this._i18n.locale);\r\n                            item1.label = this._i18n.locale === 'en' ? item1.ItemValue : item1.ItemName;\r\n                        });\r\n                    }\r\n                    this.Completelist[4].options = data;\r\n                    console.log(this.Completelist[4].options);\r\n                    this.Completelist[4].value = '';\r\n                }\r\n            }\r\n        },\r\n        async CompleteSave() {\r\n            let flag = this.Completelist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '' || item.value == null;\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                Id: this.detailobj.ID,\r\n                Status: 3,\r\n                ProduceStatus: this.Completelist[3].value,\r\n                Reason: this.Completelist[4].value\r\n            };\r\n            let res = await UpdatePoStatus(params);\r\n            this.detailShow = false;\r\n            this.CompleteModel = false;\r\n            this.getPageList();\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        toComplete() {\r\n            this.detailobj.Material = this.detailobj.MaterialDescription + '-' + this.detailobj.MaterialCode;\r\n            this.detailobj.PlanQuantity = this.detailobj.PlanQty + this.detailobj.Unit;\r\n            this.detailobj.ActualQuantity = this.detailobj.ActualQty + this.detailobj.Unit;\r\n            this.Completelist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            //console.log(this.Completelist[4]);\r\n            this.Completelist.forEach(item => {\r\n                for (let k in this.detailobj) {\r\n                    if (item.id == k) {\r\n                        item.value = this.detailobj[k];\r\n                    }\r\n                }\r\n            });\r\n            this.Completelist[4].options = [];\r\n            let PlanQuantity = Number(this.detailobj.PlanQty);\r\n            let ActualQuantity = Number(this.detailobj.ActualQty);\r\n            if (PlanQuantity > ActualQuantity) {\r\n                this.Completelist[3].value = 'NotComplete';\r\n            } else if (PlanQuantity == ActualQuantity) {\r\n                this.Completelist[3].value = 'CompleteAtOnce';\r\n            } else {\r\n                this.Completelist[3].value = 'OverComplete';\r\n            }\r\n            //console.log(this.ReasonList);\r\n            this.ReasonList.forEach(x => {\r\n                if (x.ItemCode == this.Completelist[3].value) {\r\n                    let res = this.Completelist[4].options.find(x1 => x1.ItemCode === this.Completelist[3].value && x1.ItemValue === x.ItemValue);\r\n                    if (res === null || res === undefined || typeof res === 'undefined') {\r\n                        x.key = x.ItemValue;\r\n                        x.label = this._i18n.locale === 'en' ? x.ItemValue : x.ItemName;\r\n                        this.Completelist[4].options.push(x);\r\n                    }\r\n                }\r\n            });\r\n            console.log(this.Completelist[4].options);\r\n            this.CompleteModel = true;\r\n        },\r\n        editShow() {\r\n            this.EditModel = true;\r\n        },\r\n        tabChange() {\r\n            setTimeout(() => {\r\n                this.$refs[this.activeName].getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);\r\n            }, 200);\r\n        },\r\n        async detaildrawShow(obj) {\r\n            this.activeName = 'Execute';\r\n            this.detailobj = obj;\r\n            let res = await GetLastProcessData('', this.detailobj.ID);\r\n            this.detailShow = true;\r\n            this.detailobj.Material = this.detailobj.MaterialDescription + '-' + this.detailobj.MaterialCode;\r\n            setTimeout(() => {\r\n                this.$refs.execute.getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);\r\n            }, 200);\r\n            for (let k in this.detailobj) {\r\n                this.Editlist.forEach(item => {\r\n                    if (item.id == k) {\r\n                        item.value = this.detailobj[k];\r\n                    }\r\n                });\r\n            }\r\n            if (res.response.IsReminded == '0') {\r\n                MessageBox.confirm(`${this.$t('POList.warningText')}`, '', {\r\n                    confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                    cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                    closeOnClickModal: false,\r\n                    type: 'warning'\r\n                })\r\n                    .then(async () => {\r\n                        let params = {\r\n                            IsReminded: '1',\r\n                            ID: res.response.ID\r\n                        };\r\n                        let res2 = await UpdateMaterialProcessDataStatus(params);\r\n                        Message({\r\n                            message: res2.msg,\r\n                            type: 'success'\r\n                        });\r\n                    })\r\n                    .catch(async () => {});\r\n            }\r\n        },\r\n        getDay(day) {\r\n            var today = new Date();\r\n            var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;\r\n            today.setTime(targetday_milliseconds); //注意，这行是关键代码\r\n            var tYear = today.getFullYear();\r\n            var tMonth = today.getMonth();\r\n            var tDate = today.getDate();\r\n            tMonth = this.doHandleMonth(tMonth + 1);\r\n            tDate = this.doHandleMonth(tDate);\r\n            return tYear + '-' + tMonth + '-' + tDate;\r\n        },\r\n        doHandleMonth(month) {\r\n            var m = month;\r\n            if (month.toString().length == 1) {\r\n                m = '0' + month;\r\n            }\r\n            return m;\r\n        },\r\n        getStatusName(key) {\r\n            if (key) {\r\n                let name = '';\r\n                this.StatusList.forEach(item => {\r\n                    if (item.ItemValue == key) {\r\n                        name = item.ItemName;\r\n                    }\r\n                });\r\n                return name;\r\n            }\r\n        },\r\n        getReasonName(key1, key2) {\r\n            if (key1 === null || key1 === '' || key2 === null || key2 === '') {\r\n                return '';\r\n            }\r\n            let name = key2;\r\n            //console.log(this._i18n.locale);\r\n            if (this._i18n.locale == 'en') {\r\n                return name;\r\n            }\r\n            this.ReasonList.forEach(item => {\r\n                if (item.ItemCode === key1 && item.ItemValue === key2) {\r\n                    name = item.ItemName;\r\n                }\r\n            });\r\n            //console.log(name);\r\n            return name;\r\n        },\r\n        getStatusColor(key) {\r\n            if (key) {\r\n                let color = '';\r\n                this.StatusList.forEach(item => {\r\n                    if (item.ItemValue == key) {\r\n                        color = item.Description;\r\n                    }\r\n                });\r\n                return color;\r\n            }\r\n        },\r\n\r\n        getsearch() {\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.getPageList();\r\n        },\r\n        getempty() {\r\n            this.QuickSearch = '';\r\n            this.timepicker = [];\r\n            this.timepicker[0] = this.getDay(0);\r\n            this.timepicker[1] = this.getDay(3);\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.searchlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            this.getPageList();\r\n        },\r\n        async getStatus() {\r\n            let params = {\r\n                ItemCode: 'ProductionOrderStatus'\r\n            };\r\n            const res = await GetDataTreeList(params);\r\n            let data = res.response.data;\r\n            this.StatusList = data;\r\n            data.forEach(item => {\r\n                item.label = item.ItemName;\r\n                item.value = item.ItemValue;\r\n            });\r\n\r\n            this.ReasonList = [];\r\n            this.Completelist[3].options.forEach(async item => {\r\n                let p = {\r\n                    ItemCode: item.key\r\n                };\r\n                const res = await GetDataTreeList(p);\r\n                let data = res.response.data;\r\n                //let res = await this.$getNewDataDictionary(item.key);\r\n                console.log(res.response.data);\r\n                res.response.data.forEach(item1 => {\r\n                    this.ReasonList.push(item1);\r\n                });\r\n            });\r\n\r\n            this.Editinputlist[0].options = data;\r\n            this.searchlist[0].option = data;\r\n            this.getPageList();\r\n        },\r\n        async getPageList() {\r\n            if (this.timepicker == null) {\r\n                this.timepicker = [];\r\n            }\r\n            let params = {\r\n                NeedQARelease: '0',\r\n                Resource: '',\r\n                MaterialDescription: '',\r\n                ProductionOrderNo: '',\r\n                StatusList: this.searchlist[0].value,\r\n                StartTime: this.timepicker[0],\r\n                EndTime: this.timepicker[1] == undefined ? '' : this.timepicker[1] + ' 23:59:59',\r\n                FillLineCode: '',\r\n                Key: this.QuickSearch,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize,\r\n                orderByFileds: '',\r\n                Formula: '',\r\n                SegmentCode: ''\r\n            };\r\n            for (let k in params) {\r\n                this.searchlist.forEach(item => {\r\n                    if (k == item.id) {\r\n                        params[k] = item.value;\r\n                    }\r\n                });\r\n            }\r\n            const res = await GetListViewList(params);\r\n            if (res) {\r\n                this.tableList = res.response.data;\r\n            }\r\n            // if (res.response.count == 0) {\r\n            //     this.tableList = [];\r\n            // } else {\r\n            //     this.tableList = res.response.data;\r\n            // }\r\n            this.pageOptions.total = res.response.dataCount;\r\n        },\r\n        handleSizeChange(val) {\r\n            this.pageOptions.pageSize = val;\r\n            this.getPageList();\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.pageOptions.page = val;\r\n            this.getPageList();\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scope>\r\n.PoList {\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin-bottom: 10px;\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 50%;\r\n            text-align: right;\r\n        }\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n    .splitdetailbox {\r\n        padding: 10px 0;\r\n        border: 1px solid #e8e8e8;\r\n        margin-bottom: 5px;\r\n        .dialogdetailbox {\r\n            display: flex;\r\n            align-items: center;\r\n            width: 100%;\r\n            margin-top: 10px;\r\n            .dialogdetailsinglelabel {\r\n                font-weight: 600;\r\n                width: 47%;\r\n                text-align: right;\r\n            }\r\n            .dialogdetailsinglevalue {\r\n                width: 78%;\r\n                margin-left: 20px;\r\n            }\r\n        }\r\n    }\r\n    .drawerTitlelabel {\r\n        color: #808080;\r\n        font-size: 1rem;\r\n        .statusbox {\r\n            width: auto !important;\r\n        }\r\n    }\r\n    .dialog-title {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n    }\r\n    .drawEditBox {\r\n        font-size: 1.5rem;\r\n        margin-right: 15px;\r\n        cursor: pointer;\r\n    }\r\n}\r\n.el-dialog__body {\r\n    .el-input {\r\n        width: 250px !important;\r\n    }\r\n    .el-select {\r\n        width: 250px !important;\r\n    }\r\n    .el-textarea {\r\n        width: 250px !important;\r\n    }\r\n    .longwidthinput {\r\n        .el-input {\r\n            width: 400px !important;\r\n        }\r\n        .el-select {\r\n            width: 400px !important;\r\n        }\r\n        .el-textarea {\r\n            width: 400px !important;\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}