{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\form-dialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\form-dialog.vue", "mtime": 1750217195812}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgaW1wb3J0IHsKICAgIGdldFNvcFBlcm1pc3Npb25EZXRhaWwsCiAgICBzYXZlU29wUGVybWlzc2lvbkZvcm0KICB9IGZyb20gIkAvYXBpL1NPUC9zb3BQZXJtaXNzaW9uIjsKCiAgZXhwb3J0IGRlZmF1bHQgewogICAgY29tcG9uZW50czp7CiAgICAgIAogICAgfSwKICAgIGRhdGEoKSB7CiAgICAgIHJldHVybiB7CiAgICAgICAgZGlhbG9nRm9ybToge30sCiAgICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgICAgZm9ybUxvYWRpbmc6IGZhbHNlLAogICAgICAgIGxpbmVPcHRpb25zOiBbXSwKICAgICAgICB0YXJnZXRMaW5lT3B0aW9uczogW10sCiAgICAgICAgY3VycmVudFJvdzoge30sCiAgICAgICAgbWF0SW5mbzp7fQogICAgICB9CiAgICB9LAogICAgbW91bnRlZCgpIHsKICAgIH0sCiAgICBtZXRob2RzOiB7CiAgICAgIHN1Ym1pdCgpIHsKICAgICAgICBzYXZlU29wUGVybWlzc2lvbkZvcm0odGhpcy5kaWFsb2dGb3JtKS50aGVuKHJlcz0+ewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKHJlcy5tc2cpCiAgICAgICAgICB0aGlzLiRlbWl0KCdzYXZlRm9ybScpCiAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQogICAgICAgIH0pCiAgICAgIH0sCiAgICAgIHNob3coZGF0YSkgewogICAgICAgIHRoaXMuZGlhbG9nRm9ybSA9IHt9CiAgICAgICAgdGhpcy5jdXJyZW50Um93ID0gZGF0YQogICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsKICAgICAgICAgIGlmKGRhdGEuSUQpewogICAgICAgICAgICB0aGlzLmdldERpYWxvZ0RldGFpbChkYXRhLklEKQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0sCiAgICAgIGdldERpYWxvZ0RldGFpbChpZCl7CiAgICAgICAgZ2V0U29wUGVybWlzc2lvbkRldGFpbChpZCkudGhlbihyZXMgPT4gewogICAgICAgICAgdGhpcy5kaWFsb2dGb3JtID0gcmVzLnJlc3BvbnNlCiAgICAgICAgfSkKICAgICAgfSwKICAgIH0KICB9CiAg"}, {"version": 3, "sources": ["form-dialog.vue"], "names": [], "mappings": ";AAqFA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "form-dialog.vue", "sourceRoot": "src/views/SOP/sopPermission", "sourcesContent": ["<template>\n    <el-dialog :title=\"dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')\" :visible.sync=\"dialogVisible\" width=\"700px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n       \n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"主键\">{{dialogForm.id}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item :label=\"$t('SOP.TargetId')\" prop=\"targetId\">\n              <el-select v-model=\"dialogForm.targetId\" :placeholder=\"$t('SOP.SelectTargetId')\">\n                <el-option v-for=\"item in  targetIdOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item :label=\"$t('SOP.TargetType')\" prop=\"targetType\">\n              <el-select v-model=\"dialogForm.targetType\" :placeholder=\"$t('SOP.SelectTargetType')\">\n                <el-option v-for=\"item in  targetTypeOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item :label=\"$t('SOP.GrantType')\" prop=\"grantType\">\n              <el-select v-model=\"dialogForm.grantType\" :placeholder=\"$t('SOP.SelectGrantType')\">\n                <el-option v-for=\"item in  grantTypeOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item :label=\"$t('SOP.GrantId')\" prop=\"grantId\">\n              <el-select v-model=\"dialogForm.grantId\" :placeholder=\"$t('SOP.SelectGrantId')\">\n                <el-option v-for=\"item in  grantIdOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"权限级别(1-预览 2-下载 4-上传 8-删除)\" prop=\"permLevel\">\n              <el-input v-model=\"dialogForm.permLevel\" placeholder=\"请输入权限级别(1-预览 2-下载 4-上传 8-删除)\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"创建时间\">{{dialogForm.createdate}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"创建人ID\">{{dialogForm.createuserid}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"修改时间\">{{dialogForm.modifydate}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"修改人ID\">{{dialogForm.modifyuserid}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"时间戳\">{{dialogForm.updatetimestamp}}</el-form-item>\n          </el-col>\n    \n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"删除标记(0-未删 1-已删)\">{{dialogForm.deleted}}</el-form-item>\n          </el-col>\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\"\n          @click=\"submit()\">确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </template>\n  \n\n<script>\n  import {\n    getSopPermissionDetail,\n    saveSopPermissionForm\n  } from \"@/api/SOP/sopPermission\";\n\n  export default {\n    components:{\n      \n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        lineOptions: [],\n        targetLineOptions: [],\n        currentRow: {},\n        matInfo:{}\n      }\n    },\n    mounted() {\n    },\n    methods: {\n      submit() {\n        saveSopPermissionForm(this.dialogForm).then(res=>{\n          this.$message.success(res.msg)\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        })\n      },\n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.$nextTick(_ => {\n          if(data.ID){\n            this.getDialogDetail(data.ID)\n          }\n        })\n      },\n      getDialogDetail(id){\n        getSopPermissionDetail(id).then(res => {\n          this.dialogForm = res.response\n        })\n      },\n    }\n  }\n  </script>"]}]}