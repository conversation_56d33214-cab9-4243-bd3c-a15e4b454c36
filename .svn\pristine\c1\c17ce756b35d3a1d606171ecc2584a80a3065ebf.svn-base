<template>
    <el-dialog :title="dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')" :visible.sync="dialogVisible" width="700px"
      :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
      @close="dialogVisible = false">
      <el-form ref="dialogForm" :model="dialogForm" label-width="130px">
       

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="主键">{{dialogForm.id}}</el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item :label="$t('SOP.TargetId')" prop="targetId">
              <el-select v-model="dialogForm.targetId" :placeholder="$t('SOP.SelectTargetId')">
                <el-option v-for="item in  targetIdOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item :label="$t('SOP.TargetType')" prop="targetType">
              <el-select v-model="dialogForm.targetType" :placeholder="$t('SOP.SelectTargetType')">
                <el-option v-for="item in  targetTypeOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item :label="$t('SOP.GrantType')" prop="grantType">
              <el-select v-model="dialogForm.grantType" :placeholder="$t('SOP.SelectGrantType')">
                <el-option v-for="item in  grantTypeOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item :label="$t('SOP.GrantId')" prop="grantId">
              <el-select v-model="dialogForm.grantId" :placeholder="$t('SOP.SelectGrantId')">
                <el-option v-for="item in  grantIdOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="权限级别(1-预览 2-下载 4-上传 8-删除)" prop="permLevel">
              <el-input v-model="dialogForm.permLevel" placeholder="请输入权限级别(1-预览 2-下载 4-上传 8-删除)" />
            </el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="创建时间">{{dialogForm.createdate}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="创建人ID">{{dialogForm.createuserid}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="修改时间">{{dialogForm.modifydate}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="修改人ID">{{dialogForm.modifyuserid}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="时间戳">{{dialogForm.updatetimestamp}}</el-form-item>
          </el-col>
    
          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="删除标记(0-未删 1-已删)">{{dialogForm.deleted}}</el-form-item>
          </el-col>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small"
          @click="submit()">确定
        </el-button>
      </div>
    </el-dialog>
  </template>
  

<script>
  import {
    getSopPermissionDetail,
    saveSopPermissionForm
  } from "@/api/SOP/sopPermission";

  export default {
    components:{
      
    },
    data() {
      return {
        dialogForm: {},
        dialogVisible: false,
        formLoading: false,
        lineOptions: [],
        targetLineOptions: [],
        currentRow: {},
        matInfo:{}
      }
    },
    mounted() {
    },
    methods: {
      submit() {
        saveSopPermissionForm(this.dialogForm).then(res=>{
          this.$message.success(res.msg)
          this.$emit('saveForm')
          this.dialogVisible = false
        })
      },
      show(data) {
        this.dialogForm = {}
        this.currentRow = data
        this.dialogVisible = true
        this.$nextTick(_ => {
          if(data.ID){
            this.getDialogDetail(data.ID)
          }
        })
      },
      getDialogDetail(id){
        getSopPermissionDetail(id).then(res => {
          this.dialogForm = res.response
        })
      },
    }
  }
  </script>