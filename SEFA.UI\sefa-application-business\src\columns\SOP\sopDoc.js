import i18n from '@/plugins/i18n';

export const sopDocColumns = [
    { text: () => i18n.t('SOP.DocName'), value: 'DocName', width: '200px' },
    { text: () => i18n.t('SOP.DocCode'), value: 'DocCode', width: '160px' },
    { text: () => i18n.t('SOP.DocVersion'), value: 'DocVersion', width: '120px' },
    { text: () => i18n.t('SOP.FilePath'), value: 'FilePath', width: '200px' },
    { text: () => i18n.t('SOP.FileSize'), value: 'FileSize', width: '120px' },
    { text: () => i18n.t('SOP.DocStatus'), value: 'DocStatus', width: '100px' }
];