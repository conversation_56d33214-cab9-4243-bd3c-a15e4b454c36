{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\WLPOlist\\index.vue?vue&type=style&index=0&id=012cfe67&lang=scss&scope=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\WLPOlist\\index.vue", "mtime": 1750254216308}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA2rDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/Producting/WLPOlist", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle PoList\">\r\n        <div class=\"InventorySearchBox\">\r\n            <div class=\"searchbox\">\r\n                <div class=\"datebox\">\r\n                    <div class=\"datepickbox\">\r\n                        <el-date-picker\r\n                            v-model=\"timepicker\"\r\n                            type=\"daterange\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            range-separator=\"-\"\r\n                            :start-placeholder=\"$t('DFM_RL._KSRQ')\"\r\n                            :end-placeholder=\"$t('DFM_RL._JSRQ')\"\r\n                        ></el-date-picker>\r\n                    </div>\r\n                </div>\r\n                <div class=\"inputformbox\" v-for=\"(item, index) in searchlist\" :key=\"index\">\r\n                    <v-text-field class=\"vueinput\" type=\"text\" v-if=\"item.type == 'input'\" v-model.trim=\"item.value\" dense outlined :label=\"item.name\" :placeholder=\"item.name\" />\r\n                    <el-select multiple filterable clearable v-model=\"item.value\" v-if=\"item.type == 'select'\" :myid=\"item.id\" :placeholder=\"item.name\">\r\n                        <el-option v-for=\"(it, ind) in item.option\" :key=\"ind\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                    </el-select>\r\n                    <el-checkbox v-if=\"item.type == 'checkbox'\" :myid=\"item.id\" v-model=\"item.value\">{{ item.name }}</el-checkbox>\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <el-input class=\"quickSearchinput\" :placeholder=\"$t('BatchPallets.QuickSearch')\" v-model=\"QuickSearch\">\r\n                    <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n                </el-input>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <el-button size=\"small\" style=\"margin-left: 5px\" icon=\"el-icon-s-help\" @click=\"getempty()\">{{ this.$t('GLOBAL._CZ') }}</el-button>\r\n                <el-button\r\n                    class=\"tablebtn\"\r\n                    v-has=\"'PRO_PREP_SHIFT'\"\r\n                    size=\"small\"\r\n                    :disabled=\"tablechooselist > 0 ? false : true\"\r\n                    style=\"width: 160px; margin-left: 5px\"\r\n                    icon=\"el-icon-plus\"\r\n                    @click=\"addNew()\"\r\n                >\r\n                    {{ this.$t('POList.AddMaterialPreShift') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" size=\"small\" :disabled=\"tablechooselist > 0 ? false : true\" style=\"width: 160px; margin-left: 5px\" icon=\"el-icon-plus\" @click=\"toRebuildBatch(true)\">\r\n                    {{ this.$t('POList.constructingbatches') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                </el-button>\r\n                <!-- <el-button class=\"tablebtn\" size=\"small\" :disabled=\"tablechooselist > 0 ? false : true\" style=\"width: 160px; margin-left: 5px\" icon=\"el-icon-plus\" @click=\"toBindPoRecipe(true)\">\r\n                    {{ this.$t('POList.BindPoRecipe') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                </el-button> -->\r\n                <!-- <el-button class=\"tablebtn\" size=\"small\" style=\"width: 160px; margin-left: 5px\" icon=\"el-icon-plus\" @click=\"ToThroatOutput()\">\r\n                    {{ this.$t('POList.ThroatOutput') }}\r\n                </el-button> -->\r\n            </div>\r\n        </div>\r\n        <div class=\"tablebox\">\r\n            <el-table border :data=\"tableList\" @selection-change=\"handleSelectionChange\" style=\"width: 100%\" height=\"700\">\r\n                <el-table-column type=\"selection\" width=\"55\" fixed=\"left\" :selectable=\"checkSelectable\"></el-table-column>\r\n                <el-table-column\r\n                    v-for=\"(item, index) in tableheader\"\r\n                    :fixed=\"item.fixed ? item.fixed : false\"\r\n                    :key=\"index\"\r\n                    sortable\r\n                    :align=\"item.align\"\r\n                    :prop=\"item.prop ? item.prop : item.value\"\r\n                    :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                    :width=\"item.width\"\r\n                >\r\n                    <template v-slot:header=\"scope\">\r\n                        <span v-if=\"item.icon\">\r\n                            <i :class=\"item.icon\"></i>\r\n                        </span>\r\n                        <span v-if=\"!item.icon\">{{ scope.column.label }}</span>\r\n                    </template>\r\n                    <template slot-scope=\"scope\">\r\n                        <i class=\"el-icon-document\" v-if=\"scope.column.property == 'detail'\" @click=\"detaildrawShow(scope.row)\"></i>\r\n                        <span v-else>\r\n                            <span v-if=\"scope.column.property == 'PlanStartTime'\">{{ $dayjs(scope.row.PlanStartTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'PlanEndTime'\">{{ $dayjs(scope.row.PlanEndTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                            <!-- <span v-else-if=\"scope.column.property == 'SapDate'\">{{ scope.row.SapDate == null ? '' : $dayjs(scope.row.SapDate).format('YYYY-MM-DD') }}</span> -->\r\n                          \r\n                            <span v-else-if=\"scope.column.property == 'Sequence'\">\r\n                                <div style=\"color: #808080;font-weight: 900;font-size: larger\">{{ scope.row.Sequence }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'PoStatus'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: `${getStatusColor(scope.row.PoStatus)}` }\">\r\n                                    {{ getStatusName(scope.row.PoStatus) }}\r\n                                </div>\r\n                            </span>\r\n                            <!-- <span style=\"display: flex\" v-else-if=\"scope.column.property == 'Execute'\">\r\n                                <div class=\"statusbox Execute1\">\r\n                                    {{ scope.row.RunningCount }}\r\n                                </div>\r\n                                <div class=\"statusbox Execute2\">\r\n                                    {{ scope.row.StopedCount }}\r\n                                </div>\r\n                                <div class=\"statusbox Execute3\">\r\n                                    {{ scope.row.ExecutionCount }}\r\n                                </div>\r\n                            </span> -->\r\n                            <!-- <span v-else-if=\"scope.column.property == 'Bezei'\">\r\n                                <div v-for=\"(each, index1) in scope.row[scope.column.property].split(';')\" :key=\"index1\" class=\"text-left\">{{ each }}</div>\r\n                            </span> -->\r\n                            <!-- <span v-else-if=\"scope.column.property == 'Count'\">\r\n                                {{ scope.row.Sequence + '/' + scope.row.Count }}\r\n                            </span> -->\r\n                            <!-- <span v-else-if=\"scope.column.property == 'IsHavePreservative'\">\r\n                                <i :class=\"scope.row[item.value] === '1' ? 'el-icon-star-on' : ''\"></i>\r\n                            </span> -->\r\n                            <span v-else-if=\"scope.column.property == 'QaStatus'\">\r\n                                <!-- <div class=\"statusbox\" :style=\"{ background: `${(scope.row.QaStatus=='通过'?'green':'yellow')}`}\">\r\n                                    {{ scope.row.QaStatus }}\r\n                                </div> -->\r\n                                <div class=\"qAstatusbox\" :style=\"{ background: scope.row.QaStatus === '通过' ? '#3DCD58' : '#FFA500' }\">{{ scope.row.QaStatus }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'operate'\" style=\"display: flex; justify-content: space-evenly\">\r\n                                <el-button class=\"operatebtn\" size=\"mini\" style=\"width: 70px\" icon=\"el-icon-full-screen\" @click=\"ScanOpen(scope.row)\">\r\n                                    {{ $t('Consume.Scan') }}\r\n                                </el-button>\r\n                                <el-button class=\"operatebtn\" size=\"mini\" style=\"width: 70px\" icon=\"el-icon-printer\" @click=\"toColos(scope.row)\">\r\n                                    {{ $t('Inventory.Print') }}\r\n                                </el-button>\r\n                                <el-button\r\n                                    class=\"operatebtn\"\r\n                                    size=\"mini\"\r\n                                    style=\"width: 70px\"\r\n                                    :disabled=\"scope.row.ExecutionId == '' || scope.row.ExecutionId == null\"\r\n                                    icon=\"el-icon-video-play\"\r\n                                    @click=\"ProduceOpen(scope.row)\"\r\n                                >\r\n                                    {{ $t('Consume.Produce') }}\r\n                                </el-button>\r\n                                <!-- <i class=\"el-icon-zoom-in\" @click=\"ScanOpen(scope.row)\" style=\"margin-right: 12px\"></i>\r\n                                <i class=\"el-icon-printer\" @click=\"toColos(scope.row)\"></i> -->\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'ActualQty'\">{{ scope.row[item.value] }}{{ scope.row.Unit }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'PlanQty'\">{{ scope.row[item.value] }}{{ scope.row.Unit }}</span>\r\n                            <!-- <span v-else-if=\"scope.column.property == 'ActualQty'\">{{ scope.row[item.value] }}{{ scope.row.Unit }}</span> -->\r\n                            <span v-else-if=\"scope.column.property == 'ProduceStatus'\">{{ scope.row[item.value] === null ? '' : $t(`POList.${scope.row[item.value]}`) }}</span>\r\n                            <!-- <span v-else-if=\"scope.column.property == 'Reason'\">{{ getReasonName(scope.row.ProduceStatus, scope.row.Reason) }}</span> -->\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n            <div class=\"paginationbox\">\r\n                <el-pagination\r\n                    @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageOptions.page\"\r\n                    :page-sizes=\"pageOptions.pageSizeitems\"\r\n                    :page-size=\"pageOptions.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next, jumper\"\r\n                    :total=\"pageOptions.total\"\r\n                    background\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Consume.Produce')\" id=\"Producedialog\" :visible.sync=\"ProduceModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Producelist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <span>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Produceinputlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input :disabled=\"item.disable\" onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'inputNumber'\" v-model=\"item.value\">\r\n                            <template slot=\"append\">{{ BtnObj.Unit1 }}</template>\r\n                        </el-input>\r\n                        <el-input :disabled=\"item.disable\" v-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                        <el-select :disabled=\"item.disable\" clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                            <el-option v-for=\"it in item.option\" :key=\"it.value\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                        </el-select>\r\n                        <el-date-picker :disabled=\"item.disable\" v-else-if=\"item.type == 'date'\" @change=\"GetDate(item.id)\" :type=\"item.datetype\" v-model=\"item.value\"></el-date-picker>\r\n                        <el-switch v-else-if=\"item.type == 'switch'\" v-model=\"item.value\" active-color=\"#3dcd58\" inactive-color=\"#ff4949\"></el-switch>\r\n                    </div>\r\n                </div>\r\n                <div class=\"dialogdetailbox\" v-if=\"Produceinputlist[5].value == true\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ $t('Consume.selectprinter') }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-select clearable v-model=\"selectprinter\" filterable>\r\n                            <el-option v-for=\"it in selectprinterOption\" :key=\"it.value\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                        </el-select>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-video-play\" @click=\"SaveProduce()\">\r\n                    {{ $t('Consume.Produce') }}\r\n                </el-button>\r\n                <el-button @click=\"ProduceModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-drawer size=\"80%\" :wrapperClosable=\"false\" :visible.sync=\"detailShow\" direction=\"rtl\">\r\n            <div slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"drawerTitlelabel\">\r\n                    <span style=\"font-size: 1.5rem; color: #494949; margin-right: 5px\">{{ detailobj.ProductionOrderNo }}</span>\r\n                    <span>{{ detailobj.Resource }}</span>\r\n                    |\r\n                    <span>{{ detailobj.MaterialCode }}</span>\r\n                    -\r\n                    <span>{{ detailobj.MaterialDescription }}</span>\r\n                    |\r\n                    <span>{{ detailobj.PlanQty }}</span>\r\n                    |\r\n                    <span style=\"width: 11vh; padding: 4px; margin-right: 0; display: inline-block; text-align: center\" class=\"statusbox\" :style=\"{ background: getStatusColor(detailobj.PoStatus) }\">\r\n                        {{ getStatusName(detailobj.PoStatus) }}\r\n                    </span>\r\n                    |\r\n                    <span style=\"color: #494949\">{{ detailobj.PlanStartTime }}</span>\r\n                    -\r\n                    <span style=\"color: #494949\">{{ detailobj.PlanEndTime }}</span>\r\n                </div>\r\n                <!-- <div class=\"drawEditBox\">\r\n                    <i class=\"el-icon-edit-outline\" @click=\"editShow()\"></i>\r\n                </div> -->\r\n            </div>\r\n            <div class=\"InventorySearchBox\" style=\"margin-bottom: 0px\">\r\n                <!-- <div class=\"InventorySearchBox\" style=\"margin-bottom: 0px\" v-if=\"Number(detailobj.PoStatus) <= 3\"> -->\r\n                <div class=\"searchbox\">\r\n                    <!-- <el-button size=\"small\" v-if=\"detailobj.PoStatus == '0'\" @click=\"toRelease()\" class=\"tablebtn\" style=\"margin-left: 5px\">\r\n                        {{ $t('POList.Release') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toComplete()\" v-if=\"detailobj.PoStatus == '1' || detailobj.PoStatus == '2' || detailobj.PoStatus == '3'\" class=\"tablebtn\" style=\"margin-left: 5px\">\r\n                        {{ $t('POList.Complete') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toRevokeRelease()\" v-if=\"detailobj.PoStatus == '1'\" class=\"tablebtn\" style=\"margin-left: 5px\">\r\n                        {{ $t('POList.RevokeRelease') }}\r\n                    </el-button> -->\r\n\r\n                    <el-button\r\n                        size=\"small\"\r\n                        @click=\"toRelease()\"\r\n                        v-if=\"(detailobj.PoStatus == '1' && detailobj.NeedQARelease == '0') || (detailobj.PoStatus == '7' && detailobj.NeedQARelease == '1')\"\r\n                        class=\"tablebtn\"\r\n                        style=\"margin-left: 5px\"\r\n                    >\r\n                        {{ $t('POList.Release') }}\r\n                    </el-button>\r\n                    <el-button\r\n                        size=\"small\"\r\n                        @click=\"toComplete()\"\r\n                        v-if=\"detailobj.PoStatus == '2' || detailobj.PoStatus == '5' || detailobj.PoStatus == '6'\"\r\n                        class=\"tablebtn\"\r\n                        style=\"margin-left: 5px; width: 170px\"\r\n                    >\r\n                        {{ $t('POList.Complete') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"Reopen()\" v-if=\"detailobj.PoStatus == '3'\" class=\"tablebtn\" style=\"margin-left: 5px\">\r\n                        {{ $t('POList.Reopen') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toWCSUpDate()\" v-if=\"detailobj.SendWcs == '1'\" class=\"tablebtn\" style=\"margin-left: 5px; width: 170px\">\r\n                        {{ $t('POList.WCSUpDate') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toRebuildBatch()\" v-if=\"detailobj.PoStatus == '2'\" class=\"tablebtn\" style=\"margin-left: 5px; width: 170px\">\r\n                        {{ $t('POList.constructingbatches') }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" @click=\"toBindPoRecipe()\" class=\"tablebtn\" style=\"margin-left: 5px; width: 170px\">\r\n                        {{ $t('POList.BindPoRecipe') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n            <el-tabs v-model=\"activeName\" @tab-click=\"tabChange\" type=\"border-card\">\r\n                <el-tab-pane :label=\"this.$t('POList.Execute')\" name=\"Execute\">\r\n                    <execute :ProductionOrderNo=\"detailobj.ID\" ref=\"execute\"></execute>\r\n                </el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.batch')\" name=\"batch\">\r\n                    <batch :ProductionOrderNo=\"detailobj.ID\" ref=\"batch\"></batch>\r\n                </el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.consume')\" name=\"consume\"><consume :ProductionOrderNo=\"detailobj.ID\" ref=\"consume\"></consume></el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.produce')\" name=\"produce\"><produce :ProductionOrderNo=\"detailobj.ID\" ref=\"produce\"></produce></el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.parameter')\" name=\"parameter\"><parameter :ProductionOrderNo=\"detailobj.ID\" ref=\"parameter\"></parameter></el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.formula')\" name=\"formula\"><formula :ProductionOrderNo=\"detailobj.ID\" ref=\"formula\"></formula></el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.Processlongtext')\" name=\"Processlongtext\">\r\n                    <processlongtext :ProductionOrderNo=\"detailobj.ID\" :NeedQARelease=\"detailobj.NeedQARelease\" :PoStatus=\"detailobj.PoStatus\" ref=\"Processlongtext\"></processlongtext>\r\n                </el-tab-pane>\r\n                <el-tab-pane :label=\"this.$t('POList.property')\" name=\"property\">\r\n                    <property :ProductionOrderNo=\"detailobj.ID\" ref=\"property\"></property>\r\n                </el-tab-pane>\r\n            </el-tabs>\r\n        </el-drawer>\r\n        <el-dialog :title=\"$t('POList.WCSUpDate')\" id=\"Editdialog\" :visible.sync=\"WCSModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in WCSinputlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500\">{{ item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'number'\" v-model=\"item.value\">\r\n                            <template slot=\"append\">{{ detailobj.Unit }}</template>\r\n                        </el-input>\r\n                        <el-input v-else-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                        <span v-else>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-check-outline\" @click=\"WCSSave()\">\r\n                    {{ $t('GLOBAL._QD') }}\r\n                </el-button>\r\n                <el-button @click=\"WCSModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n\r\n        <el-dialog :title=\"$t('POList.EditTitle')\" id=\"Editdialog\" :visible.sync=\"EditModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Editlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'input'\" v-model=\"item.value\">\r\n                            <template slot=\"append\">{{ $t('POList.KGHOUR') }}</template>\r\n                        </el-input>\r\n                        <span v-else>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Editinputlist\" :key=\"index\">\r\n                    <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500\">{{ item.label }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" v-if=\"item.type == 'input'\" v-model=\"item.value\">\r\n                            <template slot=\"append\">{{ detailobj.Unit }}</template>\r\n                        </el-input>\r\n                        <el-select clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                            <el-option v-for=\"(it, ind) in item.options\" :key=\"ind\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                        </el-select>\r\n                        <el-input v-else-if=\"item.type == 'textArea'\" type=\"textarea\" autosize v-model=\"item.value\"></el-input>\r\n                        <span v-else>{{ item.value }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-edit-outline\">\r\n                    {{ $t('POList.Edit') }}\r\n                </el-button>\r\n                <el-button @click=\"EditModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n\r\n        <el-dialog :title=\"$t('POList.AddMaterialPreShift')\" :visible.sync=\"ShiftListModal\">\r\n            <div class=\"dialogdetailbox\">\r\n                <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500\">{{ $t('POList.MaterialPreShift') }}*</div>\r\n                <div class=\"dialogdetailsinglevalue\">\r\n                    <el-select v-model=\"ShiftID\">\r\n                        <el-option v-for=\"it in ShiftList\" :key=\"it.ID\" :label=\"it.Name\" :value=\"it.ID\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"ShiftListModal = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-edit-outline\" @click=\"ShiftSave()\">\r\n                    {{ $t('GLOBAL._QR') }}\r\n                </el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog :title=\"$t('POList.Complete')\" :visible.sync=\"CompleteModel\" width=\"650px\">\r\n            <div class=\"dialogdetailbox\" v-for=\"(item, index) in Completelist\" :key=\"index\">\r\n                <div class=\"dialogdetailsinglelabel\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                <div class=\"dialogdetailsinglevalue\">\r\n                    <el-select @change=\"getData2(item)\" v-model=\"item.value\" clearable filterable v-if=\"item.type == 'select'\">\r\n                        <el-option v-for=\"(it, ind) in item.options\" :key=\"ind\" :label=\"it.label\" :value=\"it.key\"></el-option>\r\n                    </el-select>\r\n                    <span v-else>{{ item.value }}</span>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"CompleteModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-edit-outline\" @click=\"CompleteSave()\">\r\n                    {{ $t('GLOBAL._QR') }}\r\n                </el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog :title=\"$t('Inventory.Print')\" id=\"Startdialog\" :visible.sync=\"StartModel\" width=\"650px\">\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"dialogdetailbox\" v-for=\"(item, index) in Startlist\" :key=\"index\" v-show=\"!item.notShow\">\r\n                    <div class=\"dialogdetailsinglelabel\" :style=\"{ width: item.type == 'BatchCode' ? '20%' : '20%' }\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                    <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: item.type == 'BatchCode' ? '400px' : '77%' }\">\r\n                        <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\" :disabled=\"item.disabled\"></el-input>\r\n                        <div v-else-if=\"item.type == 'BatchCode'\" style=\"display: flex\">\r\n                            <el-input @change=\"getQrCode()\" v-model=\"item.value\"></el-input>\r\n                            <el-input v-model=\"item.value2\" disabled></el-input>\r\n                            <el-input @change=\"getQrCode()\" v-model=\"item.value3\"></el-input>\r\n                            <el-button class=\"tablebtn\" @click=\"getBatchCode()\" size=\"mini\" style=\"margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff\" icon=\"el-icon-refresh\"></el-button>\r\n                        </div>\r\n                        <el-select @change=\"GetData()\" clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                            <el-option v-for=\"(it, ind) in item.options\" :key=\"ind\" :label=\"it.value\" :value=\"it.key\"></el-option>\r\n                        </el-select>\r\n                        <el-date-picker\r\n                            @change=\"GetData()\"\r\n                            v-else-if=\"item.type == 'date'\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                            :disabled=\"item.disabled\"\r\n                            v-model=\"item.value\"\r\n                            type=\"datetime\"\r\n                        ></el-date-picker>\r\n                        <span v-else>{{ chooseItem.TargetQuantity }}{{ chooseItem.Unit1 }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-video-play\" @click=\"SendColos()\">\r\n                    {{ $t('Overview.SendColos') }}\r\n                </el-button>\r\n                <el-button @click=\"StartModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog :title=\"$t('Consume.Scan')\" id=\"SSCCdialog\" :visible.sync=\"SSCCModel\" width=\"650px\">\r\n            <div style=\"display: flex\">\r\n                <div :style=\"'100%'\">\r\n                    <div class=\"dialogdetailbox\">\r\n                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: '20%' }\">{{ $t('Consume.SSCC') + ' *' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: '77%' }\">\r\n                            <el-input v-model=\"SSCCValue\" @keyup.enter.native=\"SearchSscc()\">\r\n                                <template slot=\"append\"><i slot=\"suffix\" class=\"el-icon-full-screen\" @click=\"SearchSscc()\"></i></template>\r\n                            </el-input>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"dialogdetailbox\">\r\n                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: '20%' }\">{{ $t('Overview.Batch') + ' *' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: '77%' }\">\r\n                            <el-select clearable v-model=\"BatchId\" filterable>\r\n                                <el-option v-for=\"it in SegmentBatchList\" :key=\"it.key\" :label=\"it.value\" :value=\"it.key\"></el-option>\r\n                            </el-select>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-refresh-left\" @click=\"ShowQRCode()\">\r\n                    {{ $t('Consume.Scan') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-zoom-in\" @click=\"SearchSscc()\">\r\n                    {{ $t('Consume.Search') }}\r\n                </el-button>\r\n                <el-button @click=\"SSCCModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <QRcode ref=\"QRcode\" @getQRcodesRes=\"getQRcodesRes\"></QRcode>\r\n        <el-dialog id=\"Consumedialog\" :visible.sync=\"ConsumeModel\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">{{ $t('Consume.Consume') }}</div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Consumelist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <span>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Consumeinputlist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <el-input type=\"number\" v-if=\"item.id == 'Quantity'\" v-model=\"item.value\">\r\n                                <template slot=\"append\">{{ ConsumeObj.ChangeUnit }}</template>\r\n                            </el-input>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-folder-checked\" @click=\"ConsumeSave()\">\r\n                    {{ $t('GLOBAL._BC') }}\r\n                </el-button>\r\n                <el-button @click=\"ConsumeModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog :title=\"$t('POList.ThroatOutput')\" :visible.sync=\"ThroatOutputModal\" width=\"500px\">\r\n            <div class=\"dialogdetailbox\" style=\"margin-top: 15px\">\r\n                <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500; width: 45%\">{{ $t('PackagingWorkOrder.Order_Sort.PlanStartTime') }}*</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: 100%\">\r\n                    <el-date-picker v-model=\"PlanStartTime\" type=\"datetime\" value-format=\"yyyy-MM-dd HH:mm:ss\" placeholder=\"\"></el-date-picker>\r\n                </div>\r\n            </div>\r\n            <div class=\"dialogdetailbox\">\r\n                <div class=\"dialogdetailsinglelabel\" style=\"font-weight: 500; width: 45%\">{{ $t('POList.SourceStroage') }}*</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: 100%\">\r\n                    <el-select v-model=\"Source\" filterable>\r\n                        <el-option v-for=\"it in SourceList\" :key=\"it.key\" :label=\"it.value\" :value=\"it.key\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"ThroatOutputModal = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-edit-outline\" @click=\"ThroatOutputSave()\">\r\n                    {{ $t('GLOBAL._QR') }}\r\n                </el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { WLPOlist } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetListViewList,\r\n    GetProductionUpdateShift,\r\n    GetProductionShiftSelect,\r\n    GetLastProcessData,\r\n    UpdateMaterialProcessDataStatus,\r\n    toSendOrderInfoToSS,\r\n    GettoRelease,\r\n    UpdatePoStatus,\r\n    RebuildBatch,\r\n    BindPoRecipe,\r\n    OperationPo,\r\n    MyGetEquipmentsByOrderId,\r\n    MySendLabelPrintToColos,\r\n    MyGetQrCode,\r\n    getDataDictionary,\r\n    GetSegmentBatchList\r\n} from '@/api/Producting/POlist.js';\r\nimport { GetPrinit2 } from '@/api/Inventory/common.js';\r\nimport { GetProduceOpen, GetBPEquipmentsSelect, GetBatchCode, ConsumeScanSSCC, ConsumeViewSave, getConsumeViewEntity, ProduceLocation, ProduceSave } from '@/api/Inventory/Overview.js';\r\nimport { GetDataTreeList } from '@/api/factoryPlant/process.js';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport moment from 'moment';\r\nimport { GetMSelectListClass, GetUnitList } from '@/api/Inventory/Inventory.js';\r\nexport default {\r\n    components: {\r\n        execute: () => import('./components/Execute'),\r\n        consume: () => import('./components/consume'),\r\n        batch: () => import('./components/batch'),\r\n        parameter: () => import('./components/parameter'),\r\n        formula: () => import('./components/formula'),\r\n        produce: () => import('./components/produce'),\r\n        processlongtext: () => import('./components/Processlongtext'),\r\n        property: () => import('./components/property')\r\n    },\r\n    data() {\r\n        return {\r\n            ThroatOutputModal: false,\r\n            PlanStartTime: null,\r\n            Source: '',\r\n            SourceList: [],\r\n            StartModel: false,\r\n            WCSModel: false,\r\n            WCSinputlist: [\r\n                {\r\n                    label: this.$t('POList.Num'),\r\n                    id: 'quantity',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'number'\r\n                },\r\n                {\r\n                    label: this.$t('TRACE_WGJC._PCH'),\r\n                    id: 'lotCode',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'input'\r\n                }\r\n            ],\r\n            Startlist: [\r\n                {\r\n                    notShow: true,\r\n                    label: this.$t('ANDON_BJZY.EquipmentName'),\r\n                    id: 'EquipmentId',\r\n                    value: '',\r\n                    // require: true,\r\n                    type: 'select',\r\n                    options: []\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.BatchCode'),\r\n                    id: 'LotCode',\r\n                    require: true,\r\n                    value: '',\r\n                    value2: '',\r\n                    value3: '',\r\n                    type: 'BatchCode'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.QrCode'),\r\n                    id: 'QrCode',\r\n                    disabled: true,\r\n                    value: '',\r\n                    type: 'input'\r\n                },\r\n                {\r\n                    label: this.$t('$vuetify.dataTable.INV_TPQD.PrintCount'),\r\n                    id: 'PrintCount',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'input'\r\n                }\r\n            ],\r\n            showFrom: true,\r\n            timepicker: [this.getDay(0), this.getDay(3)],\r\n            tableheader: WLPOlist,\r\n            tableId: 'PRO_POLIST',\r\n            QuickSearch: '',\r\n            pageOptions: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            searchlist: [\r\n                {\r\n                    type: 'select',\r\n                    name: this.$t('POList.Status'),\r\n                    id: 'Available',\r\n                    value: [],\r\n                    option: []\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'MaterialDescription',\r\n                    name: this.$t('POList.Material')\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'Resource',\r\n                    name: this.$t('POList.Source')\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'ProductionOrderNo',\r\n                    name: this.$t('POList.ProcessOrder')\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'Formula',\r\n                    name: this.$t('$vuetify.dataTable.PRO_POLIST.Formula')\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'SegmentCode',\r\n                    name: this.$t('POList.SegmentCode')\r\n                }\r\n            ],\r\n            tableList: [],\r\n            activeName: 'Execute',\r\n            detailShow: false,\r\n            detailobj: {},\r\n            ProduceModel: false,\r\n            BtnObj: {},\r\n            Producelist: [\r\n                {\r\n                    label: this.$t('Consume.ProcessOrder'),\r\n                    value: '',\r\n                    id: 'ProductionOrderNo'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Source'),\r\n                    value: '',\r\n                    id: 'Source'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Material'),\r\n                    value: '',\r\n                    id: 'MaterialDescription'\r\n                }\r\n            ],\r\n            Produceinputlist: [\r\n                {\r\n                    label: this.$t('Consume.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    value: '',\r\n                    type: 'date',\r\n                    datetype: 'datetime'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.ExpirationDate'),\r\n                    id: 'ExpirationDate',\r\n                    value: '',\r\n                    type: 'date',\r\n                    disable: true,\r\n                    datetype: 'datetime'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Batch'),\r\n                    id: 'BatchCode',\r\n                    value: '',\r\n                    disable: true,\r\n                    type: 'input'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Location'),\r\n                    id: 'Location',\r\n                    value: '',\r\n                    type: 'select',\r\n                    option: [],\r\n                    require: true\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Quantity'),\r\n                    id: 'Quantity',\r\n                    value: '',\r\n                    type: 'inputNumber',\r\n                    require: true\r\n                },\r\n                {\r\n                    label: this.$t('Consume.printlabel'),\r\n                    id: 'printlabel',\r\n                    value: false,\r\n                    type: 'switch'\r\n                }\r\n            ],\r\n            selectprinter: '',\r\n            selectprinterOption: [],\r\n            detailArr: [],\r\n            EditModel: false,\r\n            Editlist: [\r\n                {\r\n                    label: this.$t('POList.ProductionOrderNo'),\r\n                    value: '',\r\n                    id: 'ProductionOrderNo'\r\n                },\r\n                {\r\n                    label: this.$t('POList.Source'),\r\n                    value: '',\r\n                    id: 'Source'\r\n                },\r\n                {\r\n                    label: this.$t('POList.Material'),\r\n                    value: '',\r\n                    id: 'Material'\r\n                },\r\n                {\r\n                    label: this.$t('POList.MaterialVersion'),\r\n                    value: '',\r\n                    id: 'MaterialVersionNumber'\r\n                },\r\n                {\r\n                    label: this.$t('POList.PlanStartTime'),\r\n                    value: '',\r\n                    id: 'PlanStartTime'\r\n                },\r\n                {\r\n                    label: this.$t('POList.PlanEndTime'),\r\n                    value: '',\r\n                    id: 'PlanEndTime'\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    label: this.$t('POList.Speed'),\r\n                    value: '',\r\n                    id: 'Speed'\r\n                }\r\n            ],\r\n            Editinputlist: [\r\n                {\r\n                    label: this.$t('POList.Status'),\r\n                    id: 'Status',\r\n                    value: '',\r\n                    type: 'select',\r\n                    options: []\r\n                },\r\n                {\r\n                    label: this.$t('POList.Num'),\r\n                    id: 'Num',\r\n                    value: '',\r\n                    type: 'input'\r\n                }\r\n            ],\r\n            StatusList: [],\r\n            ReasonList: [],\r\n            ShiftList: [],\r\n            selectTabelData: [],\r\n            tablechooselist: 0,\r\n            ShiftID: '',\r\n            ShiftListModal: false,\r\n            checkRow: {},\r\n            CompleteModel: false,\r\n            SSCCModel: false,\r\n            SSCCValue: '',\r\n            BatchId: '',\r\n            ConsumeModel: false,\r\n            ConsumeObj: {},\r\n            SegmentBatchList: [],\r\n            Consumelist: [\r\n                {\r\n                    label: this.$t('Consume.ProcessOrder'),\r\n                    id: 'ProcessOrderNo',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Location'),\r\n                    id: 'Location',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Material'),\r\n                    id: 'Material',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Lot'),\r\n                    id: 'Batch',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.SSCC'),\r\n                    id: 'Sscc',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Quantity'),\r\n                    id: 'QuantitywithUnit',\r\n                    value: ''\r\n                }\r\n            ],\r\n            Consumeinputlist: [\r\n                {\r\n                    label: this.$t('Consume.Quantity'),\r\n                    value: '',\r\n                    id: 'Quantity',\r\n                    require: true\r\n                }\r\n            ],\r\n            detailList: [\r\n                {\r\n                    label: this.$t('Consume.Material'),\r\n                    value: '',\r\n                    id: 'Material'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.StorageBin'),\r\n                    value: '',\r\n                    id: 'StorageBin'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Required'),\r\n                    value: '',\r\n                    id: 'Quantity2'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Consumptions'),\r\n                    value: '',\r\n                    id: 'Quantity1'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Remaining'),\r\n                    value: '',\r\n                    id: 'Remaining'\r\n                },\r\n                {\r\n                    label: this.$t('Consume.Uom'),\r\n                    value: '',\r\n                    id: 'Unit1'\r\n                }\r\n            ],\r\n            Completelist: [\r\n                {\r\n                    label: this.$t('POList.Material'),\r\n                    id: 'Material',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.PlanQty'),\r\n                    id: 'PlanQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.ActualQty'),\r\n                    id: 'ActualQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.ProduceStatus'),\r\n                    id: 'ProduceStatus',\r\n                    require: true,\r\n                    type: 'select',\r\n                    options: [\r\n                        {\r\n                            key: 'NotComplete',\r\n                            label: this.$t('POList.NotComplete')\r\n                        },\r\n                        {\r\n                            key: 'OverComplete',\r\n                            label: this.$t('POList.OverComplete')\r\n                        },\r\n                        {\r\n                            key: 'CompleteAtOnce',\r\n                            label: this.$t('POList.CompleteAtOnce')\r\n                        }\r\n                    ],\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.Reason'),\r\n                    id: 'Reason',\r\n                    type: 'select',\r\n                    require: true,\r\n                    options: [],\r\n                    value: ''\r\n                }\r\n            ]\r\n        };\r\n    },\r\n    mounted() {\r\n        this.getSourceList();\r\n        this.getStatus();\r\n        this.GetShiftSelect();\r\n        this.getprintList();\r\n    },\r\n    methods: {\r\n        async Reopen() {\r\n            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_Reopen')}`, '', {\r\n                confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                type: 'warning'\r\n            }).then(async () => {\r\n                let params = {\r\n                    Id: this.detailobj.ID,\r\n                    Status: 6\r\n                };\r\n                let res = await UpdatePoStatus(params);\r\n                this.detailShow = false;\r\n                this.getPageList();\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            });\r\n        },\r\n        async ThroatOutputSave() {\r\n            if (this.Source == '' || this.PlanStartTime == '') {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                EquipmentId: this.Source,\r\n                ProductionDate: this.PlanStartTime\r\n            };\r\n            let res = await GetProduceOpen(params);\r\n            let data = res.response;\r\n            if (data == null) {\r\n                Message({\r\n                    message: `${this.$t('POList.NotFoundPO')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            } else {\r\n                this.ProduceOpen(data);\r\n                this.ThroatOutputModal = false;\r\n            }\r\n        },\r\n        async getSourceList() {\r\n            let res = await GetBPEquipmentsSelect();\r\n            this.SourceList = res.response;\r\n        },\r\n        ToThroatOutput() {\r\n            this.PlanStartTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n            this.Source = '';\r\n            this.ThroatOutputModal = true;\r\n        },\r\n        ShowQRCode() {\r\n            this.$refs.QRcode.getQRcode();\r\n        },\r\n        getQRcodesRes(val) {\r\n            this.SSCCValue = val.text;\r\n            this.SearchSscc();\r\n        },\r\n        async getprintList() {\r\n            let res = await GetPrinit2();\r\n            res.response.forEach(item => {\r\n                item.value = item.ID;\r\n                item.label = item.Code;\r\n                item.ItemName = item.Code;\r\n                item.ItemValue = item.ID;\r\n            });\r\n            this.selectprinterOption = res.response;\r\n        },\r\n        async SaveProduce() {\r\n            if (this.Produceinputlist[5].value === true && this.selectprinter === '') {\r\n                Message({\r\n                    message: `${this.$t('Inventory.PleaseSelectPrinter')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let flag = this.Produceinputlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                ExpirationDate: this.Produceinputlist[1].value,\r\n                ProductionDate: this.Produceinputlist[0].value,\r\n                BatchId: this.BtnObj.BatchId,\r\n                Location: this.Produceinputlist[3].value,\r\n                Quantity: this.Produceinputlist[4].value,\r\n                EquipmentId: this.BtnObj.RunEquipmentId,\r\n                ExecutionId: this.BtnObj.ExecutionId,\r\n                UnitId: this.BtnObj.UnitId,\r\n                IsPrint: this.Produceinputlist[5].value,\r\n                PrintId: this.selectprinter\r\n            };\r\n            let res = await ProduceSave(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.getPageList();\r\n            this.ProduceModel = false;\r\n        },\r\n        async ProduceOpen(row) {\r\n            this.Produceinputlist[3].value = '';\r\n            this.BtnObj = row;\r\n            let params = {\r\n                EquipmentId: row.RunEquipmentId\r\n            };\r\n            this.selectprinter = '';\r\n            let res = await ProduceLocation(params);\r\n            res.response.forEach(item => {\r\n                item.label = item.LocationName;\r\n                item.value = item.LocationId;\r\n                if (item.IsDefault == '1') {\r\n                    this.Produceinputlist[3].value = item.LocationId;\r\n                }\r\n            });\r\n            this.Produceinputlist[3].option = res.response;\r\n            if (this.selectprinterOption.length != 0) {\r\n                this.selectprinter = this.selectprinterOption[0].value;\r\n            }\r\n            for (let k in row) {\r\n                this.Producelist.forEach(item => {\r\n                    if (item.id == k) {\r\n                        if (k == 'MaterialDescription') {\r\n                            item.value = row.MaterialDescription + '-' + row.MaterialCode;\r\n                        } else {\r\n                            item.value = row[k];\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n            this.Produceinputlist.forEach(item => {\r\n                if (item.id == 'printlabel') {\r\n                    item.value = false;\r\n                } else if (item.id != 'Location') {\r\n                    item.value = '';\r\n                }\r\n            });\r\n            this.Produceinputlist[0].value = row.ProductionDate;\r\n            this.Produceinputlist[1].value = row.ExpirationDate;\r\n            this.Produceinputlist[2].value = row.BatchCode;\r\n            this.ProduceModel = true;\r\n        },\r\n        checkSelectable(row) {\r\n            if (row.PoStatus == '1' || row.PoStatus == '2') {\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        },\r\n        toWCSUpDate() {\r\n            this.WCSinputlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            this.WCSModel = true;\r\n        },\r\n        toColos(row) {\r\n            this.checkRow = row;\r\n            this.Startlist.forEach((item, index) => {\r\n                if (index == 1) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 2) {\r\n                    item.value2 = '';\r\n                    item.value3 = '';\r\n                } else {\r\n                    item.value = '';\r\n                }\r\n            });\r\n            this.getBatchCode();\r\n            this.StartModel = true;\r\n            // this.getEquipmentList();\r\n        },\r\n        async WCSSave() {\r\n            let flag = this.WCSinputlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let obj = {};\r\n            this.WCSinputlist.forEach(item => {\r\n                obj[item.id] = item.value;\r\n            });\r\n            obj.actionType = 3;\r\n            obj.productionOrderId = this.detailobj.ID;\r\n            let res = await toSendOrderInfoToSS(obj);\r\n            this.getPageList();\r\n            this.WCSModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        // async getEquipmentList() {\r\n        //     let id = JSON.stringify(this.checkRow.ID);\r\n        //     let res = await MyGetEquipmentsByOrderId(id);\r\n        //     res.response.forEach(item => {\r\n        //         item.key = item.key + '|' + item.value;\r\n        //     });\r\n        //     this.Startlist[0].options = res.response;\r\n        //     this.StartModel = true;\r\n        // },\r\n        GetData() {\r\n            if (this.Startlist[1].value != '') {\r\n                this.getBatchCode();\r\n            }\r\n        },\r\n        ScanOpen(row) {\r\n            this.SSCCValue = '';\r\n            this.SSCCModel = true;\r\n            this.GetSegmentBatchList(row);\r\n        },\r\n        async SearchSscc(row) {\r\n            if (row) {\r\n                this.SSCCValue = row.Sscc;\r\n            }\r\n            if (this.SSCCValue === '' || this.SSCCValue === null) {\r\n                Message({\r\n                    message: `${this.$t('ConsumptionHistory.NonSSCC')}`,\r\n                    type: 'error'\r\n                });\r\n                return;\r\n            }\r\n            if (this.BatchId === '' || this.BatchId === null) {\r\n                Message({\r\n                    message: `${this.$t('ConsumptionHistory.NoBatchId')}`,\r\n                    type: 'error'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                Sscc: this.SSCCValue\r\n            };\r\n            let res = await ConsumeScanSSCC(params);\r\n            let data = res.response;\r\n            this.Id = data.ID;\r\n            if (data.Location2) {\r\n                data.Location = data.Location1Code + '-' + data.Location2;\r\n            }\r\n            if (data.MaterialCode) {\r\n                data.Material = data.MaterialName + '-' + data.MaterialCode;\r\n            }\r\n            if (data.Quantity) {\r\n                data.QuantitywithUnit = data.Quantity + ' ' + data.Unit1;\r\n            }\r\n            this.ConsumeObj = data;\r\n            for (let k in data) {\r\n                this.Consumelist.forEach(item => {\r\n                    if (item.id == k) {\r\n                        item.value = data[k];\r\n                    }\r\n                });\r\n            }\r\n            let v = this.ConsumeObj.ChangeUnit.toLowerCase() === 'g' ? 1000 : 1;\r\n            this.Consumeinputlist[0].value = data.Quantity * v;\r\n            //this.Consumeinputlist[0].value = data.Quantity;\r\n\r\n            await this.GetConsumeViewEntity(data.ID);\r\n\r\n            this.SSCCModel = false;\r\n            this.ConsumeModel = true;\r\n        },\r\n        async GetConsumeViewEntity(id) {\r\n            let res = await getConsumeViewEntity(id);\r\n            if (res) {\r\n                var obj = res.response;\r\n                if (obj.MaterialName) {\r\n                    obj.Material = obj.MaterialName + '-' + obj.MaterialCode;\r\n                }\r\n                if (obj.Quantity2) {\r\n                    obj.Remaining = (Number(obj.Quantity2) - Number(obj.Quantity1)).toFixed(2);\r\n                }\r\n                this.detailobj = obj;\r\n                for (let k in obj) {\r\n                    this.detailList.forEach(item => {\r\n                        if (item.id == k) {\r\n                            item.value = obj[k];\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        async ConsumeSave() {\r\n            let v = this.ConsumeObj.ChangeUnit.toLowerCase() === 'g' ? 1000 : 1;\r\n            if (this.Consumeinputlist[0].value == 0) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return false;\r\n            }\r\n            if (Number(this.Consumeinputlist[0].value) > Number(this.ConsumeObj.Quantity * v)) {\r\n                Message({\r\n                    message: `${this.$t('Consume.Over')}`,\r\n                    type: 'warning'\r\n                });\r\n                return false;\r\n            }\r\n            if (Number(this.Consumeinputlist[0].value) > Number(this.detailList[4].value)) {\r\n                Message({\r\n                    message: `${this.$t('Consume.Over2')}`,\r\n                    type: 'warning'\r\n                });\r\n            }\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                Sscc: this.SSCCValue,\r\n                Quantity: this.Consumeinputlist[0].value,\r\n                IsNotCheckRunOrder: true\r\n            };\r\n            let res = await ConsumeViewSave(params);\r\n            this.BatchId = '';\r\n            this.SSCCValue = '';\r\n            this.ConsumeModel = false;\r\n            this.Consumeinputlist[0].value = '';\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        async GetSegmentBatchList(row) {\r\n            this.BatchId = '';\r\n            this.SegmentBatchList = [];\r\n            let p = {\r\n                ProductionOrderId: row.ID\r\n            };\r\n            let res = await GetSegmentBatchList(p);\r\n            this.SegmentBatchList = res.response;\r\n        },\r\n        async getQrCode() {\r\n            let code = this.Startlist[2].value + this.Startlist[2].value2 + this.Startlist[2].value3;\r\n            let res = await MyGetQrCode('', this.checkRow.ID, code);\r\n            this.Startlist[3].value = res.response;\r\n        },\r\n        async SendColos() {\r\n            let flag = this.Startlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                poId: this.checkRow.ID,\r\n                batchCode: this.Startlist[2].value + this.Startlist[2].value2 + this.Startlist[2].value3,\r\n                count: this.Startlist[3].value\r\n            };\r\n            if (params.batchCode.length > 10) {\r\n                Message({\r\n                    message: `${this.$t('Overview.BatchCodeLong')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let res = await MySendLabelPrintToColos(params);\r\n            this.getPageList();\r\n            this.StartModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        async getBatchCode() {\r\n            let LineCode = this.checkRow.LineCode;\r\n            let productionId = this.checkRow.ID;\r\n            let date = moment(this.Startlist[1].value).format('YYYY-MM-DD HH:mm:ss');\r\n            let p = {\r\n                LineCode: this.Startlist[2].value,\r\n                equipmentCode: LineCode,\r\n                productionDate: date,\r\n                productionId: productionId\r\n            };\r\n            let res = await GetBatchCode(p);\r\n            if (res.response == null) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'warning'\r\n                });\r\n            } else {\r\n                this.Startlist[2].value = res.response.substring(0, 2);\r\n                this.Startlist[2].value2 = res.response.substring(2, 5);\r\n            }\r\n        },\r\n        async GetShiftSelect() {\r\n            let res = await GetProductionShiftSelect();\r\n            this.ShiftList = res.response;\r\n        },\r\n        handleSelectionChange(val) {\r\n            this.selectTabelData = val;\r\n            this.tablechooselist = val.length;\r\n        },\r\n        addNew() {\r\n            this.ShiftID = '';\r\n            this.ShiftListModal = true;\r\n        },\r\n        async ShiftSave() {\r\n            if (this.ShiftID == '') {\r\n                Message({\r\n                    message: this.$t('POList.CHECKMaterialPreShift'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let arr = this.selectTabelData.map(item => {\r\n                return item.ID;\r\n            });\r\n            let params = {\r\n                ShiftID: this.ShiftID,\r\n                ProIDS: arr\r\n            };\r\n            let res = await GetProductionUpdateShift(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.ShiftListModal = false;\r\n            this.getPageList();\r\n        },\r\n        async toRebuildBatch(isPl) {\r\n            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_GJPC')}`, '', {\r\n                confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                type: 'warning'\r\n            }).then(async () => {\r\n                let arr = [];\r\n                if (isPl) {\r\n                    arr = this.selectTabelData.map(item => {\r\n                        return item.ID;\r\n                    });\r\n                } else {\r\n                    arr = [this.detailobj.ID];\r\n                }\r\n                let res = await RebuildBatch(arr);\r\n                this.detailShow = false;\r\n                this.getPageList();\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            });\r\n        },\r\n        async toBindPoRecipe(isPl) {\r\n            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_BDPF')}`, '', {\r\n                confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                type: 'warning'\r\n            }).then(async () => {\r\n                let arr = [];\r\n                if (isPl) {\r\n                    arr = this.selectTabelData.map(item => {\r\n                        return item.ID;\r\n                    });\r\n                } else {\r\n                    arr = [this.detailobj.ID];\r\n                }\r\n                let res = await BindPoRecipe(arr);\r\n                this.detailShow = false;\r\n                this.getPageList();\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            });\r\n        },\r\n        async toRelease() {\r\n            MessageBox.confirm(`${this.$t('GLOBAL._COMFIRM_SF')}`, '', {\r\n                confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                type: 'warning'\r\n            }).then(async () => {\r\n                let obj = {\r\n                    ID: this.detailobj.ID\r\n                };\r\n                if (this.detailobj.NeedQARelease == '0') {\r\n                    let parmas = {\r\n                        key: '2',\r\n                        body: JSON.stringify(obj)\r\n                    };\r\n                    let res = await OperationPo(parmas);\r\n                    this.getPageList();\r\n                    this.$refs[this.activeName].getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);\r\n                    Message({\r\n                        message: res.msg,\r\n                        type: 'success'\r\n                    });\r\n                } else {\r\n                    let parmas = {\r\n                        key: '5',\r\n                        body: JSON.stringify(obj)\r\n                    };\r\n                    let res = await OperationPo(parmas);\r\n                    this.getPageList();\r\n                    this.$refs[this.activeName].getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);\r\n                    Message({\r\n                        message: res.msg,\r\n                        type: 'success'\r\n                    });\r\n                }\r\n            });\r\n        },\r\n        async getData2(item) {\r\n            if (item.id == 'ProduceStatus') {\r\n                if (item.value != '') {\r\n                    let res = await this.$getNewDataDictionary(item.value);\r\n                    //let res = this.ReasonList.find(x=>x.ItemCode = item.value)\r\n                    // console.log(res);\r\n                    let data = res;\r\n                    if (data.length > 0) {\r\n                        data.forEach(item1 => {\r\n                            item1.key = item1.ItemValue;\r\n                            // console.log(this._i18n.locale);\r\n                            item1.label = this._i18n.locale === 'en' ? item1.ItemValue : item1.ItemName;\r\n                        });\r\n                    }\r\n                    this.Completelist[4].options = data;\r\n                    console.log(this.Completelist[4].options);\r\n                    this.Completelist[4].value = '';\r\n                }\r\n            }\r\n        },\r\n        async CompleteSave() {\r\n            let flag = this.Completelist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '' || item.value == null;\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                Id: this.detailobj.ID,\r\n                Status: 3,\r\n                ProduceStatus: this.Completelist[3].value,\r\n                Reason: this.Completelist[4].value\r\n            };\r\n            let res = await UpdatePoStatus(params);\r\n            this.detailShow = false;\r\n            this.CompleteModel = false;\r\n            this.getPageList();\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        toComplete() {\r\n            this.detailobj.Material = this.detailobj.MaterialDescription + '-' + this.detailobj.MaterialCode;\r\n            this.detailobj.PlanQuantity = this.detailobj.PlanQty + this.detailobj.Unit;\r\n            this.detailobj.ActualQuantity = this.detailobj.ActualQty + this.detailobj.Unit;\r\n            this.Completelist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            //console.log(this.Completelist[4]);\r\n            this.Completelist.forEach(item => {\r\n                for (let k in this.detailobj) {\r\n                    if (item.id == k) {\r\n                        item.value = this.detailobj[k];\r\n                    }\r\n                }\r\n            });\r\n            this.Completelist[4].options = [];\r\n            let PlanQuantity = Number(this.detailobj.PlanQty);\r\n            let ActualQuantity = Number(this.detailobj.ActualQty);\r\n            if (PlanQuantity > ActualQuantity) {\r\n                this.Completelist[3].value = 'NotComplete';\r\n            } else if (PlanQuantity == ActualQuantity) {\r\n                this.Completelist[3].value = 'CompleteAtOnce';\r\n            } else {\r\n                this.Completelist[3].value = 'OverComplete';\r\n            }\r\n            //console.log(this.ReasonList);\r\n            this.ReasonList.forEach(x => {\r\n                if (x.ItemCode == this.Completelist[3].value) {\r\n                    let res = this.Completelist[4].options.find(x1 => x1.ItemCode === this.Completelist[3].value && x1.ItemValue === x.ItemValue);\r\n                    if (res === null || res === undefined || typeof res === 'undefined') {\r\n                        x.key = x.ItemValue;\r\n                        x.label = this._i18n.locale === 'en' ? x.ItemValue : x.ItemName;\r\n                        this.Completelist[4].options.push(x);\r\n                    }\r\n                }\r\n            });\r\n            console.log(this.Completelist[4].options);\r\n            this.CompleteModel = true;\r\n        },\r\n        editShow() {\r\n            this.EditModel = true;\r\n        },\r\n        tabChange() {\r\n            setTimeout(() => {\r\n                this.$refs[this.activeName].getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);\r\n            }, 200);\r\n        },\r\n        async detaildrawShow(obj) {\r\n            this.activeName = 'Execute';\r\n            this.detailobj = obj;\r\n            let res = await GetLastProcessData('', this.detailobj.ID);\r\n            this.detailShow = true;\r\n            this.detailobj.Material = this.detailobj.MaterialDescription + '-' + this.detailobj.MaterialCode;\r\n            setTimeout(() => {\r\n                this.$refs.execute.getProductionOrderNo(this.detailobj.ID, this.detailobj.NeedQARelease, this.detailobj.PoStatus);\r\n            }, 200);\r\n            for (let k in this.detailobj) {\r\n                this.Editlist.forEach(item => {\r\n                    if (item.id == k) {\r\n                        item.value = this.detailobj[k];\r\n                    }\r\n                });\r\n            }\r\n            if (res.response.IsReminded == '0') {\r\n                MessageBox.confirm(`${this.$t('POList.warningText')}`, '', {\r\n                    confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                    cancelButtonText: `${this.$t('GLOBAL._GB')}`,\r\n                    closeOnClickModal: false,\r\n                    type: 'warning'\r\n                })\r\n                    .then(async () => {\r\n                        let params = {\r\n                            IsReminded: '1',\r\n                            ID: res.response.ID\r\n                        };\r\n                        let res2 = await UpdateMaterialProcessDataStatus(params);\r\n                        Message({\r\n                            message: res2.msg,\r\n                            type: 'success'\r\n                        });\r\n                    })\r\n                    .catch(async () => {});\r\n            }\r\n        },\r\n        getDay(day) {\r\n            var today = new Date();\r\n            var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;\r\n            today.setTime(targetday_milliseconds); //注意，这行是关键代码\r\n            var tYear = today.getFullYear();\r\n            var tMonth = today.getMonth();\r\n            var tDate = today.getDate();\r\n            tMonth = this.doHandleMonth(tMonth + 1);\r\n            tDate = this.doHandleMonth(tDate);\r\n            return tYear + '-' + tMonth + '-' + tDate;\r\n        },\r\n        doHandleMonth(month) {\r\n            var m = month;\r\n            if (month.toString().length == 1) {\r\n                m = '0' + month;\r\n            }\r\n            return m;\r\n        },\r\n        getStatusName(key) {\r\n            if (key) {\r\n                let name = '';\r\n                this.StatusList.forEach(item => {\r\n                    if (item.ItemValue == key) {\r\n                        name = item.ItemName;\r\n                    }\r\n                });\r\n                return name;\r\n            }\r\n        },\r\n        getReasonName(key1, key2) {\r\n            if (key1 === null || key1 === '' || key2 === null || key2 === '') {\r\n                return '';\r\n            }\r\n            let name = key2;\r\n            //console.log(this._i18n.locale);\r\n            if (this._i18n.locale == 'en') {\r\n                return name;\r\n            }\r\n            this.ReasonList.forEach(item => {\r\n                if (item.ItemCode === key1 && item.ItemValue === key2) {\r\n                    name = item.ItemName;\r\n                }\r\n            });\r\n            //console.log(name);\r\n            return name;\r\n        },\r\n        getStatusColor(key) {\r\n            if (key) {\r\n                let color = '';\r\n                this.StatusList.forEach(item => {\r\n                    if (item.ItemValue == key) {\r\n                        color = item.Description;\r\n                    }\r\n                });\r\n                return color;\r\n            }\r\n        },\r\n\r\n        getsearch() {\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.getPageList();\r\n        },\r\n        getempty() {\r\n            this.QuickSearch = '';\r\n            this.timepicker = [];\r\n            this.timepicker[0] = this.getDay(0);\r\n            this.timepicker[1] = this.getDay(3);\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.searchlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            this.getPageList();\r\n        },\r\n        async getStatus() {\r\n            let params = {\r\n                ItemCode: 'ProductionOrderStatus'\r\n            };\r\n            const res = await GetDataTreeList(params);\r\n            let data = res.response.data;\r\n            this.StatusList = data;\r\n            data.forEach(item => {\r\n                item.label = item.ItemName;\r\n                item.value = item.ItemValue;\r\n            });\r\n\r\n            this.ReasonList = [];\r\n            this.Completelist[3].options.forEach(async item => {\r\n                let p = {\r\n                    ItemCode: item.key\r\n                };\r\n                const res = await GetDataTreeList(p);\r\n                let data = res.response.data;\r\n                //let res = await this.$getNewDataDictionary(item.key);\r\n                console.log(res.response.data);\r\n                res.response.data.forEach(item1 => {\r\n                    this.ReasonList.push(item1);\r\n                });\r\n            });\r\n\r\n            this.Editinputlist[0].options = data;\r\n            this.searchlist[0].option = data;\r\n            this.getPageList();\r\n        },\r\n        async getPageList() {\r\n            if (this.timepicker == null) {\r\n                this.timepicker = [];\r\n            }\r\n            let params = {\r\n                NeedQARelease: '0',\r\n                Resource: '',\r\n                MaterialDescription: '',\r\n                ProductionOrderNo: '',\r\n                StatusList: this.searchlist[0].value,\r\n                StartTime: this.timepicker[0],\r\n                EndTime: this.timepicker[1] == undefined ? '' : this.timepicker[1] + ' 23:59:59',\r\n                FillLineCode: '',\r\n                Key: this.QuickSearch,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize,\r\n                orderByFileds: '',\r\n                Formula: '',\r\n                SegmentCode: ''\r\n            };\r\n            for (let k in params) {\r\n                this.searchlist.forEach(item => {\r\n                    if (k == item.id) {\r\n                        params[k] = item.value;\r\n                    }\r\n                });\r\n            }\r\n            const res = await GetListViewList(params);\r\n            if (res) {\r\n                this.tableList = res.response.data;\r\n            }\r\n            // if (res.response.count == 0) {\r\n            //     this.tableList = [];\r\n            // } else {\r\n            //     this.tableList = res.response.data;\r\n            // }\r\n            this.pageOptions.total = res.response.dataCount;\r\n        },\r\n        handleSizeChange(val) {\r\n            this.pageOptions.pageSize = val;\r\n            this.getPageList();\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.pageOptions.page = val;\r\n            this.getPageList();\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scope>\r\n.PoList {\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin-bottom: 10px;\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 50%;\r\n            text-align: right;\r\n        }\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n    .splitdetailbox {\r\n        padding: 10px 0;\r\n        border: 1px solid #e8e8e8;\r\n        margin-bottom: 5px;\r\n        .dialogdetailbox {\r\n            display: flex;\r\n            align-items: center;\r\n            width: 100%;\r\n            margin-top: 10px;\r\n            .dialogdetailsinglelabel {\r\n                font-weight: 600;\r\n                width: 47%;\r\n                text-align: right;\r\n            }\r\n            .dialogdetailsinglevalue {\r\n                width: 78%;\r\n                margin-left: 20px;\r\n            }\r\n        }\r\n    }\r\n    .drawerTitlelabel {\r\n        color: #808080;\r\n        font-size: 1rem;\r\n        .statusbox {\r\n            width: auto !important;\r\n        }\r\n    }\r\n    .dialog-title {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n    }\r\n    .drawEditBox {\r\n        font-size: 1.5rem;\r\n        margin-right: 15px;\r\n        cursor: pointer;\r\n    }\r\n}\r\n.el-dialog__body {\r\n    .el-input {\r\n        width: 250px !important;\r\n    }\r\n    .el-select {\r\n        width: 250px !important;\r\n    }\r\n    .el-textarea {\r\n        width: 250px !important;\r\n    }\r\n    .longwidthinput {\r\n        .el-input {\r\n            width: 400px !important;\r\n        }\r\n        .el-select {\r\n            width: 400px !important;\r\n        }\r\n        .el-textarea {\r\n            width: 400px !important;\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}