{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopAudit\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopAudit\\index.vue", "mtime": 1750296894458}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAwHA;AACA,SACAA,WADA,EACAC,eADA,EACAC,gBADA,QAEA,oBAFA;AAIA;AAGA;EACAC,iBADA;;EAEAC;IACA;MACAC;QACAC,YADA;QAEAC,YAFA;QAGAC,WAHA;QAIAC,WAJA;QAKAC;MALA,CADA;MAQAC,QARA;MASAC,aATA;MAUAC,yBAVA;MAWAC,cAXA;MAYAC,QAZA;MAaAC;QACAb,aADA;QAEAc,sBAFA;QAGAC,qCAHA;QAGA;QACAC,qCAJA;QAIA;QACAC,6CALA,CAKA;;MALA,CAbA;MAoBA;MACAC,0BArBA;MAsBAC;QACAC;MADA,CAtBA;MAyBAC;IAzBA;EA2BA,CA9BA;;EA+BAC;IACA;IACA;MACA;IACA,CAFA;;IAGAC;MACA;IACA,CAFA;EAGA,CAvCA;;EAwCAC;IACAC;MACA;IACA,CAHA;;IAIAC;MACA;MACA;IACA,CAPA;;IAQAC;MACA;MACA;IACA,CAXA;;IAYAC;MACA;MACA;IACA,CAfA;;IAiBAC;MACA;QACA1B,YADA;QAEAC,YAFA;QAGAC,WAHA;QAIAC,WAJA;QAKAC;MALA;MAOA;IACA,CA1BA;;IA4BAuB;MACA;QACAC,4BADA;QAEAC,mCAFA;QAGAC,kCAHA;QAIAC;MAJA,GAKAC,IALA,CAKA;QACAtC;UACA;UACA;QACA,CAHA;MAIA,CAVA,EAUAuC,KAVA,CAUAC;QACAC;MACA,CAZA;IAaA,CA1CA;;IA4CAC;MACAzC;QACA;QACA;MACA,CAHA;IAIA,CAjDA;;IAmDA;IACA0C;MACA;MACA;QACA;MACA,CAJA,CAKA;;;MACA;IACA,CA3DA;;IA6DA;IACAC;MACA;MACA;IACA,CAjEA;;IAmEA;IACAC;MACA;QACAC,wCADA;QAEAC,uCAFA;QAGAC;MAHA,GAIAV,IAJA,CAIA;QACA,oBACA,MADA;UAEAW,cAFA;UAEA;UACAC,qCAHA;UAIAC;QAJA;QAMAjD;UACA;UACA;QACA,CAHA;MAIA,CAfA,EAeAqC,KAfA,CAeA;QACA;MACA,CAjBA;IAkBA,CAvFA;;IAyFA;IACAa;MACA;MACA;MACA;IACA,CA9FA;;IAgGA;IACAC;MACA;QACA;UACA,oBACA,uBADA;YAEAJ,cAFA;YAEA;YACAC,qCAHA;YAIAC;UAJA;UAMAjD;YACA;YACA;YACA;UACA,CAJA;QAKA;MACA,CAdA;IAeA,CAjHA;;IAmHA;IACAoD;MACA;MACA;MACA;;MACA;QACAC;QACAC;MACA;;MACA;IACA,CA7HA;;IA+HA;IACAC;MACA;QACA,6BADA;QAEA;MAFA;MAIA;IACA,CAtIA;;IAwIA;IACAC;MACA;IACA,CA3IA;;IA6IA;IACAC;MACA;QACA,iCADA;QAEA,iCAFA;QAGA;MAHA;MAKA;IACA,CArJA;;IAuJA;IACAC;MACA;QACA,8BADA;QAEA,iCAFA;QAGA,+BAHA;QAIA;MAJA;MAMA;IACA,CAhKA;;IAkKA;IACAC;MACA;QACA,YADA;QAEA,YAFA;QAGA,YAHA;QAIA;MAJA;MAMA;IACA;;EA3KA;AAxCA,E,CAuNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "names": ["delSopAudit", "getSopAuditList", "saveSopAuditForm", "name", "data", "searchForm", "pageIndex", "pageSize", "doc<PERSON>ame", "docCode", "uploadUser", "total", "tableData", "tableName", "loading", "mainH", "buttonOption", "serveIp", "uploadUrl", "exportUrl", "DownLoadUrl", "rejectDialogVisible", "rejectForm", "auditComment", "currentAuditRow", "mounted", "window", "methods", "showDialog", "handleCurrentChange", "handleSizeChange", "getSearchBtn", "resetForm", "delRow", "title", "message", "confirmText", "cancelText", "then", "catch", "err", "console", "getTableData", "getDocFieldValue", "previewDoc", "<PERSON><PERSON><PERSON><PERSON>", "confirmButtonText", "cancelButtonText", "type", "AuditResult", "AuditUserId", "AuditComment", "<PERSON><PERSON><PERSON><PERSON>", "confirmReject", "formatFileSize", "size", "index", "formatStatus", "getStatusType", "formatOperationType", "formatAuditResult", "getAuditResultType"], "sourceRoot": "src/views/SOP/sopAudit", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <div class=\"root\">\n    <div class=\"root-head\">\n      <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n        <div class=\"form-content\">\n          <div class=\"search-area\">\n            <div class=\"search-row\">\n              <el-form-item :label=\"$t('SOP.DocName')\" prop=\"docName\" label-width=\"40px\" class=\"search-form-item\">\n                <el-input v-model=\"searchForm.docName\" :placeholder=\"$t('SOP.EnterDocName')\" clearable size=\"small\"></el-input>\n              </el-form-item>\n\n              <el-form-item :label=\"$t('SOP.DocCode')\" prop=\"docCode\" label-width=\"40px\" class=\"search-form-item\">\n                <el-input v-model=\"searchForm.docCode\" :placeholder=\"$t('SOP.EnterDocCode')\" clearable size=\"small\"></el-input>\n              </el-form-item>\n\n              <el-form-item :label=\"$t('SOP.UploadUser')\" prop=\"uploadUser\" label-width=\"40px\" class=\"search-form-item\">\n                <el-input v-model=\"searchForm.uploadUser\" :placeholder=\"$t('SOP.EnterUploadUser')\" clearable size=\"small\"></el-input>\n              </el-form-item>\n\n              <div class=\"action-buttons\">\n                <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn\" size=\"small\">{{ $t('GLOBAL._CX') }}</el-button>\n                <el-button icon=\"el-icon-refresh\" @click=\"resetForm\" size=\"small\">{{ $t('GLOBAL._CZ') }}</el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </el-form>\n    </div>\n    <div class=\"root-main\">\n      <el-table class=\"mt-3\"\n                :height=\"mainH\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n        <el-table-column\n          type=\"index\"\n          label=\"序号\"\n          width=\"50\"\n          align=\"center\">\n        </el-table-column>\n        <el-table-column v-for=\"(item) in tableName\"\n                         :default-sort=\"{prop: 'date', order: 'descending'}\"\n                         :key=\"item.value\"\n                         :prop=\"item.value\"\n                         :label=\"typeof item.text === 'function' ? item.text() : item.text\"\n                         :width=\"item.width\"\n                         :align=\"item.alignType || 'center'\"\n                         sortable\n                         show-overflow-tooltip\n        >\n          <template slot-scope=\"scope\">\n            <template v-if=\"item.value === 'FileSize'\">\n              {{ formatFileSize(getDocFieldValue(scope.row, item.value)) }}\n            </template>\n            <template v-else-if=\"item.value === 'DocStatus'\">\n              <el-tag :type=\"getStatusType(getDocFieldValue(scope.row, item.value))\" size=\"small\">\n                {{ formatStatus(getDocFieldValue(scope.row, item.value)) }}\n              </el-tag>\n            </template>\n            <template v-else-if=\"item.value === 'OperationType'\">\n              {{ formatOperationType(scope.row[item.value]) }}\n            </template>\n            <template v-else-if=\"item.value === 'AuditResult'\">\n              <el-tag :type=\"getAuditResultType(scope.row[item.value])\" size=\"small\">\n                {{ formatAuditResult(scope.row[item.value]) }}\n              </el-tag>\n            </template>\n            <template v-else-if=\"['DocName', 'DocCode', 'DocVersion', 'FilePath'].includes(item.value)\">\n              {{ getDocFieldValue(scope.row, item.value) }}\n            </template>\n            <template v-else>\n              {{ scope.row[item.value] }}\n            </template>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"operation\" width=\"180\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <div class=\"operation-buttons\">\n              <el-button size=\"mini\" type=\"primary\" @click=\"previewDoc(scope.row)\">{{ $t('SOP.Preview') }}</el-button>\n              <el-button size=\"mini\" type=\"success\" @click=\"approveAudit(scope.row)\"\n                         :disabled=\"scope.row.AuditResult !== null && scope.row.AuditResult !== 0\">{{ $t('SOP.Approve') }}</el-button>\n              <el-button size=\"mini\" type=\"danger\" @click=\"rejectAudit(scope.row)\"\n                         :disabled=\"scope.row.AuditResult !== null && scope.row.AuditResult !== 0\">{{ $t('SOP.Reject') }}</el-button>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    <div class=\"root-footer\">\n      <el-pagination\n          class=\"mt-3\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"searchForm.pageIndex\"\n          :page-sizes=\"[10,20, 50, 100,500]\"\n          :page-size=\"searchForm.pageSize\"\n          layout=\"->,total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          background\n      ></el-pagination>\n    </div>\n    <!-- 审核不通过原因对话框 -->\n    <el-dialog :title=\"$t('SOP.RejectReason')\" :visible.sync=\"rejectDialogVisible\" width=\"500px\"\n               :close-on-click-modal=\"false\" :close-on-press-escape=\"false\">\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" label-width=\"120px\">\n        <el-form-item :label=\"$t('SOP.RejectReason')\" prop=\"auditComment\"\n                      :rules=\"[{ required: true, message: $t('SOP.RejectReasonRequired'), trigger: 'blur' }]\">\n          <el-input type=\"textarea\" v-model=\"rejectForm.auditComment\"\n                    :placeholder=\"$t('SOP.EnterRejectReason')\" :rows=\"4\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rejectDialogVisible = false\">{{ $t('GLOBAL._QX') }}</el-button>\n        <el-button type=\"primary\" @click=\"confirmReject\">{{ $t('GLOBAL._QD') }}</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\nimport {\n    delSopAudit, getSopAuditList, saveSopAuditForm\n} from \"@/api/SOP/sopAudit\";\n\nimport { sopAuditColumn } from '@/api/SOP/sopAudit.js';\n\n\nexport default {\n  name: 'index.vue',\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n        docName: '',\n        docCode: '',\n        uploadUser: ''\n      },\n      total: 0,\n      tableData: [],\n      tableName: sopAuditColumn,\n      loading: false,\n      mainH: 0,\n      buttonOption:{\n        name:'SOP审核',\n        serveIp:'baseURL_DFM',\n        uploadUrl:'/api/SopAudit/ImportData', //导入\n        exportUrl:'/api/SopAudit/ExportData', //导出\n        DownLoadUrl:'/api/SopAudit/DownLoadTemplate', //下载模板\n      },\n      // 审核不通过原因对话框\n      rejectDialogVisible: false,\n      rejectForm: {\n        auditComment: ''\n      },\n      currentAuditRow: null\n    }\n  },\n  mounted() {\n    this.getTableData()\n    this.$nextTick(() => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    })\n    window.onresize = () => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    }\n  },\n  methods: {\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    resetForm() {\n      this.searchForm = {\n        pageIndex: 1,\n        pageSize: 20,\n        docName: '',\n        docCode: '',\n        uploadUser: ''\n      }\n      this.getTableData()\n    },\n\n    delRow(row) {\n      this.$confirms({\n        title: this.$t('GLOBAL._TS'),\n        message: this.$t('GLOBAL._COMFIRM'),\n        confirmText: this.$t('GLOBAL._QD'),\n        cancelText: this.$t('GLOBAL._QX')\n      }).then(async () => {\n        delSopAudit([row.ID]).then(res => {\n          this.$message.success(res.msg)\n          this.getTableData()\n        })\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n\n    getTableData() {\n      getSopAuditList(this.searchForm).then(res => {\n        this.tableData = res.response.data\n        this.total = res.response.dataCount\n      })\n    },\n\n    // 获取Doc字段中的文档信息\n    getDocFieldValue(row, fieldName) {\n      // 如果是文档相关字段，从Doc对象中获取\n      if (['DocName', 'DocCode', 'DocVersion', 'FilePath', 'FileSize', 'DocStatus'].includes(fieldName)) {\n        return row.Doc ? row.Doc[fieldName] : ''\n      }\n      // 其他字段直接从row中获取\n      return row[fieldName]\n    },\n\n    // 预览文档\n    previewDoc(row) {\n      // 这里可以实现文档预览功能\n      this.$message.info(this.$t('SOP.PreviewNotImplemented'))\n    },\n\n    // 通过审核\n    approveAudit(row) {\n      this.$confirm(this.$t('SOP.ConfirmApprove'), this.$t('GLOBAL._TS'), {\n        confirmButtonText: this.$t('GLOBAL._QD'),\n        cancelButtonText: this.$t('GLOBAL._QX'),\n        type: 'warning'\n      }).then(() => {\n        const auditData = {\n          ...row,\n          AuditResult: 2, // 审批通过\n          AuditUserId: this.$store.getters.name,\n          AuditComment: this.$t('SOP.AuditPassed')\n        }\n        saveSopAuditForm(auditData).then(res => {\n          this.$message.success(this.$t('SOP.ApproveSuccess'))\n          this.getTableData()\n        })\n      }).catch(() => {\n        this.$message.info(this.$t('GLOBAL._QXCZ'))\n      })\n    },\n\n    // 不通过审核\n    rejectAudit(row) {\n      this.currentAuditRow = row\n      this.rejectForm.auditComment = ''\n      this.rejectDialogVisible = true\n    },\n\n    // 确认不通过\n    confirmReject() {\n      this.$refs.rejectForm.validate((valid) => {\n        if (valid) {\n          const auditData = {\n            ...this.currentAuditRow,\n            AuditResult: 3, // 审批不通过\n            AuditUserId: this.$store.getters.name,\n            AuditComment: this.rejectForm.auditComment\n          }\n          saveSopAuditForm(auditData).then(res => {\n            this.$message.success(this.$t('SOP.RejectSuccess'))\n            this.rejectDialogVisible = false\n            this.getTableData()\n          })\n        }\n      })\n    },\n\n    // 格式化文件大小\n    formatFileSize(size) {\n      if (!size) return ''\n      const units = ['B', 'KB', 'MB', 'GB']\n      let index = 0\n      while (size >= 1024 && index < units.length - 1) {\n        size /= 1024\n        index++\n      }\n      return `${size.toFixed(2)} ${units[index]}`\n    },\n\n    // 格式化文档状态\n    formatStatus(status) {\n      const statusMap = {\n        1: this.$t('SOP.StatusValid'),\n        0: this.$t('SOP.StatusInvalid')\n      }\n      return statusMap[status] || this.$t('GLOBAL._WZ')\n    },\n\n    // 获取状态类型\n    getStatusType(status) {\n      return status === 1 ? 'success' : 'danger'\n    },\n\n    // 格式化操作类型\n    formatOperationType(type) {\n      const typeMap = {\n        1: this.$t('SOP.OperationCreate'),\n        2: this.$t('SOP.OperationModify'),\n        3: this.$t('SOP.OperationDelete')\n      }\n      return typeMap[type] || this.$t('GLOBAL._WZ')\n    },\n\n    // 格式化审核结果\n    formatAuditResult(result) {\n      const resultMap = {\n        0: this.$t('SOP.AuditPending'),\n        1: this.$t('SOP.AuditInProgress'),\n        2: this.$t('SOP.AuditApproved'),\n        3: this.$t('SOP.AuditRejected')\n      }\n      return resultMap[result] || this.$t('GLOBAL._WZ')\n    },\n\n    // 获取审核结果类型\n    getAuditResultType(result) {\n      const typeMap = {\n        0: 'warning',\n        1: 'primary',\n        2: 'success',\n        3: 'danger'\n      }\n      return typeMap[result] || 'info'\n    }\n  }\n}\n\n//<!-- 移到到src/local/en.json和zh-Hans.json -->\n//\"SopAudit\": {\n//    \"table\": {\n//        \"docId\": \"docId\",\n//        \"operationType\": \"operationType\",\n//        \"oldValue\": \"oldValue\",\n//        \"newValue\": \"newValue\",\n//        \"operatorId\": \"operatorId\",\n//        \"operateTime\": \"operateTime\",\n//        \"clientIp\": \"clientIp\",\n//        \"createdate\": \"createdate\",\n//        \"createuserid\": \"createuserid\",\n//        \"modifydate\": \"modifydate\",\n//        \"modifyuserid\": \"modifyuserid\",\n//        \"deleted\": \"deleted\",\n//    }\n//},\n</script>\n\n<style lang=\"scss\" scoped>\n.el-form-item--small.el-form-item {\n  margin-bottom: 0px;\n}\n\n.search-form-item {\n  .el-form-item__content {\n    width: 180px; // 统一宽度为180px\n  }\n}\n\n.root-head {\n  .search-form {\n    :deep(.el-form) {\n      .el-form-item--small.el-form-item {\n        margin-bottom: 0;\n      }\n    }\n\n    .form-content {\n      padding: 4px;\n\n      .search-area {\n        .search-row {\n          display: flex;\n          align-items: center;\n          gap: 4px;\n\n          .el-form-item {\n            margin: 0;\n            flex: none;\n\n            .el-form-item__label {\n              padding-right: 4px;\n              line-height: 26px;\n              font-size: 12px;\n            }\n\n            .el-form-item__content {\n              line-height: 26px;\n\n              .el-input,\n              .el-select {\n                :deep(.el-input__inner) {\n                  height: 26px;\n                  line-height: 26px;\n                  padding: 0 8px;\n                  font-size: 12px;\n                }\n              }\n            }\n          }\n\n          .action-buttons {\n            display: flex;\n            gap: 4px;\n            margin-left: 4px;\n\n            .el-button {\n              height: 26px;\n              padding: 0 10px;\n              font-size: 12px;\n\n              [class^=\"el-icon-\"] {\n                margin-right: 3px;\n                font-size: 12px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n.mt-8p {\n  margin-top: 8px;\n}\n\n.pd-left {\n  padding-left: 5px\n}\n\n.operation-buttons {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 2px;\n\n  .el-button--mini {\n    padding: 2px;\n    font-size: 11px;\n    border-radius: 3px;\n    min-width: auto;\n  }\n}\n</style>"]}]}