{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\auth\\Login.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\auth\\Login.vue", "mtime": 1750254216324}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpjb25zdCBuYW1lID0gJ3BhZ2UtbG9naW4nOw0KaW1wb3J0IHsgY29uZmlnVXJsIH0gZnJvbSAnQC9jb25maWcnOw0KDQpjb25zdCBTU09fVVJMID0gY29uZmlnVXJsW3Byb2Nlc3MuZW52LlZVRV9BUFBfU0VSVkVdLlNTT19VUkw7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6IG5hbWUsDQogIGNvbXBvbmVudHM6IHsNCiAgICBMb2NhbGVTd2l0Y2g6ICgpID0+IGltcG9ydCgnQC9jb21wb25lbnRzL2xvY2FsZS9Mb2NhbGVTd2l0Y2gnKQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIGZvcm1WYWxpZDogZmFsc2UsDQogICAgICBmb3JtTW9kZWw6IHsNCiAgICAgICAgdXNlcm5hbWU6ICcnLA0KICAgICAgICBwYXNzd29yZDogJycsDQogICAgICAgIHRva2VuOiAnJw0KICAgICAgfSwNCiAgICAgIGZvcm1SdWxlOiB7DQogICAgICAgIHVzZXJuYW1lOiBbdiA9PiAhIXYgfHwgdGhpcy4kdCgncnVsZS5yZXF1aXJlZFVzZXJuYW1lJyldLA0KICAgICAgICBwYXNzd29yZDogW3YgPT4gISF2IHx8IHRoaXMuJHQoJ3J1bGUucmVxdWlyZWRQYXNzd29yZCcpXQ0KICAgICAgfSwNCiAgICAgIHJlZGlyZWN0UGF0aDogJy8nLA0KICAgICAgcXVlcnlQYXI6IHt9DQogICAgfTsNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICAkcm91dGUoKSB7DQogICAgICBsb2NhdGlvbi5yZWxvYWQoKTsNCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5pc091dGVyQ2hhaW4oKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOi3s+i9rOS9v+eUqOWfn+eZu+W9lQ0KICAgIGdvRG9tYWluRW50cnkoKSB7DQogICAgICBjb25zb2xlLmxvZygiaG9zdCIsIHdpbmRvdy5kb2N1bWVudC5sb2NhdGlvbi5ob3N0KQ0KICAgICAgY29uc3QgY2FsbGJhY2sgPSB3aW5kb3cubG9jYXRpb24ub3JpZ2luOw0KICAgICAgY29uc3QgdXJsID0gYGh0dHBzOi8vc3NvLmFhYy5jb20vbG9naW4uYXNweD91cmw9JHtkZWNvZGVVUklDb21wb25lbnQoY2FsbGJhY2spfWA7DQogICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IHVybA0KICAgIH0sDQogICAgaXNPdXRlckNoYWluKCkgew0KICAgICAgY29uc3QgbyA9IHRoaXMuJHJvdXRlLnF1ZXJ5Ow0KICAgICAgY29uc3QgeyByZWRpcmVjdCwgcGFyLCBjb2RlLCBsb2dpblR5cGUgfSA9IG87DQoNCiAgICAgIGlmIChsb2dpblR5cGUgJiYgbG9naW5UeXBlLnRvTG93ZXJDYXNlKCkgPT09ICdzc28nKSB7DQogICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gU1NPX1VSTA0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCg0KICAgICAgdGhpcy5yZWRpcmVjdFBhdGggPSByZWRpcmVjdDsNCiAgICAgIC8vIHF1ZXJ5UGFy77ya5aSW6ZO+6Lez6L2s6L+H5p2l5bim55qE5Y+C5pWwDQogICAgICB0aGlzLnF1ZXJ5UGFyID0gcGFyID8gSlNPTi5wYXJzZShwYXIpIDoge307DQogICAgICAvL19ETFpI77yaIOiHquWKqOeZu+W9lei0puWPt++8m19ETE1N77yaIOiHquWKqOeZu+W9leWvhueggQ0KICAgICAgY29uc3QgeyBfRExaSCwgX0RMTU0gfSA9IHRoaXMucXVlcnlQYXI7DQogICAgICAvLyB0aGlzLiRuZXh0VGljaygoKT0+ew0KDQogICAgICAvLyB9KQ0KICAgICAgaWYgKF9ETFpIICYmIF9ETE1NKSB7DQogICAgICAgIGNvbnNvbGUubG9nKF9ETFpILCBfRExNTSk7DQogICAgICAgIHRoaXMuZm9ybU1vZGVsLnVzZXJuYW1lID0gX0RMWkg7DQogICAgICAgIHRoaXMuZm9ybU1vZGVsLnBhc3N3b3JkID0gX0RMTU07DQogICAgICAgIHRoaXMubG9naW5QYXIoKTsNCiAgICAgIH0gZWxzZSBpZiAoY29kZSkgew0KICAgICAgICB0aGlzLmZvcm1Nb2RlbC50b2tlbiA9IGNvZGUNCiAgICAgICAgdGhpcy5sb2dpblBhcigpDQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyB0aGlzLmdvRG9tYWluRW50cnkoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlTG9naW4oKSB7DQogICAgICBpZiAodGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlKCkpIHsNCiAgICAgICAgdGhpcy5sb2dpblBhcigpOw0KICAgICAgfQ0KICAgIH0sDQogICAgbG9naW5QYXIoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgdGhpcy4kc3RvcmUNCiAgICAgICAgICAuZGlzcGF0Y2goJ2xvZ2luJywgdGhpcy5mb3JtTW9kZWwpDQogICAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5oYW5kbGVQZXJtaXNzaW9uKCk7DQogICAgICAgICAgfSkNCiAgICAgICAgICAuY2F0Y2goKCkgPT4gew0KICAgICAgICAgICAgd2luZG93Ll9WTUEuJGVtaXQoJ1NIT1dfU05BQ0tCQVInLCB7DQogICAgICAgICAgICAgIHNob3c6IHRydWUsDQogICAgICAgICAgICAgIHRleHQ6ICfnmbvlvZXlpLHotKUnLA0KICAgICAgICAgICAgICBjb2xvcjogJ2Vycm9yJw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZVBlcm1pc3Npb24oKSB7DQogICAgICB0aGlzLiRzdG9yZQ0KICAgICAgICAgIC5kaXNwYXRjaCgnZ2V0UGVybWlzc2lvbicpDQogICAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICAvLyDnmbvlvZXmiJDlip/vvIzljrvmjonlnLDlnYDmoI/otKblj7flr4bnoIENCiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLnF1ZXJ5UGFyLl9ETFpIOw0KICAgICAgICAgICAgZGVsZXRlIHRoaXMucXVlcnlQYXIuX0RMTU07DQogICAgICAgICAgICBjb25zdCByb3V0ZSA9IHRoaXMucmVkaXJlY3RQYXRoID8geyBwYXRoOiB0aGlzLnJlZGlyZWN0UGF0aCwgcXVlcnk6IHRoaXMucXVlcnlQYXIgfSA6IHsgcGF0aDogJy8nIH07DQogICAgICAgICAgICB0aGlzLiRzdG9yZS5jb21taXQoJ1NFVEZMQUcnLCB0cnVlKQ0KICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2gocm91dGUpOw0KICAgICAgICAgIH0pDQogICAgICAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvNDA0Jyk7DQogICAgICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVSZWdpc3RlcigpIHsNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMpOw0KICAgIH0sDQogICAgaGFuZGxlU29jaWFsTG9naW4oKSB7IH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["Login.vue"], "names": [], "mappings": ";AA4CA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Login.vue", "sourceRoot": "src/views/auth", "sourcesContent": ["<template>\r\n  <div class=\"\">\r\n    <v-img class=\"img\" src=\"/static/defaultbg_2.png\" alt=\"\"></v-img>\r\n\r\n    <v-row class=\"page-login float\" fill-height>\r\n      <v-col :cols=\"12\">\r\n        <div class=\"logo1\">\r\n          <v-img class=\"float-left mx-15\" src=\"/static/Schneider_logo.png\" width=\"260px\" style=\"position: relative;margin-top: 0vh;\"></v-img>\r\n          <h2 class=\"float-right mx-15\" style=\"color: white; line-height: 82px;\">\r\n            <div style=\"margin-left: 2vw\">数字化工厂管理平台</div>\r\n          </h2>\r\n        </div>\r\n        <v-card class=\"d-flex flex-row mt-4\">\r\n          <v-img class=\"loginimg\" lazy-src=\"/static/Slogo_left_2.png\" src=\"/static/Slogo_left_2.png\"></v-img>\r\n          <v-card-text>\r\n            <v-form ref=\"form\" v-model=\"formValid\" class=\"ma-10\" width=\"500\" lazy-validation>\r\n              <v-text-field v-model=\"formModel.username\" append-icon=\"mdi-account\" autocomplete=\"off\"\r\n                            name=\"login\" autofocus :label=\"$t('username')\" :placeholder=\"$t('username')\" type=\"text\"\r\n                            required outlined :rules=\"formRule.username\" />\r\n              <v-text-field v-model=\"formModel.password\" append-icon=\"mdi-lock\" autocomplete=\"off\"\r\n                            name=\"password\" :label=\"$t('password')\" :placeholder=\"$t('password')\" type=\"password\"\r\n                            :rules=\"formRule.password\" required outlined @keyup.enter=\"handleLogin\" />\r\n              <v-btn large color=\"primary\" style=\"width: 100%\" :loading=\"loading\" @click=\"handleLogin\">\r\n                {{ $t('login') }}\r\n              </v-btn>\r\n            </v-form>\r\n            <div class=\"lang\">\r\n              <v-btn text color=\"primary\" @click=\"goDomainEntry()\">{{ $t('GLOBAL._YDL') }}</v-btn>\r\n              <LocaleSwitch />\r\n            </div>\r\n          </v-card-text>\r\n        </v-card>\r\n        <div class=\"copy\" align-center>CopyRight&copy;2022施耐德电气（中国）有限公司</div>\r\n      </v-col>\r\n    </v-row>\r\n    <v-row class=\"img my-0\" fill-height></v-row>\r\n    <!-- <div class=\"loading-box\">\r\n        <a-spin tip=\"系统正在初始化中...\" :spinning=\"loading\">\r\n        </a-spin>\r\n    </div> -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nconst name = 'page-login';\r\nimport { configUrl } from '@/config';\r\n\r\nconst SSO_URL = configUrl[process.env.VUE_APP_SERVE].SSO_URL;\r\nexport default {\r\n  name: name,\r\n  components: {\r\n    LocaleSwitch: () => import('@/components/locale/LocaleSwitch')\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      formValid: false,\r\n      formModel: {\r\n        username: '',\r\n        password: '',\r\n        token: ''\r\n      },\r\n      formRule: {\r\n        username: [v => !!v || this.$t('rule.requiredUsername')],\r\n        password: [v => !!v || this.$t('rule.requiredPassword')]\r\n      },\r\n      redirectPath: '/',\r\n      queryPar: {}\r\n    };\r\n  },\r\n  watch: {\r\n    $route() {\r\n      location.reload();\r\n    }\r\n  },\r\n  mounted() {\r\n    this.isOuterChain();\r\n  },\r\n  methods: {\r\n    // 跳转使用域登录\r\n    goDomainEntry() {\r\n      console.log(\"host\", window.document.location.host)\r\n      const callback = window.location.origin;\r\n      const url = `https://sso.aac.com/login.aspx?url=${decodeURIComponent(callback)}`;\r\n      window.location.href = url\r\n    },\r\n    isOuterChain() {\r\n      const o = this.$route.query;\r\n      const { redirect, par, code, loginType } = o;\r\n\r\n      if (loginType && loginType.toLowerCase() === 'sso') {\r\n        window.location.href = SSO_URL\r\n        return false\r\n      }\r\n\r\n      this.redirectPath = redirect;\r\n      // queryPar：外链跳转过来带的参数\r\n      this.queryPar = par ? JSON.parse(par) : {};\r\n      //_DLZH： 自动登录账号；_DLMM： 自动登录密码\r\n      const { _DLZH, _DLMM } = this.queryPar;\r\n      // this.$nextTick(()=>{\r\n\r\n      // })\r\n      if (_DLZH && _DLMM) {\r\n        console.log(_DLZH, _DLMM);\r\n        this.formModel.username = _DLZH;\r\n        this.formModel.password = _DLMM;\r\n        this.loginPar();\r\n      } else if (code) {\r\n        this.formModel.token = code\r\n        this.loginPar()\r\n      } else {\r\n        // this.goDomainEntry()\r\n      }\r\n    },\r\n    handleLogin() {\r\n      if (this.$refs.form.validate()) {\r\n        this.loginPar();\r\n      }\r\n    },\r\n    loginPar() {\r\n      this.loading = true;\r\n      this.$store\r\n          .dispatch('login', this.formModel)\r\n          .then(() => {\r\n            this.handlePermission();\r\n          })\r\n          .catch(() => {\r\n            window._VMA.$emit('SHOW_SNACKBAR', {\r\n              show: true,\r\n              text: '登录失败',\r\n              color: 'error'\r\n            });\r\n            this.loading = false;\r\n          });\r\n    },\r\n    handlePermission() {\r\n      this.$store\r\n          .dispatch('getPermission')\r\n          .then(() => {\r\n            this.loading = false;\r\n            // 登录成功，去掉地址栏账号密码\r\n            delete this.queryPar._DLZH;\r\n            delete this.queryPar._DLMM;\r\n            const route = this.redirectPath ? { path: this.redirectPath, query: this.queryPar } : { path: '/' };\r\n            this.$store.commit('SETFLAG', true)\r\n            this.$router.push(route);\r\n          })\r\n          .catch(() => {\r\n            this.$router.push('/404');\r\n          });\r\n    },\r\n    handleRegister() {\r\n      console.log(this);\r\n    },\r\n    handleSocialLogin() { }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-login {\r\n  position: relative;\r\n  min-width: 920px;\r\n  max-width: 920px;\r\n  margin: -221px auto;\r\n\r\n  // .loginimg {\r\n  //     .v-responsive__content {\r\n  //         background: rgba(255, 255, 255, 0.4) !important;\r\n  //     }\r\n  // }\r\n  .lang {\r\n    float: right;\r\n  }\r\n\r\n  .copy {\r\n    text-align: center;\r\n    margin-top: 48px;\r\n    color: #aaa;\r\n  }\r\n\r\n  .logo1 {\r\n    position: absolute;\r\n    top: -74px;\r\n  }\r\n}\r\n\r\n.img {\r\n  height: calc(50vh - 16px);\r\n}\r\n\r\n.loading-box {\r\n  position: fixed;\r\n  width: 100%;\r\n  height: 100%;\r\n  top: 0;\r\n  left: 0;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  // background: #fff;\r\n\r\n  // #3dcd58\r\n  ::v-deep .ant-spin-text {\r\n    color: #3dcd58;\r\n  }\r\n\r\n  ::v-deep .ant-spin-dot.ant-spin-dot-spin {\r\n    i {\r\n      background-color: #3dcd58;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}