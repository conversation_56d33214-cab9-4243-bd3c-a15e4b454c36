{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\columns\\SOP\\sopDoc.js", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\columns\\SOP\\sopDoc.js", "mtime": 1750249183818}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\eslint-loader\\index.js", "mtime": 1743379020994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGkxOG4gZnJvbSAnQC9wbHVnaW5zL2kxOG4nOwpleHBvcnQgY29uc3Qgc29wRG9jQ29sdW1ucyA9IFt7CiAgdGV4dDogKCkgPT4gaTE4bi50KCdTT1AuRG9jTmFtZScpLAogIHZhbHVlOiAnRG9jTmFtZScsCiAgd2lkdGg6ICcyMDBweCcKfSwgewogIHRleHQ6ICgpID0+IGkxOG4udCgnU09QLkRvY0NvZGUnKSwKICB2YWx1ZTogJ0RvY0NvZGUnLAogIHdpZHRoOiAnMTYwcHgnCn0sIHsKICB0ZXh0OiAoKSA9PiBpMThuLnQoJ1NPUC5Eb2NWZXJzaW9uJyksCiAgdmFsdWU6ICdEb2NWZXJzaW9uJywKICB3aWR0aDogJzEyMHB4Jwp9LCB7CiAgdGV4dDogKCkgPT4gaTE4bi50KCdTT1AuRmlsZVBhdGgnKSwKICB2YWx1ZTogJ0ZpbGVQYXRoJywKICB3aWR0aDogJzIwMHB4Jwp9LCB7CiAgdGV4dDogKCkgPT4gaTE4bi50KCdTT1AuRmlsZVNpemUnKSwKICB2YWx1ZTogJ0ZpbGVTaXplJywKICB3aWR0aDogJzEyMHB4Jwp9LCB7CiAgdGV4dDogKCkgPT4gaTE4bi50KCdTT1AuRG9jU3RhdHVzJyksCiAgdmFsdWU6ICdEb2NTdGF0dXMnLAogIHdpZHRoOiAnMTAwcHgnCn1dOw=="}, {"version": 3, "names": ["i18n", "sopDocColumns", "text", "t", "value", "width"], "sources": ["C:/work/syngentagroup/SEFA_XZD/SEFA.UI/sefa-application-business/src/columns/SOP/sopDoc.js"], "sourcesContent": ["import i18n from '@/plugins/i18n';\r\n\r\nexport const sopDocColumns = [\r\n    { text: () => i18n.t('SOP.DocName'), value: 'DocName', width: '200px' },\r\n    { text: () => i18n.t('SOP.DocCode'), value: 'DocCode', width: '160px' },\r\n    { text: () => i18n.t('SOP.DocVersion'), value: 'DocVersion', width: '120px' },\r\n    { text: () => i18n.t('SOP.FilePath'), value: 'FilePath', width: '200px' },\r\n    { text: () => i18n.t('SOP.FileSize'), value: 'FileSize', width: '120px' },\r\n    { text: () => i18n.t('SOP.DocStatus'), value: 'DocStatus', width: '100px' }\r\n];"], "mappings": "AAAA,OAAOA,IAAP,MAAiB,gBAAjB;AAEA,OAAO,MAAMC,aAAa,GAAG,CACzB;EAAEC,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,aAAP,CAAd;EAAqCC,KAAK,EAAE,SAA5C;EAAuDC,KAAK,EAAE;AAA9D,CADyB,EAEzB;EAAEH,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,aAAP,CAAd;EAAqCC,KAAK,EAAE,SAA5C;EAAuDC,KAAK,EAAE;AAA9D,CAFyB,EAGzB;EAAEH,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,gBAAP,CAAd;EAAwCC,KAAK,EAAE,YAA/C;EAA6DC,KAAK,EAAE;AAApE,CAHyB,EAIzB;EAAEH,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,cAAP,CAAd;EAAsCC,KAAK,EAAE,UAA7C;EAAyDC,KAAK,EAAE;AAAhE,CAJyB,EAKzB;EAAEH,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,cAAP,CAAd;EAAsCC,KAAK,EAAE,UAA7C;EAAyDC,KAAK,EAAE;AAAhE,CALyB,EAMzB;EAAEH,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,eAAP,CAAd;EAAuCC,KAAK,EAAE,WAA9C;EAA2DC,KAAK,EAAE;AAAlE,CANyB,CAAtB"}]}