﻿using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpContextUser;

namespace SEFA.DFM.Services
{
    public class SopAuditServices : BaseServices<SopAuditEntity>, ISopAuditServices
    {
        private readonly IBaseRepository<SopAuditEntity> _dal;
        private readonly IUser _user;

        public SopAuditServices(IBaseRepository<SopAuditEntity> dal,
            IUser user)
        {
            this._dal = dal;
            base.BaseDal = dal;
            this._user = user;
        }

        public async Task<List<SopAuditEntity>> GetList(SopAuditRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SopAuditEntity>()
                .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<SopAuditEntity>> GetPageList(SopAuditRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SopAuditEntity>()
                .AndIF(!string.IsNullOrWhiteSpace(_user.Name), a => a.AuditUserId.Equals(_user.Name))
                .OrIF(!string.IsNullOrWhiteSpace(_user.Name), a => a.OperatorId.Equals(_user.Name))
                .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return data;
        }

        public async Task<bool> SaveForm(SopAuditEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}