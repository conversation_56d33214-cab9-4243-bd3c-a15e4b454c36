# SOP模块国际化修改总结

## 修改概述

参照MaterialPreparation模块的国际化实现规范，对SOP模块的sopDir和sopPermission进行了国际化修改，确保符合项目的国际化功能规范。

## 修改的文件

### 1. Vue组件文件

#### 1.1 sopDir模块
- **文件**: `SEFA.UI/sefa-application-business/src/views/SOP/sopDir/index.vue`
  - 将硬编码的中文文本替换为 `$t()` 函数调用
  - 修改的文本包括：
    - 表单标签：目录名称、目录编码、目录负责人
    - 占位符文本：请输入目录名称、请输入目录编码
    - 表格列标题
    - 按钮文本：取消、确定
    - 数据列定义中的标签
    - 验证规则中的错误消息

- **文件**: `SEFA.UI/sefa-application-business/src/views/SOP/sopDir/form-dialog.vue`
  - 修改表单标签和占位符文本
  - 更新验证规则中的错误消息

#### 1.2 sopPermission模块
- **文件**: `SEFA.UI/sefa-application-business/src/views/SOP/sopPermission/index.vue`
  - 将搜索表单中的标签和占位符文本国际化

- **文件**: `SEFA.UI/sefa-application-business/src/views/SOP/sopPermission/form-dialog.vue`
  - 修改权限配置表单中的标签和占位符文本

### 2. 国际化文件

#### 2.1 中文简体 (zh-CN.json)
添加了SOP模块的国际化配置：
```json
"SOP": {
    "DirName": "目录名称",
    "DirCode": "目录编码(唯一)",
    "DirOwner": "目录负责人",
    "EnterDirName": "请输入目录名称",
    "EnterDirCode": "请输入目录编码(唯一)",
    "PrimaryKey": "主键",
    "ParentDirId": "父目录ID",
    "DirOwnerId": "目录负责人ID",
    "CreateTime": "创建时间",
    "CreateUserId": "创建人ID",
    "ModifyTime": "修改时间",
    "ModifyUserId": "修改人ID",
    "Timestamp": "时间戳",
    "DeleteFlag": "删除标记(0-未删 1-已删)",
    "ParentDirIdRequired": "父目录ID不能为空",
    "DirNameRequired": "目录名称不能为空",
    "DirCodeRequired": "目录编码(唯一)不能为空",
    "DirOwnerRequired": "目录负责人ID不能为空",
    "DirNameLength": "长度在 2 到 50 个字符",
    "DirCodeFormat": "只能包含字母、数字、下划线和横线",
    "TargetId": "授权对象ID(目录ID/文档ID)",
    "TargetType": "对象类型(1-目录 2-文档)",
    "GrantType": "授权类型(1-用户 2-部门)",
    "GrantId": "被授权对象ID",
    "SelectTargetId": "请选择授权对象ID(目录ID/文档ID)",
    "SelectTargetType": "请选择对象类型(1-目录 2-文档)",
    "SelectGrantType": "请选择授权类型(1-用户 2-部门)",
    "SelectGrantId": "请选择被授权对象ID"
}
```

#### 2.2 中文繁体 (zh-TW.json)
添加了对应的繁体中文翻译

#### 2.3 英文 (en.json)
添加了对应的英文翻译

## 修改原则

1. **遵循项目规范**: 参照MaterialPreparation模块的国际化实现方式
2. **使用$t()函数**: 所有显示文本都通过$t()函数进行国际化
3. **统一命名规范**: 使用SOP作为命名空间，保持一致性
4. **完整性**: 包含所有用户界面文本，包括标签、占位符、错误消息等
5. **多语言支持**: 提供中文简体、中文繁体和英文三种语言支持

## 国际化键值命名规范

- 基础字段：`SOP.DirName`、`SOP.DirCode`等
- 占位符文本：`SOP.EnterDirName`、`SOP.EnterDirCode`等
- 验证消息：`SOP.DirNameRequired`、`SOP.DirCodeRequired`等
- 选择提示：`SOP.SelectTargetId`、`SOP.SelectGrantType`等

## 验证

修改完成后，SOP模块的sopDir和sopPermission功能将支持：
1. 中文简体界面
2. 中文繁体界面  
3. 英文界面
4. 动态语言切换
5. 所有文本的完整国际化支持

## 注意事项

1. 所有硬编码的中文文本已被替换为国际化函数调用
2. 验证规则中的错误消息也已国际化
3. 保持了原有的功能逻辑不变
4. 遵循了项目现有的国际化架构和命名规范
