{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\FullBag.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\components\\FullBag.vue", "mtime": 1750150388566}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA2CA;AACA;AACA;AAEA;EACAA;IACA;MACAC,QADA;MAEAC,OAFA;MAGAC,eAHA;MAIAC,aAJA;MAKAC,aALA;MAMAC,SANA;MAOAC;IAPA;EASA,CAXA;;EAYAC;IACA;IACA;IACA;IACA;EACA,CAjBA;;EAkBAC;IACAC;MACA;IACA,CAHA;;IAIAC;MACA;IACA,CANA;;IAOAC;MACA;IACA,CATA;;IAUA;MACA;;MACA;QACAC;UACAC,+DADA;UAEAC;QAFA;QAIA;MACA;;MACA;MACA;;MACA;QACAF;UACAC,8DADA;UAEAC;QAFA;QAIA;MACA;;MACA;QACAF;UACAC,gEADA;UAEAC;QAFA;QAIA;MACA;;MACA;QACAF;UACAC,uDADA;UAEAC;QAFA;QAIA;MACA;;MACA;QACAC,sEADA;QACA;QACAC,iDAFA;QAGAC,iBAHA;QAIAC,iDAJA;QAKAC,uBALA;QAMAC,4BANA;QAOAC,qCAPA;QAQAC,yBARA;QASAC,2CATA;QAUAC,qFAVA;QAWAC,0DAXA;QAYAC,4CAZA;QAaAC,+BAbA;QAcAC;MAdA;;MAgBA;QACA7B;MACA,CAFA,MAEA;QACAA;MACA;;MACA;MACAa;QACAC,gBADA;QAEAC;MAFA;MAIA;QACA;QACA;MACA,CAHA;MAIA;IACA;;EAzEA;AAlBA", "names": ["data", "sscc", "Bags", "ssccFlag", "BagWeight", "<PERSON><PERSON><PERSON>", "SubId", "InQuantity", "mounted", "methods", "getbtnStatus", "getRowBySSCC", "getSSCC", "Message", "message", "type", "ChangeUnit", "PrintId", "subID", "equpmentID", "bags", "actualValue", "MaterialId", "bagWeight", "targetWeight", "actualWeight", "containerID", "proOrderID", "batchID", "batchConsumeRequirementId"], "sourceRoot": "src/views/Inventory/buildpalletsStart/components", "sources": ["FullBag.vue"], "sourcesContent": ["<template>\r\n    <div class=\"usemystyle PartialBag\">\r\n        <div class=\"tabinputbox\">\r\n            <div class=\"tabinputsinglebox\">\r\n                <el-input size=\"mini\" @change=\"getRowBySSCC\" ref=\"autoFocus\" :placeholder=\"$t('Consume.SSCC')\" v-model=\"sscc\">\r\n                    <template slot=\"append\"><i class=\"el-icon-full-screen\"></i></template>\r\n                </el-input>\r\n            </div>\r\n            <div class=\"tabinputsinglebox\" style=\"flex-direction: row; align-items: center\">\r\n                <div class=\"tabinputsinglelabel\">{{ $t('MaterialPreparationBuild.Bags') }}:</div>\r\n                <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" type=\"number\" v-model=\"Bags\"></el-input>\r\n                <!-- <div class=\"tabbtnsinglebox\" style=\"flex-direction: row; align-items: center; width: 220px\">\r\n                    <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-top\"></el-button>\r\n                    <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-bottom\"></el-button>\r\n                </div> -->\r\n            </div>\r\n            <div class=\"tabinputsinglebox\" style=\"flex-direction: row; align-items: center\">\r\n                <div class=\"tabinputsinglelabel\">{{ $t('MaterialPreparationBuild.BagWeight') }}:</div>\r\n                <el-input onkeyup=\"value=value.replace(/^0+|[^0-9\\.]/g, '')\" type=\"number\" v-model=\"BagWeight\"></el-input>\r\n                <!-- <div class=\"tabbtnsinglebox\" style=\"flex-direction: row; align-items: center; width: 250px\">\r\n                    <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-top\"></el-button>\r\n                    <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-bottom\"></el-button>\r\n                </div> -->\r\n            </div>\r\n            <div class=\"tabinputsinglebox\">\r\n                <div class=\"tabbtnsinglebox\">\r\n                    <el-button\r\n                        ref=\"FullBagbtn\"\r\n                        style=\"margin-left: 5px\"\r\n                        :disabled=\"!(ssccFlag && sscc != '' && detailobj.CompleteStates != 'OK' && Bags != 0)\"\r\n                        size=\"small\"\r\n                        icon=\"el-icon-bottom\"\r\n                        class=\"tablebtn\"\r\n                        @click=\"Transfer()\"\r\n                    >\r\n                        {{ this.$t('MaterialPreparationBuild.Transfer') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { TransferFullBag } from '@/api/Inventory/MaterialPreparation.js';\r\nimport { Message, MessageBox } from 'element-ui';\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            sscc: '',\r\n            Bags: 0,\r\n            ssccFlag: false,\r\n            BagWeight: '',\r\n            detailobj: {},\r\n            SubId: '',\r\n            InQuantity: 0\r\n        };\r\n    },\r\n    mounted() {\r\n        this.detailobj = JSON.parse(this.$route.query.query);\r\n        this.BagWeight = this.detailobj.BagSize;\r\n        this.Bags = this.detailobj.BagS;\r\n        this.getbtnStatus();\r\n    },\r\n    methods: {\r\n        getbtnStatus() {\r\n            return this.ssccFlag && this.sscc != '' && this.detailobj.CompleteStates != 'OK' && this.Bags != 0;\r\n        },\r\n        getRowBySSCC() {\r\n            this.$emit('getRowBySscc', this.sscc);\r\n        },\r\n        getSSCC() {\r\n            this.$emit('getRowSSCC', this.sscc);\r\n        },\r\n        async Transfer() {\r\n            let num = Number(this.Bags) * Number(this.BagWeight);\r\n            if (this.$parent.$parent.$parent.isExpirationDate) {\r\n                Message({\r\n                    message: this.$t('MaterialPreparationBuild.OverExpirationDate'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            this.detailobj = this.$parent.$parent.$parent.detailobj;\r\n            let SelectList = this.$parent.$parent.$parent.SelectList;\r\n            if (SelectList == null || SelectList.length == 0) {\r\n                Message({\r\n                    message: this.$t('MaterialPreparationBuild.BatchPalletsEmpty'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            if (num.toFixed() > this.InQuantity) {\r\n                Message({\r\n                    message: this.$t('MaterialPreparationBuild.InQuantityNotEnough'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            if (num + Number(this.detailobj.MQuantity) > this.detailobj.MaxPvalue) {\r\n                Message({\r\n                    message: this.$t('MaterialPreparationBuild.QtyOverMax'),\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let data = {\r\n                ChangeUnit:this.detailobj.ChangeUnit?\"\":this.detailobj.ChangeUnit,//增加单位转换\r\n                PrintId: window.sessionStorage.getItem('PrintId'),\r\n                subID: this.SubId,\r\n                equpmentID: window.sessionStorage.getItem('room'),\r\n                bags: Number(this.Bags),\r\n                actualValue: this.InQuantity,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                bagWeight: this.BagWeight,\r\n                targetWeight: this.detailobj.MQuantityTotal,\r\n                actualWeight: this.detailobj.MQuantity == null ? 0 : Number(this.detailobj.MQuantity),\r\n                containerID: window.sessionStorage.getItem('BatchPallets'),\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId,\r\n                batchConsumeRequirementId: this.detailobj.BatchConsumeRequirementId,\r\n            };\r\n            if (window.sessionStorage.getItem('MaterialPreparation') != 'clbl') {\r\n                data.actualWeight = this.detailobj.MQuantity == null ? 0 : Number(this.detailobj.MQuantity);\r\n            } else {\r\n                data.actualWeight = this.detailobj.MQuantity == null ? 0 : Number(this.detailobj.MQuantity);\r\n            }\r\n            let res = await TransferFullBag(data);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.$nextTick(_ => {\r\n                this.sscc = '';\r\n                this.$refs.autoFocus.focus();\r\n            });\r\n            this.$emit('getRefresh');\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\">\r\n.PartialBag {\r\n    padding: 10px;\r\n    margin: 10px;\r\n    height: 88px;\r\n    width: 100%;\r\n    border: 1px solid #ebeef5;\r\n    .tabinputbox {\r\n        height: 100%;\r\n        width: 100%;\r\n        display: flex;\r\n    }\r\n    .statusbox {\r\n        padding: 0 10px;\r\n        height: 30px;\r\n        background: #ffa500;\r\n    }\r\n    .tabinputsinglebox {\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        max-width: 250px;\r\n        margin-right: 10px;\r\n\r\n        .tabinputsinglelabel {\r\n            width: 100px;\r\n            font-size: 12px;\r\n        }\r\n        .tabbtnsinglebox {\r\n            height: 30px;\r\n            margin-bottom: 4px;\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}