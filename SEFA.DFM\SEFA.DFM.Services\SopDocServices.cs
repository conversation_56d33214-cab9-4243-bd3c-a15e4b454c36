﻿using System;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Http;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.ViewModels.ENUM;

namespace SEFA.DFM.Services
{
    public class SopDocServices : BaseServices<SopDocEntity>, ISopDocServices
    {
        private readonly IBaseRepository<SopDocEntity> _dal;
        private readonly ISopAuditServices _isopauthServices;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ISopDirServices _isopDirServices;
        private readonly IUser _user;
        private readonly IUnitOfWork _unitOfWork;

        public SopDocServices(IBaseRepository<SopDocEntity> dal,
            ISopAuditServices isopauthServices,
            ISopDirServices isopDirServices,
            IHttpContextAccessor httpContextAccessor,
            IUnitOfWork unitOfWork,
            IUser user)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _isopauthServices = isopauthServices;
            _isopDirServices = isopDirServices;
            _httpContextAccessor = httpContextAccessor;
            _unitOfWork = unitOfWork;
            _user = user;
        }


        public async Task<bool> BatchAddAsync(List<SopDocEntity> docs)
        {
            // 开始事务
            _unitOfWork.BeginTran();

            try
            {
                // 1. 预处理文档列表
                var clientIp = _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? "";
                var now = DateTime.Now;

                // 并行预处理文档
                Parallel.ForEach(docs, doc =>
                {
                    doc.CreateCustomGuid(_user.UserName);
                    doc.DocStatus = 3;
                });

                // 2. 获取目录ID并查询所有者
                var dirIds = docs.Select(a => a.DirId).Distinct().ToList();
                var dirOwnerMap = (await _isopDirServices.FindList(a => dirIds.Contains(a.ID)))
                    .ToDictionary(a => a.ID, a => a.OwnerUserid);

                // 3. 创建审核记录
                List<SopAuditEntity> sopAuditEntities = docs
                    .Select(sopDocEntity =>
                    {
                        var ownerId = dirOwnerMap.TryGetValue(sopDocEntity.DirId, out var id) ? id : "";

                        var entity = new SopAuditEntity
                        {
                            DocId = sopDocEntity.ID,
                            OperationType = (int)OperationType.Create,
                            OperatorId = _user.Name,
                            OperateTime = now,
                            AuditUserId = ownerId,
                            ClientIp = clientIp,
                            Deleted = 0
                        };
                        entity.CreateCustomGuid(_user.Name); // 单独调用void方法
                        return entity; // 返回创建的对象
                    })
                    .ToList();


                // 4. 批量添加审核记录
                await _isopauthServices.Add(sopAuditEntities);

                // 5. 批量添加文档
                var result = await _dal.Add(docs) > 0;

                // 提交事务
                _unitOfWork.CommitTran();
                return result;
            }
            catch (Exception ex)
            {
                // 回滚事务
                _unitOfWork.RollbackTran();
                // 记录错误日志
                SerilogServer.LogError(ex, "SopDocServices.BatchAddAsync");
                return false;
            }
        }


        public async Task<string> GetLatestVersionByCode(string docCode)
        {
            var lastVersion = await _dal.Db.Queryable<SopDocEntity>()
                .Where(a => a.DocCode == docCode && a.Deleted == 0)
                .OrderBy(b => b.CreateDate, OrderByType.Desc).ToListAsync();
            return lastVersion.FirstOrDefault()?.DocVersion;
        }

        public async Task<List<SopDocEntity>> GetList(SopDocRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SopDocEntity>()
                .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<SopDocEntity>> GetPageList(SopDocRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SopDocEntity>()
                .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return data;
        }

        public async Task<bool> SaveForm(SopDocEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}