<template>
  <div class="app-container">
    <div class="search-container">
      <el-form :model="queryParams" size="small" label-position="right" inline ref="queryForm" :label-width="labelWidth" 
        v-show="showSearch" @submit.native.prevent>
        
        <el-form-item :label="$t('SOP.DirName')" prop="dirName">
          <el-input v-model="queryParams.dirName" :placeholder="$t('SOP.EnterDirName')" />
        </el-form-item>

        <el-form-item :label="$t('SOP.DirCode')" prop="dirCode">
          <el-input v-model="queryParams.dirCode" :placeholder="$t('SOP.EnterDirCode')" />
        </el-form-item>

        <el-form-item :label="$t('SOP.DirOwner')" prop="ownerUserid">
          <user-select v-model="queryParams.ownerUserid" />
        </el-form-item>

        <el-form-item>
          <el-button-group>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-button-group>
        </el-form-item>
      </el-form>
      
      <div class="button-group">
        <el-button-group>
          <el-button 
            type="primary" 
            v-hasPermi="['DFM:SOP:add']" 
            plain 
            icon="el-icon-plus" 
            size="mini"
            @click="handleAdd">新增</el-button>
          <el-button 
            type="info" 
            plain 
            icon="el-icon-sort" 
            size="mini"
            @click="toggleExpandAll">展开/折叠</el-button>
          <el-button 
            type="danger" 
            :disabled="multiple" 
            v-hasPermi="['DFM:SOP:delete']" 
            plain 
            icon="el-icon-delete" 
            size="mini"
            @click="handleDelete">删除</el-button>
        </el-button-group>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </div>
    </div>

    <!-- 数据区域 -->
    <el-table v-if="refreshTable" :data="dataList" v-loading="loading" ref="table" border highlight-current-row @selection-change="handleSelectionChange"
      :default-expand-all="isExpandAll" row-key="id" :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      @row-click="handleRowClick">
      <el-table-column type="selection" width="50" align="center"/>
      <el-table-column prop="parentId" label="父目录ID" align="center">
        <template slot-scope="scope">
          <dict-tag :options=" parentIdOptions" :value="scope.row.parentId" />
        </template>
      </el-table-column>
      <el-table-column prop="dirName" :label="$t('SOP.DirName')" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="dirCode" :label="$t('SOP.DirCode')" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="ownerUserid" :label="$t('SOP.DirOwner')" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.ownerUserName || scope.row.ownerUserid }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createdate" label="创建时间" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="createuserid" label="创建人ID" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="modifydate" label="修改时间" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="modifyuserid" label="修改人ID" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="deleted" label="删除标记(0-未删 1-已删)" align="center" />

      <el-table-column label="操作" align="center" width="140">
        <template slot-scope="scope">
          <el-button size="mini" v-hasPermi="['DFM:SOP:edit']" type="success" icon="el-icon-edit" title="编辑"
            @click="handleUpdate(scope.row)"></el-button>
          <el-button size="mini" v-hasPermi="['DFM:SOP:delete']" type="danger" icon="el-icon-delete" title="删除"
            @click="handleDelete(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination class="mt10" background :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改SOP文档目录树结构表对话框 -->
    <el-dialog :title="title" :lock-scroll="false" :visible.sync="open" >
      <el-form ref="form" :model="form" :rules="rules" :label-width="formLabelWidth">
        <el-row :gutter="20">
        
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancel">{{ $t('GLOBAL._QX') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  getSopDirTree as treelistSopDir,
  getSopDirList as listSopDir,
  saveSopDirForm as addSopDir,
  delSopDir,
  saveSopDirForm as updateSopDir,
  getSopDirDetail as getSopDir,
} from '@/api/SOP/sopDir.js';
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import UserSelect from '@/components/UserSelect';

export default {
  name: "sopdir",
  components: { 
    UserSelect 
  },
  data() {
    return {
      labelWidth: "100px",
      formLabelWidth:"100px",
      // 选中id数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dirName: undefined,
        dirCode: undefined,
        ownerUserid: undefined,
      },
      // 弹出层标题
      title: "",
      // 操作类型 1、add 2、edit
      opertype: 0,
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 表单参数
      form: {},
      columns: [
        { index: 0, key: 'id', label: this.$t('SOP.PrimaryKey'), checked:  true  },
        { index: 1, key: 'parentId', label: this.$t('SOP.ParentDirId'), checked:  true  },
        { index: 2, key: 'dirName', label: this.$t('SOP.DirName'), checked:  true  },
        { index: 3, key: 'dirCode', label: this.$t('SOP.DirCode'), checked:  true  },
        { index: 4, key: 'ownerUserid', label: this.$t('SOP.DirOwnerId'), checked:  true  },
        { index: 5, key: 'createdate', label: this.$t('SOP.CreateTime'), checked:  true  },
        { index: 6, key: 'createuserid', label: this.$t('SOP.CreateUserId'), checked:  true  },
        { index: 7, key: 'modifydate', label: this.$t('SOP.ModifyTime'), checked:  true  },
        { index: 8, key: 'modifyuserid', label: this.$t('SOP.ModifyUserId'), checked:  true  },
        { index: 9, key: 'updatetimestamp', label: this.$t('SOP.Timestamp'), checked:  false  },
        { index: 10, key: 'deleted', label: this.$t('SOP.DeleteFlag'), checked:  false  },
      ],
      // 父目录ID选项列表
      parentIdOptions: [],
      // 目录负责人ID选项列表
      ownerUseridOptions: [],
      dataList: [],
      total: 0,
      rules: {
        parentId: [
          { required: true, message: this.$t('SOP.ParentDirIdRequired'), trigger: "change" }
        ],
        dirName: [
          { required: true, message: this.$t('SOP.DirNameRequired'), trigger: "blur" }
        ],
        dirCode: [
          { required: true, message: this.$t('SOP.DirCodeRequired'), trigger: "blur" }
        ],
        ownerUserid: [
          { required: true, message: this.$t('SOP.DirOwnerRequired'), trigger: "change" }
        ],
      },
    };
  },
  created() {
    // 列表数据查询
    this.getList();
  },
  methods: {
    // 查询数据
    getList() {
      this.loading = true;
      treelistSopDir(this.queryParams).then(res => {
         if (res.code == 200) {
           this.dataList = res.data;
           this.loading = false;
         }
       })
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.dirName,
        children: node.children,
      };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 重置数据表单
    reset() {
      this.form = {
        id: undefined,
        parentId: undefined,
        dirName: undefined,
        dirCode: undefined,
        ownerUserid: undefined,
        createdate: undefined,
        createuserid: undefined,
        modifydate: undefined,
        modifyuserid: undefined,
        updatetimestamp: undefined,
        deleted: undefined,
      };
      this.resetForm("form");
    },
    /** 重置查询操作 */
    resetQuery() {
      this.timeRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1
      this.multiple = !selection.length;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加";
      this.opertype = 1;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const Ids = row.id || this.ids;

      this.$confirm('是否确认删除参数编号为"' + Ids + '"的数据项？')
        .then(function () {
          return delSopDir(Ids);
        })
        .then(() => {
          this.handleQuery();
          this.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getSopDir(id).then((res) => {
        const { code, data } = res;
        if (code == 200) {
          this.open = true;
          this.title = "修改数据";
          this.opertype = 2;

          this.form = {
            ...data,
          };
        }
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined && this.opertype === 2) {
            updateSopDir(this.form)
              .then((res) => {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
            })
          } else {
            addSopDir(this.form)
              .then((res) => {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
            })
          }
        }
      });
    },
    //展开/折叠操作
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    // 行点击事件
    handleRowClick(row) {
      this.$emit('node-click', row);
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .search-container {
    margin-bottom: 15px;
    
    .el-form {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      padding-bottom: 15px;
      border-bottom: 1px solid #ebeef5;
      
      .el-form-item {
        margin-bottom: 0;
        margin-right: 20px;
        
        .el-input {
          width: 200px;
        }
      }
    }
    
    .button-group {
      display: flex;
      align-items: center;
      margin-top: 15px;
      
      .el-button-group {
        margin-right: 10px;
        
        .el-button {
          margin-right: 0;
          border-radius: 0;
          
          &:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
          }
          
          &:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
          }
        }
      }
    }
  }

  .el-table {
    margin: 15px 0;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    
    &::before {
      display: none;
    }

    .el-table__header {
      th {
        background-color: #f8fafc;
        color: #333;
        font-weight: 600;
      }
    }
    
    .el-table__body {
      tr:hover > td {
        background-color: #f5f7fa !important;
      }
    }
  }

  .pagination {
    margin-top: 15px;
    text-align: right;
  }
}
</style>