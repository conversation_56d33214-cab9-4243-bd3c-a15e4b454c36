﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;
using SEFA.Base.Model.BASE;

namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///文档变更审计表
    ///</summary>
    [SugarTable("DFM_M_SOP_AUDIT")]
    public class SopAuditEntity : EntityBase
    {
        public SopAuditEntity()
        {
        }

        [SugarColumn(IsIgnore = true)]
        public SopDocEntity Doc { get; set; }
        
        /// <summary>
        /// Desc:关联文档ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DOC_ID")]
        public string DocId { get; set; }

        /// <summary>
        /// Desc:操作类型(1-创建 2-修改 3-删除)
        /// Desc:操作类型
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "OPERATION_TYPE")]
        public int OperationType { get; set; }


        /// <summary>
        /// Desc:变更前值(JSON格式)
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "OLD_VALUE")]
        public string OldValue { get; set; }

        /// <summary>
        /// Desc:变更后值(JSON格式)
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "NEW_VALUE")]
        public string NewValue { get; set; }

        /// <summary>
        /// Desc:操作人ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "OPERATOR_ID")]
        public string OperatorId { get; set; }

        /// <summary>
        /// Desc:操作时间
        /// Default:DateTime.Now
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "OPERATE_TIME")]
        public DateTime? OperateTime { get; set; }

        /// <summary>
        /// Desc:客户端IP
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CLIENT_IP")]
        public string ClientIp { get; set; }

        /// <summary>
        /// Desc:删除标记(0-未删 1-已删)
        /// Default:0
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int? Deleted { get; set; }
        
        /// <summary>
        /// 审核人
        /// </summary>
        [SugarColumn(ColumnName = "AUDIT_USER_ID")]
        public string AuditUserId { get; set; }
        
        /// <summary>
        /// 审核意见
        /// </summary>
        [SugarColumn(ColumnName = "AUDIT_COMMENT")]
        public string AuditComment { get; set; }
        
        
        /// <summary>
        /// 审批结果(0-审批中，1-审批通过，2-审批不通过)
        /// </summary>
        [SugarColumn(ColumnName = "AUDIT_RESULT")]
        public int? AuditResult { get; set; }
    }
}