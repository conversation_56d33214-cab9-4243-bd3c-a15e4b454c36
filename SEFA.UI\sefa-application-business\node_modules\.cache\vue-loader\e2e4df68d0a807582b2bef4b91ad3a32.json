{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\productionManagement\\ResourceDefinition\\components\\form-dialog-operation.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\productionManagement\\ResourceDefinition\\components\\form-dialog-operation.vue", "mtime": 1750254216361}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgeyBzYXZlT3BlcmF0aW9uIH0gZnJvbSAnQC9hcGkvcHJvZHVjdGlvbk1hbmFnZW1lbnQvRm9ybXVsYSc7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ2FkZCcsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGRpYWxvZ0Zvcm06IHt9LA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBmb3JtTG9hZGluZzogZmFsc2UsDQogICAgICBydWxlczogew0KICAgICAgICBTZWdtZW50Q29kZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlt6XmrrXnvJbnoIEnLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgXSwNCiAgICAgICAgU2VnbWVudE5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5bel5q615ZCN56ewJywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBzdWJtaXQoKSB7DQogICAgICB0aGlzLiRyZWZzLmRpYWxvZ0Zvcm0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHNhdmVPcGVyYXRpb24odGhpcy5kaWFsb2dGb3JtKS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MocmVzLm1zZykNCiAgICAgICAgICAgIHRoaXMuJGVtaXQoJ3NhdmVGb3JtJykNCiAgICAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBzaG93KGRhdGEpIHsNCiAgICAgIGNvbnNvbGUubG9nKGRhdGEpOw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgdGhpcy4kbmV4dFRpY2soXyA9PiB7DQogICAgICAgIHRoaXMuZGlhbG9nRm9ybSA9IHsNCiAgICAgICAgICAuLi5kYXRhDQogICAgICAgIH0NCiAgICAgICAgdGhpcy4kcmVmcy5kaWFsb2dGb3JtLnJlc2V0RmllbGRzKCkNCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["form-dialog-operation.vue"], "names": [], "mappings": ";AAqBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "form-dialog-operation.vue", "sourceRoot": "src/views/productionManagement/ResourceDefinition/components", "sourcesContent": ["<template>\r\n  <el-dialog :title=\"dialogForm.ID ? '编辑' : '新增'\" :visible.sync=\"dialogVisible\" width=\"600px\"\r\n    :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\r\n    @close=\"dialogVisible = false\">\r\n    <el-form :rules=\"rules\" ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"80px\">\r\n      <el-form-item label=\"工段编码\" prop=\"SegmentCode\">\r\n        <el-input v-model=\"dialogForm.SegmentCode\" :maxlength=\"20\" placeholder=\"\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"工段名称\" prop=\"SegmentName\">\r\n        <el-input v-model=\"dialogForm.SegmentName\" :maxlength=\"20\" placeholder=\"\" />\r\n      </el-form-item>\r\n    </el-form>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\r\n      <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\"\r\n        @click=\"submit()\">确定\r\n      </el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n<script>\r\nimport { saveOperation } from '@/api/productionManagement/Formula';\r\n\r\nexport default {\r\n  name: 'add',\r\n  data() {\r\n    return {\r\n      dialogForm: {},\r\n      dialogVisible: false,\r\n      formLoading: false,\r\n      rules: {\r\n        SegmentCode: [\r\n          { required: true, message: '请输入工段编码', trigger: 'blur' },\r\n        ],\r\n        SegmentName: [\r\n          { required: true, message: '请输入工段名称', trigger: 'blur' },\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$refs.dialogForm.validate((valid) => {\r\n        if (valid) {\r\n          saveOperation(this.dialogForm).then(res => {\r\n            this.$message.success(res.msg)\r\n            this.$emit('saveForm')\r\n            this.dialogVisible = false\r\n          })\r\n        }\r\n      });\r\n    },\r\n    show(data) {\r\n      console.log(data);\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.dialogForm = {\r\n          ...data\r\n        }\r\n        this.$refs.dialogForm.resetFields()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}