import request from '@/util/request'
import { configUrl } from '@/config'
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM

/**
 * 权限申请审核表分页查询
 * @param {查询条件} data
 */
export function getSopPermApplyList(data) {
    return request({
        url: baseURL + '/api/SopPermApply/GetPageList',
        method: 'post',
        data
    })
}

/**
 * 保存权限申请审核表
 * @param data
 */
export function saveSopPermApplyForm(data) {
    return request({
        url: baseURL + '/api/SopPermApply/SaveForm',
        method: 'post',
        data
    })
}

/**
 * 获取权限申请审核表详情
 * @param {Id}
 */
export function getSopPermApplyDetail(id) {
    return request({
        url: baseURL + '/api/SopPermApply/GetEntity',
        method: 'post',
        data: id
    })
}

/**
 * 删除权限申请审核表
 * @param {主键} data
 */
export function delSopPermApply(data) {
    return request({
        url: baseURL + '/api/SopPermApply/Delete',
        method: 'post',
        data
    })
}


