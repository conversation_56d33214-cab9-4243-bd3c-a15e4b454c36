{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\components\\POManagement.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\components\\POManagement.vue", "mtime": 1750254216282}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA0aA;AACA;AACA;AACA;AACA,SACAA,mBADA,EAEAC,oBAFA,EAGAC,iBAHA,EAIAC,YAJA,EAKAC,eALA,EAMAC,cANA,EAOAC,cAPA,EAQAC,gBARA,EASAC,kBATA,EAUAC,UAVA,EAWAC,iBAXA,EAYAC,kBAZA,EAaAC,WAbA,EAcAC,4BAdA,EAeAC,cAfA,QAgBA,6BAhBA;AAiBA;AACA;AAEA;EACAC,oBADA;;EAGAC;IACA;MACAC,eACA;QACAC,gCADA;QAEAC,kBAFA;QAGAC;MAHA,CADA,EAMA;QACAF,kCADA;QAEAC,oBAFA;QAGAC;MAHA,CANA,EAWA;QACAF,sCADA;QAEAC,mBAFA;QAGAE,aAHA;QAIAC,cAJA;QAKAC,UACA;UACAC,kBADA;UAEAN;QAFA,CADA,EAKA;UACAM,mBADA;UAEAN;QAFA,CALA,EASA;UACAM,qBADA;UAEAN;QAFA,CATA,CALA;QAmBAE;MAnBA,CAXA,EAgCA;QACAF,+BADA;QAEAC,YAFA;QAGAG,cAHA;QAIAD,aAJA;QAKAE,WALA;QAMAH;MANA,CAhCA,CADA;MA0CAK,aA1CA;MA2CAC,8FA3CA;MA4CAC,2BA5CA;MA6CAC,eA7CA;MA8CAC,4BA9CA;MA+CAC,+BA/CA;MAgDAC,iCAhDA;MAiDAC,yBAjDA;MAkDAC,YAlDA;MAmDAC,4BAnDA;MAoDAC,eApDA;MAqDAC,eArDA;MAsDAC,aAtDA;MAuDAC,aACA;QACAhB,aADA;QAEAP,qCAFA;QAGAI,iBAHA;QAIAC;MAJA,CADA,CAvDA;MA+DAmB;QACAC,QADA;QAEAC,OAFA;QAEA;QACAC,YAHA;QAGA;QACAC,YAJA;QAIA;QACAC;MALA,CA/DA;MAsEAC,uBAtEA;MAuEAC,iBAvEA;MAwEAC,YACA;QACA7B,oCADA;QAEAC,eAFA;QAGAC,SAHA;QAIA4B,cAJA;QAKA1B;MALA,CADA,EAQA;QACAJ,gCADA;QAEAC,aAFA;QAGAC,SAHA;QAIAC,aAJA;QAKAC,cALA;QAMA2B;MANA,CARA,EAgBA;QACA/B,oCADA;QAEAC,aAFA;QAGAC,SAHA;QAIA8B,UAJA;QAKAC,UALA;QAMA9B,aANA;QAOAC;MAPA,CAhBA,EAyBA;QACAJ,yCADA;QAEAC,oBAFA;QAGAE,aAHA;QAIAD,SAJA;QAKAE;MALA,CAzBA,EAgCA;QACAJ,yCADA;QAEAC,oBAFA;QAGAE,aAHA;QAIAD,SAJA;QAKAE,YALA;QAMA0B;MANA,CAhCA,EAwCA;QACA9B,yCADA;QAEAC,oBAFA;QAGAC;MAHA,CAxCA,EA6CA;QACAF,mCADA;QAEAC,cAFA;QAGAC,SAHA;QAIAE;MAJA,CA7CA,EAmDA;QACAJ,SADA;QAEAC,kBAFA;QAGAC,YAHA;QAIAE;MAJA,CAnDA,CAxEA;MAkIA8B;QACAC,gBADA;QAEAC,eAFA;QAGAC,kBAHA;QAIAC,SAJA;QAKAC,gBALA;QAMAC;MANA,CAlIA;MA0IAC,kBA1IA;MA2IAC,mBA3IA;MA4IAC,gBA5IA;MA6IAC,mBA7IA;MA8IAC,iBA9IA;MA+IAC,gBA/IA;MAgJAC,kBAhJA;MAiJAC,mBAjJA;MAkJAC,aACA;QACAjD,mCADA;QAEAE,SAFA;QAGAD;MAHA,CADA,EAMA;QACAD,yCADA;QAEAE,SAFA;QAGAD;MAHA,CANA,CAlJA;MA8JAiD,kBACA;QACAlD,yCADA;QAEAC,oBAFA;QAGAC,SAHA;QAIAC,aAJA;QAKAC,YALA;QAMA+C;MANA,CADA,EASA;QACAnD,oCADA;QAEAC,aAFA;QAGAC,SAHA;QAIA8B,UAJA;QAKAC,UALA;QAMA9B,aANA;QAOAC;MAPA,CATA,EAkBA;MACA;MACA;MACA;MACA;MAEA;QACAJ,yCADA;QAEAC,oBAFA;QAGAE,aAHA;QAIAD,SAJA;QAKAE,YALA;QAMA+C;MANA,CAxBA,EAgCA;QACAnD,mCADA;QAEAC,cAFA;QAGAE,cAHA;QAIAD,SAJA;QAKAE;MALA,CAhCA,CA9JA;MAsMAgD,UAtMA;MAuMAC,oBAvMA;MAwMAC,eAxMA;MAyMAC,iBAzMA;MA0MAC,WA1MA;MA2MAC,SA3MA;MA4MAC,SA5MA;MA6MAC,gBA7MA;MA8MAC,aA9MA;MA+MAC,kBA/MA;MAgNAC;IAhNA;EAkNA,CAtNA;;EAuNAC;IACAC;MACA;MACA;MACA,wCAHA,CAIA;MACA;MACA;MACA;MACA;;MACA;MACA;MACA;IACA,CAbA;;IAcA;MACA;MACA;QACA;UACAC;QADA;QAGA;QACAC;UACA;QACA,CAFA;MAGA,CARA;IASA,CAzBA;;IA0BAC;MACA;QACA;MACA;;MACA,gBAJA,CAKA;;MACA;QACA;MACA;;MACA;QACA;UACAtE;QACA;MACA,CAJA,EATA,CAcA;;MACA;IACA,CA1CA;;IA2CAuE;MACA;MACAC;MACA;IACA,CA/CA;;IAgDA;MACA;QACAJ;MADA;MAGA;MACA;MACA;IACA,CAvDA;;IAwDAK;MACA;QACA;QACA;UACA;YACAzE;UACA;QACA,CAJA;QAKA;MACA;IACA,CAlEA;;IAmEA0E;MACA;MACA;MACA;MACA;MACA;IACA,CAzEA;;IA0EAC;MACA;MACA;MACA;MACA;MACA;QACAC;MACA,CAFA;MAGA;MACA;IACA,CApFA;;IAqFA;MACA;QACA;MACA;;MACA;QACAC,6BADA;QAEAC,gCAFA;QAGAC,gCAHA;QAIApD,mCAJA;QAKAqD,6BALA;QAMAjC;MANA;MAQA;MACA;MACA;;MACA;QACA;MACA;;MACA;MACAkC;MACA;MACAC;IACA,CA3GA;;IA4GA;MACA;QACAC;MADA;MAGA;MACA;MACA;MACA;IACA,CApHA;;IAqHA;MACA;QACA;UACA,uDADA,CAEA;UACA;;UACA;;UACA;YACAlF;cACAmF,4BADA,CAEA;;cACAA;YACA,CAJA;UAKA;;UACA;UACA;QACA;MACA;IACA,CAvIA;;IAwIA;MACA;QACA;QACA;UACAhF;QADA,EAFA,CAKA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA,CAvJA;;IAwJA;MACA;MACA;QACAiF,iCADA;QAEAC,iCAFA;QAGAC,oBAHA;QAIAzB;MAJA;MAMA;;MACA;QACA0B;UACAC,gBADA;UAEAlF;QAFA;MAIA,CALA,MAKA;QACA;QACA;MACA;IACA,CA1KA;;IA2KA;MACA;MACA;QACA8E,uCADA;QAEAC,iCAFA;QAGAC,oBAHA;QAIAzB;MAJA;MAMA;;MACA;QACA0B;UACAC,gBADA;UAEAlF;QAFA;MAIA,CALA,MAKA;QACA;QACA;MACA;IACA,CA7LA;;IA8LA;MACA;QACA;MACA;;MACA;QACAmF,gCADA;QAEAjC,6BAFA;QAGAsB,gCAHA;QAIApD,mCAJA;QAKAqD,6BALA;QAMAjC;MANA;MAQA;MACA;MACA;MACA;QACA2C,gCADA;QAEAjC,6BAFA;QAGAkC,2BAHA;QAIAZ,gCAJA;QAKApD;MALA;MAOA;MACA;MACA;;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA;;MACA;MACAsD;MACA;MACAC,yEAjCA,CAkCA;IACA,CAjOA;;IAkOA;IACA;IACA;IACAU;MACA;;MACA;QACAC;MACA;IACA,CA1OA;;IA2OA;MACA;MACAL;QACAC,gBADA;QAEAlF;MAFA;IAIA,CAjPA;;IAkPA;MACA;QACA;UACA;QACA;MACA,CAJA;;MAKA;QACAiF;UACAC,yCADA;UAEAlF;QAFA;QAIA;MACA;;MACA;QACAuF,6CADA;QAEAC,WAFA;QAGAC,kBAHA;QAIAC;MAJA;MAMA;QACA;UACAC;QACA,CAFA,MAEA;UACAA;QACA;MACA,CANA;MAOAA;;MACA;QACAV;UACAC,+CADA;UAEAlF;QAFA;QAIA;MACA;;MACA;MACA;MACA;MACAiF;QACAC,gBADA;QAEAlF;MAFA;IAIA,CA3RA;;IA6RA;MACA;QACA4F,0CADA;QAEA5C;MAFA;MAIA;MACA;MACA;MACA;MACAiC;QACAC,gBADA;QAEAlF;MAFA;IAIA,CA1SA;;IA2SA;MACA;QACAiF;UACAC,yCADA;UAEAlF;QAFA;QAIA;MACA;;MACA;QACAuF,6CADA;QAEA/C,qBAFA;QAGAqD;MAHA;MAKA;MACA;MACA;MACAZ;QACAC,gBADA;QAEAlF;MAFA;IAIA,CA/TA;;IAgUA8F;MACA;IACA,CAlUA;;IAmUA;MACA;QACAb;UACAC,yCADA;UAEAlF;QAFA;QAIA;MACA;;MACA;QACA;UACA;YACA;UACA;QACA,CAJA;;QAKA;UACAiF;YACAC,yCADA;YAEAlF;UAFA;UAIA;QACA;MACA;;MACA;QACAuF,6CADA;QAEA/C,qBAFA;QAGAqD,2BAHA;QAIAE,yCAJA;QAKAC;MALA;MAOA;MACA;MACA;MACA;;MACA;QACAC;UACAC,uBADA;UAEAC;QAFA;MAIA,CALA,MAKA;QACAlB;UACAC,gBADA;UAEAlF;QAFA;MAIA;IACA,CA/WA;;IAgXAoG;MACA;MACA;MACA;MACA;QACA/B;MACA,CAFA,EAJA,CAOA;;MACA;QACA;UACA;YACAA;UACA;QACA;MACA,CANA;MAOA;MACA;MACA;;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA,CAFA,MAEA;QACA;MACA;;MACA;QACA;UACA;;UACA;YACAgC;YACAA;YACA;UACA;QACA;MACA,CATA;MAUA;MACA;IACA,CArZA;;IAsZAC;MACA;MACA;IACA,CAzZA;;IA0ZAC;MACA;QACAlC;;QACA;UACAA;UACAA;QACA;MACA,CANA;MAOA;QACAA;MACA,CAFA;MAGA;MACA,+DAZA,CAaA;;MACA;MACA;MACA;IACA,CA3aA;;IA4aAmC;MACA;QACAnC;MACA,CAFA;MAGA;MACA;MACA;IACA,CAnbA;;IAobA;MACA;QACAkB,6CADA;QAEAkB,mCAFA;QAGAvD;MAHA;MAKA;MACA;MACA;;MACA;QACA+C;UACAC,6CADA;UAEAC;QAFA;MAIA,CALA,MAKA;QACAlB;UACAC,gBADA;UAEAlF;QAFA;MAIA,CAnBA,CAoBA;MACA;MACA;MACA;;IACA,CA5cA;;IA6cA0G;MACA;MACA;MACA;MACA;MACA;MACA;QACArC;MACA,CAFA;;MAGA;QACA;MACA,CAFA,MAEA;QACA;MACA,CAFA,MAEA;QACA;MACA;IACA,CA7dA;;IA8dAsC;MACA;QACA;UACA;QACA,CAFA,MAEA;UACA;QACA;MACA;IACA,CAteA;;IAueAC;MACA;QACA;UACA;QACA,CAFA,MAEA;UACA;QACA;MACA;IACA,CA/eA;;IAgfAC;MACA;MACA;MACA;MACAC,uCAJA,CAIA;;MACAA,6CALA,CAKA;;MACA;IACA,CAvfA;;IAwfAC;MACA;QACA;UAAA;YACA9C;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;MApCA;IAsCA,CA/hBA;;IAgiBA+C;MACA;QACA,qCADA,CAEA;;QACA;QACA;MACA,CALA,MAKA;QACA;QACA;MACA;;MACA;MACA;QACA;UACA3C;QACA,CAFA,MAEA;UACAA;QACA,CAFA,MAEA;UACAA;UACAA;UACAA;QACA,CAJA,MAIA;UACAA;QACA,CAFA,MAEA;UACAA;QACA;MACA,CAdA;MAeA;MACA,oBA3BA,CA4BA;IACA,CA7jBA;;IA8jBA;MACA;MACA;;MACA;QACA;MACA;IACA,CApkBA;;IAqkBA;MACA;QACA;QACA;MACA;;MACA;QACA;UACA;QACA;MACA,CAJA;;MAKA;QACAY;UACAC,yCADA;UAEAlF;QAFA;QAIA;MACA;;MACA;QACAiH,oCADA;QAEAC,oDAFA;QAGAtC,0CAHA;QAIAuC,WAJA;QAKA3B,WALA;QAMAtC,6BANA;QAOAuB,aAPA;QAQAgB,kBARA;QASAC;MATA;;MAWA;QACAC;MACA,CAFA,MAEA;QACAA;MACA;;MACA;QACA;UACAA;QACA,CAFA,MAEA;UACAA;QACA;MACA,CANA,EAjCA,CAwCA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MACA;;MACA;QACA7B;MACA,CAFA,MAEA;QACAA;MACA;;MACA;MACA;MACA;MACAmB;QACAC,gBADA;QAEAlF;MAFA;IAIA,CAloBA;;IAmoBAoH;MACA;QACA;QACA;QACA;MACA,CAJA,MAIA;QACA;QACA;MACA;IACA,CA5oBA;;IA6oBAC;MACA;MACA;MACA;IACA,CAjpBA;;IAkpBAC;MACA;MACA;MACA;IACA;;EAtpBA;AAvNA", "names": ["GetProcessOrderView", "GetProcessOrderView2", "GetBBatchListView", "GetBatchCode", "PoProducedStart", "PoProducedStop", "PoProducedHold", "PoProducedResume", "PoProducedUpdatePo", "AutoReport", "UpdateOrderRemark", "PoExecutionHistroy", "GetRunOrder", "GetEquipmentProcessOrderView", "StartNextBatch", "name", "data", "Completelist", "label", "id", "value", "require", "type", "options", "key", "viewtitle", "timepicker", "tableId", "runningCode", "header", "Activeheader", "Historyheader", "ActivePOManagemenList", "Activenum", "AvailablePOManagemenList", "Availablenum", "HistroyList", "Histroynum", "searchlist", "pageOptions", "total", "page", "pageSize", "pageCount", "pageSizeitems", "activeName", "StartModel", "Startlist", "disabled", "option", "value2", "value3", "chooseItem", "ProcessOrder", "isResume", "TargetQuantity", "Unit1", "MaterialCode", "MaterialName", "tablechooselist", "selectTabelData", "stopModel", "EndTime", "isComplete", "HoldModel", "UpdateModel", "UpdateRemark", "Updatelist", "Updateinputlist", "datetype", "Remark", "ExecutionhStatus", "EquipmentId", "EquipmentCode", "IsPack", "Text1", "Text2", "productionId", "isEdit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ReasonList", "methods", "getEquipmentModal", "ItemCode", "res", "getReasonName", "calculateDate", "date", "getExecutionhStatus", "<PERSON><PERSON>ch", "<PERSON><PERSON><PERSON>y", "item", "Key", "RunEquipmentId", "pageIndex", "StartTime", "el", "el3", "PoSegmentRequirementId", "item1", "LineCode", "equipmentCode", "productionDate", "Message", "message", "Search", "ExecutionStatus", "changePagination", "el2", "ExecutionId", "LotCode", "ProductionDate", "ExpirationDate", "params", "ID", "IsComplete", "ResumeBtn", "ProduceStatus", "Reason", "MessageBox", "confirmButtonText", "callback", "stopBtn", "x", "holdBtn", "updateBtn", "updateRemarkBtn", "Number", "handleClick", "GetDate", "GetDate2", "addDays", "newDate", "DateAdd", "startOrder", "SegmentId", "ProductionOrderId", "BatchId", "handleSelectionChange", "handleSizeChange", "handleCurrentChange"], "sourceRoot": "src/views/Producting/Overview/components", "sources": ["POManagement.vue"], "sourcesContent": ["<template>\r\n    <div class=\"usemystyle POManagement\">\r\n        <div class=\"subsubtabs\">\r\n            <el-tabs v-model=\"activeName\" type=\"border-card\" @tab-click=\"handleClick\">\r\n                <el-tab-pane :label=\"$t('Overview.AvailableOrders') + `(${Availablenum})`\" name=\"Available\">\r\n                    <div class=\"InventorySearchBox\">\r\n                        <div class=\"searchbox\">\r\n                            <div class=\"datebox\">\r\n                                <div class=\"datepickbox\">\r\n                                    <el-date-picker\r\n                                        v-model=\"timepicker\"\r\n                                        type=\"daterange\"\r\n                                        value-format=\"yyyy-MM-dd\"\r\n                                        range-separator=\"-\"\r\n                                        :start-placeholder=\"$t('DFM_RL._KSRQ')\"\r\n                                        :end-placeholder=\"$t('DFM_RL._JSRQ')\"\r\n                                    ></el-date-picker>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"inputformbox\" :style=\"{ width: item.width }\" v-for=\"(item, index) in searchlist\" :key=\"index\">\r\n                                <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\" :myid=\"item.id\" :placeholder=\"item.name\"></el-input>\r\n                                <el-select :style=\"{ width: item.width }\" v-model=\"item.value\" v-if=\"item.type == 'select'\" :myid=\"item.id\" :placeholder=\"item.name\">\r\n                                    <el-option v-for=\"(it, ind) in item.option\" :key=\"ind\" :label=\"it.label\" :value=\"it.value\"></el-option>\r\n                                </el-select>\r\n                            </div>\r\n                            <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                            <el-button size=\"small\" style=\"margin-left: 5px\" icon=\"el-icon-s-help\" @click=\"getempty()\">{{ this.$t('GLOBAL._CZ') }}</el-button>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"tablebox\">\r\n                        <el-table border :data=\"AvailablePOManagemenList\" style=\"width: 100%\" height=\"520\">\r\n                            <el-table-column\r\n                                v-for=\"(item, index) in header\"\r\n                                :key=\"index\"\r\n                                :align=\"item.align\"\r\n                                :prop=\"item.prop ? item.prop : item.value\"\r\n                                :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                                :width=\"item.width\"\r\n                            >\r\n                                <template slot-scope=\"scope\">\r\n                                    <span v-if=\"scope.column.property == 'operate'\">\r\n                                        <el-button\r\n                                            size=\"mini\"\r\n                                            class=\"operatebtn\"\r\n                                            v-if=\"scope.row.ExecutionStatus == null || scope.row.ExecutionStatus == 3\"\r\n                                            @click=\"startOrder(scope)\"\r\n                                            icon=\"el-icon-video-play\"\r\n                                        >\r\n                                            {{ $t('Overview.start') }}\r\n                                        </el-button>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'PlanStartTime'\">{{ $dayjs(scope.row.PlanStartTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                                    <span v-else-if=\"scope.column.property == 'PlanEndTime'\">{{ $dayjs(scope.row.PlanEndTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                                    <span v-else-if=\"scope.column.property == 'Segment'\">\r\n                                        <div>{{ scope.row.SegmentCode }}</div>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'IsHavePreservative'\">\r\n                                        <i :class=\"scope.row[item.value] === '1' ? 'el-icon-star-on' : ''\"></i>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'LineNominalSpeed'\">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>\r\n                                    <span v-else>{{ scope.row[item.prop] }}</span>\r\n                                </template>\r\n                            </el-table-column>\r\n                        </el-table>\r\n                    </div>\r\n                </el-tab-pane>\r\n                <el-tab-pane :label=\"$t('Overview.ActiveOrders') + `(${Activenum})`\" name=\"Active\">\r\n                    <div class=\"InventorySearchBox\">\r\n                        <div class=\"searchbox\">\r\n                            <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                            <el-button\r\n                                class=\"tablebtn\"\r\n                                :disabled=\"tablechooselist > 0 ? false : true\"\r\n                                size=\"small\"\r\n                                @click=\"stopBtn()\"\r\n                                style=\"margin-left: 5px; width: 12vh\"\r\n                                icon=\"el-icon-circle-close\"\r\n                            >\r\n                                {{ this.$t('Overview.Stop') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <!-- <el-button class=\"tablebtn\" @click=\"holdBtn()\" :disabled=\"tablechooselist > 0 ? false : true\" size=\"small\" style=\"margin-left: 5px; width: 12vh\" icon=\"el-icon-video-pause\">\r\n                                {{ this.$t('Overview.Hold') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <el-button\r\n                                class=\"tablebtn\"\r\n                                @click=\"ResumeBtn()\"\r\n                                :disabled=\"tablechooselist > 0 ? false : true\"\r\n                                size=\"small\"\r\n                                style=\"margin-left: 5px; width: 14vh\"\r\n                                icon=\"el-icon-video-play\"\r\n                            >\r\n                                {{ this.$t('Overview.Resume') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button> -->\r\n                            <el-button class=\"tablebtn\" @click=\"updateBtn()\" :disabled=\"tablechooselist > 0 ? false : true\" size=\"small\" style=\"margin-left: 5px; width: 16vh\" icon=\"el-icon-setting\">\r\n                                {{ this.$t('Overview.UpdateOrder') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <el-button\r\n                                class=\"tablebtn\"\r\n                                @click=\"updateRemarkBtn()\"\r\n                                :disabled=\"tablechooselist > 0 ? false : true\"\r\n                                size=\"small\"\r\n                                style=\"margin-left: 5px; width: 16vh\"\r\n                                icon=\"el-icon-setting\"\r\n                            >\r\n                                {{ this.$t('Overview.UpdateRemark') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <!-- <el-button\r\n                                class=\"tablebtn\"\r\n                                @click=\"startNextBatchBtn()\"\r\n                                :disabled=\"tablechooselist > 0 && Number(selectTabelData.Number) < selectTabelData.BatchCount ? false : true\"\r\n                                size=\"small\"\r\n                                style=\"margin-left: 5px; width: 16vh\"\r\n                                icon=\"el-icon-setting\"\r\n                            >\r\n                                {{ this.$t('Overview.NextBatch') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button>\r\n                            <el-button class=\"tablebtn\" @click=\"AutoReport()\" :disabled=\"tablechooselist > 0 ? false : true\" size=\"small\" style=\"margin-left: 5px; width: 16vh\" icon=\"el-icon-setting\">\r\n                                {{ this.$t('Overview.AutoReport') }}{{ tablechooselist == 0 ? '' : `(${tablechooselist})` }}\r\n                            </el-button> -->\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"tablebox\">\r\n                        <el-table border :data=\"ActivePOManagemenList\" ref=\"ActiveTable\" highlight-current-row @current-change=\"handleSelectionChange\" style=\"width: 100%\" height=\"520\">\r\n                            <!-- <el-table-column type=\"selection\" width=\"55\" fixed=\"left\"></el-table-column> -->\r\n                            <el-table-column\r\n                                v-for=\"(item, index) in Activeheader\"\r\n                                :key=\"index\"\r\n                                :align=\"item.align\"\r\n                                :prop=\"item.prop ? item.prop : item.value\"\r\n                                :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                                :width=\"item.width\"\r\n                            >\r\n                                <template slot-scope=\"scope\">\r\n                                    <span v-if=\"scope.column.property == 'ProcessOrder'\">\r\n                                        <div>{{ scope.row.ProcessOrder }}({{ scope.row.Number }})</div>\r\n                                    </span>\r\n                                    <!-- <span v-else-if=\"scope.column.property == 'Status'\">\r\n                                        {{ getExecutionhStatus(scope.row.Status) }}\r\n                                    </span> -->\r\n                                    <!-- <span v-else-if=\"scope.column.property == 'LineNominalSpeed'\">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>-->\r\n                                    <span v-else>{{ scope.row[item.prop] }}</span> \r\n                                </template>\r\n                            </el-table-column>\r\n                        </el-table>\r\n                    </div>\r\n                </el-tab-pane>\r\n\r\n                <el-tab-pane :label=\"$t('Overview.History')\" name=\"History\">\r\n                    <div class=\"InventorySearchBox\">\r\n                        <div class=\"searchbox\">\r\n                            <div class=\"datebox\">\r\n                                <div class=\"datepickbox\">\r\n                                    <el-date-picker\r\n                                        v-model=\"timepicker\"\r\n                                        type=\"daterange\"\r\n                                        value-format=\"yyyy-MM-dd\"\r\n                                        range-separator=\"-\"\r\n                                        :start-placeholder=\"$t('DFM_RL._KSRQ')\"\r\n                                        :end-placeholder=\"$t('DFM_RL._JSRQ')\"\r\n                                    ></el-date-picker>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"inputformbox\" :style=\"{ width: item.width }\" v-for=\"(item, index) in searchlist\" :key=\"index\">\r\n                                <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\" :myid=\"item.id\" :placeholder=\"item.name\"></el-input>\r\n                            </div>\r\n                            <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                            <el-button size=\"small\" style=\"margin-left: 5px\" icon=\"el-icon-s-help\" @click=\"getempty()\">{{ this.$t('GLOBAL._CZ') }}</el-button>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"tablebox\">\r\n                        <el-table border :data=\"HistroyList\" style=\"width: 100%\" height=\"520\">\r\n                            <el-table-column\r\n                                v-for=\"(item, index) in Historyheader\"\r\n                                :key=\"index\"\r\n                                :align=\"item.align\"\r\n                                :prop=\"item.prop ? item.prop : item.value\"\r\n                                :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                                :width=\"item.width\"\r\n                            >\r\n                                <template slot-scope=\"scope\">\r\n                                    <span v-if=\"scope.column.property == 'Material'\">\r\n                                        <div>{{ scope.row.MaterialCode }}</div>\r\n                                        <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'SAP'\">\r\n                                        <div>{{ scope.row.SegmentCode }}</div>\r\n                                        <div style=\"color: #808080\">{{ scope.row.SegmentName }}</div>\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'Status'\">\r\n                                        {{ getExecutionhStatus(scope.row.Status) }}\r\n                                    </span>\r\n                                    <span v-else-if=\"scope.column.property == 'NominalSpeed'\">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>\r\n                                    <span v-else>{{ scope.row[item.prop] }}</span>\r\n                                </template>\r\n                            </el-table-column>\r\n                        </el-table>\r\n                    </div>\r\n                </el-tab-pane>\r\n            </el-tabs>\r\n            <div class=\"paginationbox\">\r\n                <el-pagination\r\n                    @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageOptions.page\"\r\n                    :page-sizes=\"pageOptions.pageSizeitems\"\r\n                    :page-size=\"pageOptions.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next\"\r\n                    :total=\"pageOptions.total\"\r\n                    background\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Overview.StartOrder')\" id=\"Startdialog\" :visible.sync=\"StartModel\" :width=\"IsPack == '0' ? '650px' : '650px'\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">\r\n                    {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.StartOrder') }}\r\n                    <div class=\"dialogsubtitlebox\" style=\"display: inline\">{{ chooseItem.ProcessOrder }}</div>\r\n                </div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailboxtitle\">\r\n                    {{ chooseItem.MaterialCode }}-{{ chooseItem.MaterialName }}\r\n                    <el-tag class=\"splitdetailboxtitleTag\" size=\"small\" v-if=\"Activenum != 0\">{{ ActivePOManagemenList[0] ? ActivePOManagemenList[0].ProcessOrder : '' }}</el-tag>\r\n                </div>\r\n                <div class=\"detailsnote\" v-if=\"runningCode != '' && !chooseItem.isResume\">\r\n                    {{ $t('Overview.Note1') }}\r\n                    <span style=\"font-weight: 600\">{{ runningCode }}</span>\r\n                    {{ $t('Overview.Note2') }}\r\n                </div>\r\n                <div style=\"display: flex\">\r\n                    <div :style=\"{ width: IsPack == '0' ? '100%' : '100%' }\">\r\n                        <div class=\"dialogdetailbox\" v-for=\"(item, index) in Startlist\" :key=\"index\">\r\n                            <div class=\"dialogdetailsinglelabel\" :style=\"{ width: item.type == 'BatchCode' ? '20%' : '20%' }\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                            <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: item.type == 'BatchCode' || item.type == 'checkBox' ? '400px' : '77%' }\">\r\n                                <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                               \r\n                                <div v-else-if=\"item.type == 'BatchCode'\" style=\"display: flex\">\r\n                                    <el-input v-model=\"item.value\"></el-input>\r\n                                    <el-input v-model=\"item.value2\" disabled></el-input>\r\n                                    <el-input v-model=\"item.value3\"></el-input>\r\n                                    <el-button\r\n                                        class=\"tablebtn\"\r\n                                        @click=\"getBatchCode()\"\r\n                                        size=\"mini\"\r\n                                        style=\"margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff\"\r\n                                        icon=\"el-icon-refresh\"\r\n                                    ></el-button>\r\n                                </div>\r\n                                <el-select clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                                    <el-option v-for=\"it in item.option\" :key=\"it.ID\" :label=\"it.Number\" :value=\"it.ID\"></el-option>\r\n                                </el-select>\r\n                                <el-date-picker\r\n                                    @change=\"GetDate(item.id)\"\r\n                                    v-else-if=\"item.type == 'date'\"\r\n                                    value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                                    :disabled=\"item.disabled\"\r\n                                    v-model=\"item.value\"\r\n                                    type=\"datetime\"\r\n                                ></el-date-picker>\r\n                                <span v-else-if=\"item.id == 'TargetQuantity'\">{{ chooseItem.TargetQuantity }}{{ chooseItem.Unit1 }}</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button style=\"float: left\" v-if=\"chooseItem.isResume\">\r\n                    {{ $t('Overview.bottleneck') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" :disabled=\"IsDifferent\" icon=\"el-icon-video-play\" @click=\"ProducedStart()\">\r\n                    {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.Start') }}\r\n                </el-button>\r\n                <el-button @click=\"StartModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog id=\"Stopdialog\" :visible.sync=\"stopModel\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">\r\n                    {{ $t('Overview.StopNote') }}\r\n                </div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailboxtitle\">\r\n                    {{ selectTabelData != {} ? selectTabelData.MaterialName + '-' + selectTabelData.MaterialCode : '' }}\r\n                    <el-tag class=\"splitdetailboxtitleTag\" size=\"small\">{{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</el-tag>\r\n                </div>\r\n                <div class=\"dialogdetailbox\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ $t('Overview.EndTime') }}</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-date-picker disabled v-model=\"EndTime\" type=\"datetime\"></el-date-picker>\r\n                    </div>\r\n                </div>\r\n                <div v-if=\"isComplete == true && selectTabelData.NeedQARelease == '1'\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Completelist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <el-select @change=\"getData2(item)\" v-model=\"item.value\" clearable filterable :disabled=\"item.id == 'ProduceStatus'\" v-if=\"item.type == 'select'\">\r\n                                <el-option v-for=\"(it, ind) in item.options\" :key=\"ind\" :label=\"it.label\" :value=\"it.key\"></el-option>\r\n                            </el-select>\r\n                            <span v-else>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <div style=\"float: left\">\r\n                    <el-checkbox v-model=\"isComplete\">{{ $t('Overview.CompletePO') }}</el-checkbox>\r\n                </div>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-circle-close\" @click=\"StopProduced()\">\r\n                    {{ $t('Overview.Stop') }}\r\n                </el-button>\r\n                <el-button @click=\"stopModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog id=\"Holddialog\" :visible.sync=\"HoldModel\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">\r\n                    {{ $t('Overview.HoldNote') }}\r\n                </div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailboxtitle\">\r\n                    {{ selectTabelData != {} ? selectTabelData.MaterialName + '-' + selectTabelData.MaterialCode : '' }}\r\n                    <el-tag class=\"splitdetailboxtitleTag\" size=\"small\">{{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</el-tag>\r\n                </div>\r\n                <div class=\"dialogdetailbox\">\r\n                    <div class=\"dialogdetailsinglelabel\">{{ $t('Overview.EndTime') }} *</div>\r\n                    <div class=\"dialogdetailsinglevalue\">\r\n                        <el-date-picker disabled v-model=\"EndTime\" type=\"datetime\"></el-date-picker>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <div style=\"float: left\">\r\n                    <el-checkbox v-model=\"isComplete\">{{ $t('Overview.CompletePO') }}</el-checkbox>\r\n                </div>\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-video-pause\" @click=\"HoldProduced()\">\r\n                    {{ $t('Overview.Hold') }}\r\n                </el-button>\r\n                <el-button @click=\"HoldModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog id=\"Updatedialog\" :visible.sync=\"UpdateModel\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">{{ $t('Overview.UpdateNote') }} {{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Updatelist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <span>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Updateinputlist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: item.type == 'BatchCode' ? '20%' : '20%' }\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                        <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: item.type == 'BatchCode' ? '400px' : '77%' }\">\r\n                            <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n\r\n                            <div v-else-if=\"item.type == 'BatchCode'\" style=\"display: flex\">\r\n                                <el-input v-model=\"item.value\"></el-input>\r\n                                <el-input v-model=\"item.value2\" disabled></el-input>\r\n                                <el-input v-model=\"item.value3\"></el-input>\r\n                                <el-button\r\n                                    :disabled=\"Updateinputlist[0].value == ''\"\r\n                                    class=\"tablebtn\"\r\n                                    @click=\"getBatchCode2()\"\r\n                                    size=\"mini\"\r\n                                    style=\"margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff\"\r\n                                    icon=\"el-icon-refresh\"\r\n                                ></el-button>\r\n                            </div>\r\n                            <el-select clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                                <el-option v-for=\"it in item.option\" :key=\"it.ID\" :label=\"it.BatchCode\" :value=\"it.ID\"></el-option>\r\n                            </el-select>\r\n                            <el-date-picker v-else-if=\"item.type == 'date'\" :type=\"item.datetype\" v-model=\"item.value\"></el-date-picker>\r\n                            <!-- <el-date-picker @change=\"GetDate2(item.id)\" v-else-if=\"item.type == 'date'\" :type=\"item.datetype\" v-model=\"item.value\"></el-date-picker> -->\r\n                            <span v-else>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-setting\" @click=\"UpdateProduced()\">\r\n                    {{ $t('Overview.UpdateOrder') }}\r\n                </el-button>\r\n                <el-button @click=\"UpdateModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <el-dialog id=\"Updatedialog2\" :visible.sync=\"UpdateRemark\" width=\"650px\">\r\n            <span slot=\"title\" class=\"dialog-title\">\r\n                <div class=\"dialogtitlebox\">{{ $t('Overview.UpdateNote') }} {{ selectTabelData != {} ? selectTabelData.ProcessOrder : '' }}</div>\r\n            </span>\r\n            <div class=\"splitdetailbox\">\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Updatelist\" :key=\"index\">\r\n                        <div class=\"dialogdetailsinglelabel\">{{ item.label }}</div>\r\n                        <div class=\"dialogdetailsinglevalue\">\r\n                            <span>{{ item.value }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"splitdetailbox\">\r\n                    <div class=\"dialogdetailbox\" key=\"remark\">\r\n                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: '20%' }\">{{ $t('Overview.Comments') }}</div>\r\n                        <div class=\"dialogdetailsinglevalue longwidthinput\" :style=\"{ width: '77%' }\">\r\n                            <el-input v-model=\"Remark\"></el-input>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-setting\" @click=\"UpdateOrderRemark()\">\r\n                    {{ $t('Overview.UpdateRemark') }}\r\n                </el-button>\r\n                <el-button @click=\"UpdateRemark = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { POManagemenAvailable, POManagemenActive, POManagemenHistory } from '@/columns/factoryPlant/tableHeaders';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { GetDataTreeList, GetDataItemList } from '@/api/factoryPlant/process.js';\r\nimport {\r\n    GetProcessOrderView,\r\n    GetProcessOrderView2,\r\n    GetBBatchListView,\r\n    GetBatchCode,\r\n    PoProducedStart,\r\n    PoProducedStop,\r\n    PoProducedHold,\r\n    PoProducedResume,\r\n    PoProducedUpdatePo,\r\n    AutoReport,\r\n    UpdateOrderRemark,\r\n    PoExecutionHistroy,\r\n    GetRunOrder,\r\n    GetEquipmentProcessOrderView,\r\n    StartNextBatch\r\n} from '@/api/Inventory/Overview.js';\r\nimport moment from 'moment';\r\nimport { ConsoleLogger } from '@microsoft/signalr/dist/esm/Utils';\r\n\r\nexport default {\r\n    name: 'POManagement',\r\n\r\n    data() {\r\n        return {\r\n            Completelist: [\r\n                {\r\n                    label: this.$t('POList.PlanQty'),\r\n                    id: 'PlanQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.ActualQty'),\r\n                    id: 'ActualQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.ProduceStatus'),\r\n                    id: 'ProduceStatus',\r\n                    require: true,\r\n                    type: 'select',\r\n                    options: [\r\n                        {\r\n                            key: 'NotComplete',\r\n                            label: this.$t('POList.NotComplete')\r\n                        },\r\n                        {\r\n                            key: 'OverComplete',\r\n                            label: this.$t('POList.OverComplete')\r\n                        },\r\n                        {\r\n                            key: 'CompleteAtOnce',\r\n                            label: this.$t('POList.CompleteAtOnce')\r\n                        }\r\n                    ],\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('POList.Reason'),\r\n                    id: 'Reason',\r\n                    type: 'select',\r\n                    require: true,\r\n                    options: [],\r\n                    value: ''\r\n                }\r\n            ],\r\n            viewtitle: '',\r\n            timepicker: [moment(new Date()).format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')],\r\n            tableId: 'PRO_POManagement',\r\n            runningCode: '',\r\n            header: POManagemenAvailable,\r\n            Activeheader: POManagemenActive,\r\n            Historyheader: POManagemenHistory,\r\n            ActivePOManagemenList: [],\r\n            Activenum: 0,\r\n            AvailablePOManagemenList: [],\r\n            Availablenum: 0,\r\n            HistroyList: [],\r\n            Histroynum: 0,\r\n            searchlist: [\r\n                {\r\n                    type: 'input',\r\n                    name: this.$t('Overview.QuickSearch'),\r\n                    id: 'QuickSearch',\r\n                    value: ''\r\n                }\r\n            ],\r\n            pageOptions: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            activeName: 'Available',\r\n            StartModel: false,\r\n            Startlist: [\r\n                {\r\n                    label: this.$t('Overview.StartTime'),\r\n                    id: 'StartTime',\r\n                    value: '',\r\n                    disabled: true,\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.Batch'),\r\n                    id: 'BatchId',\r\n                    value: '',\r\n                    require: true,\r\n                    type: 'select',\r\n                    option: []\r\n                },\r\n                {\r\n                    label: this.$t('Overview.BatchCode'),\r\n                    id: 'LotCode',\r\n                    value: '',\r\n                    value2: '',\r\n                    value3: '',\r\n                    require: true,\r\n                    type: 'BatchCode'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ExpirationDate'),\r\n                    id: 'ExpirationDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date',\r\n                    disabled: true\r\n                },\r\n                {\r\n                    label: this.$t('Overview.TargetQuantity'),\r\n                    id: 'TargetQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Overview.CrewSize'),\r\n                    id: 'CrewSize',\r\n                    value: '',\r\n                    type: 'input'\r\n                },\r\n                {\r\n                    label: '',\r\n                    id: 'IsUpdateLtxt',\r\n                    value: false,\r\n                    type: 'checkBox'\r\n                }\r\n            ],\r\n            chooseItem: {\r\n                ProcessOrder: '',\r\n                isResume: false,\r\n                TargetQuantity: '',\r\n                Unit1: '',\r\n                MaterialCode: '',\r\n                MaterialName: ''\r\n            },\r\n            tablechooselist: 0,\r\n            selectTabelData: {},\r\n            stopModel: false,\r\n            EndTime: new Date(),\r\n            isComplete: false,\r\n            HoldModel: false,\r\n            UpdateModel: false,\r\n            UpdateRemark: false,\r\n            Updatelist: [\r\n                {\r\n                    label: this.$t('Overview.Material'),\r\n                    value: '',\r\n                    id: 'Material'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.TargetQuantity'),\r\n                    value: '',\r\n                    id: 'TargetQuantity'\r\n                }\r\n            ],\r\n            Updateinputlist: [\r\n                {\r\n                    label: this.$t('Overview.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    value: '',\r\n                    require: true,\r\n                    type: 'date',\r\n                    datetype: 'datetime'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.BatchCode'),\r\n                    id: 'LotCode',\r\n                    value: '',\r\n                    value2: '',\r\n                    value3: '',\r\n                    require: true,\r\n                    type: 'BatchCode'\r\n                },\r\n                // {\r\n                //     label: this.$t('Overview.DefaultBatchCode'),\r\n                //     id: 'DefaultBatchCode',\r\n                //     value: ''\r\n                // },\r\n\r\n                {\r\n                    label: this.$t('Overview.ExpirationDate'),\r\n                    id: 'ExpirationDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date',\r\n                    datetype: 'datetime'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.Comments'),\r\n                    id: 'Comments',\r\n                    require: false,\r\n                    value: '',\r\n                    type: 'input'\r\n                }\r\n            ],\r\n            Remark: '',\r\n            ExecutionhStatus: [],\r\n            EquipmentId: '',\r\n            EquipmentCode: '',\r\n            IsPack: '1',\r\n            Text1: '',\r\n            Text2: '',\r\n            productionId: '',\r\n            isEdit: false,\r\n            IsDifferent: false,\r\n            ReasonList: []\r\n        };\r\n    },\r\n    methods: {\r\n        getEquipmentModal(item, Equipmentitem) {\r\n            this.productionId = item.ProductionOrderId;\r\n            this.EquipmentId = item.ID;\r\n            this.EquipmentCode = item.EquipmentCode;\r\n            // if (Equipmentitem) {\r\n            // } else {\r\n            //     // this.EquipmentId = '';\r\n            //     // this.EquipmentCode = '';\r\n            // }\r\n            this.getStatus();\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        },\r\n        async getStatus() {\r\n            this.ReasonList = [];\r\n            this.Completelist[2].options.forEach(async item => {\r\n                let p = {\r\n                    ItemCode: item.key\r\n                };\r\n                const res = await GetDataTreeList(p);\r\n                res.response.data.forEach(item1 => {\r\n                    this.ReasonList.push(item1);\r\n                });\r\n            });\r\n        },\r\n        getReasonName(key1, key2) {\r\n            if (key1 === null || key1 === '' || key2 === null || key2 === '') {\r\n                return '';\r\n            }\r\n            let name = key2;\r\n            //console.log(this._i18n.locale);\r\n            if (this._i18n.locale == 'en') {\r\n                return name;\r\n            }\r\n            this.ReasonList.forEach(item => {\r\n                if (item.ItemCode === key1 && item.ItemValue === key2) {\r\n                    name = item.ItemName;\r\n                }\r\n            });\r\n            //console.log(name);\r\n            return name;\r\n        },\r\n        calculateDate(days) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + days);\r\n            return this.formatDate(date);\r\n        },\r\n        async getStauts() {\r\n            let params2 = {\r\n                ItemCode: 'ProductionExecutionStatus'\r\n            };\r\n            const res2 = await GetDataItemList(params2);\r\n            let data2 = res2.response;\r\n            this.ExecutionhStatus = data2;\r\n        },\r\n        getExecutionhStatus(val) {\r\n            if (val) {\r\n                let name = '';\r\n                this.ExecutionhStatus.forEach(item => {\r\n                    if (item.ItemValue == val) {\r\n                        name = item.ItemName;\r\n                    }\r\n                });\r\n                return name;\r\n            }\r\n        },\r\n        getsearch() {\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.getStauts();\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        },\r\n        getempty() {\r\n            this.QuickSearch = '';\r\n            this.timepicker = [];\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.searchlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        },\r\n        async getPoExecutionHistroy() {\r\n            if (this.timepicker == null) {\r\n                this.timepicker = [];\r\n            }\r\n            let params = {\r\n                Key: this.searchlist[0].value,\r\n                RunEquipmentId: this.EquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize,\r\n                StartTime: this.timepicker[0],\r\n                EndTime: this.timepicker[1] == undefined ? '' : this.timepicker[1] + ' 23:59:59'\r\n            };\r\n            let res = await PoExecutionHistroy(params);\r\n            this.HistroyList = res.response.data;\r\n            this.Histroynum = res.response.dataCount;\r\n            if (this.activeName == 'History') {\r\n                this.pageOptions.total = this.Histroynum;\r\n            }\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n        },\r\n        async getBatchList() {\r\n            let params = {\r\n                PoSegmentRequirementId: this.chooseItem.ID\r\n            };\r\n            let res = await GetBBatchListView(params);\r\n            this.Startlist[1].option = res.response;\r\n            this.Startlist[1].value = this.Startlist[1].option[0].ID;\r\n            this.StartModel = true;\r\n        },\r\n        async getData2(item) {\r\n            if (item.id == 'ProduceStatus') {\r\n                if (item.value != '') {\r\n                    let res = await this.$getNewDataDictionary(item.value);\r\n                    //let res = this.ReasonList.find(x=>x.ItemCode = item.value)\r\n                    // console.log(res);\r\n                    let data = res;\r\n                    if (data.length > 0) {\r\n                        data.forEach(item1 => {\r\n                            item1.key = item1.ItemValue;\r\n                            // console.log(this._i18n.locale);\r\n                            item1.label = this._i18n.locale === 'en' ? item1.ItemValue : item1.ItemName;\r\n                        });\r\n                    }\r\n                    this.Completelist[3].options = data;\r\n                    this.Completelist[3].value = '';\r\n                }\r\n            }\r\n        },\r\n        async getLtext(id) {\r\n            if (this.IsPack === '0') {\r\n                this.IsDifferent = false;\r\n                let params = {\r\n                    id: id\r\n                };\r\n                // let r = await GetCookOrderLtexts(params);\r\n                // if (r.response.length == 2) {\r\n                //     if (r.msg === '长文本不一致！') {\r\n                //         this.IsDifferent = true;\r\n                //     }\r\n                //     this.Text1 = r.response[0].ProcessData;\r\n                //     this.Text2 = r.response[1].ProcessData;\r\n                // }\r\n            }\r\n        },\r\n        async getBatchCode() {\r\n            let date = moment(this.Startlist[3].value).format('YYYY-MM-DD HH:mm:ss');\r\n            let p = {\r\n                LineCode: this.Startlist[2].value,\r\n                equipmentCode: this.EquipmentCode,\r\n                productionDate: date,\r\n                productionId: this.chooseItem.ProductionOrderId\r\n            };\r\n            let res = await GetBatchCode(p);\r\n            if (res.response == null) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'warning'\r\n                });\r\n            } else {\r\n                this.Startlist[2].value = res.response.substring(0, 2);\r\n                this.Startlist[2].value2 = res.response.substring(2, 5);\r\n            }\r\n        },\r\n        async getBatchCode2() {\r\n            let date = moment(this.Updateinputlist[0].value).format('YYYY-MM-DD HH:mm:ss');\r\n            let p = {\r\n                LineCode: this.Updateinputlist[1].value,\r\n                equipmentCode: this.EquipmentCode,\r\n                productionDate: date,\r\n                productionId: this.selectTabelData.ProductionOrderId\r\n            };\r\n            let res = await GetBatchCode(p);\r\n            if (res.response == null) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'warning'\r\n                });\r\n            } else {\r\n                this.Updateinputlist[1].value = res.response.substring(0, 2);\r\n                this.Updateinputlist[1].value2 = res.response.substring(2, 5);\r\n            }\r\n        },\r\n        async GetProcessOrderView() {\r\n            if (this.timepicker == null) {\r\n                this.timepicker = [];\r\n            }\r\n            let params = {\r\n                Search: this.searchlist[0].value,\r\n                EquipmentId: this.EquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize,\r\n                StartTime: this.timepicker[0],\r\n                EndTime: this.timepicker[1] == undefined ? '' : this.timepicker[1] + ' 23:59:59'\r\n            };\r\n            let res = await GetProcessOrderView(params);\r\n            this.AvailablePOManagemenList = res.response.data;\r\n            this.Availablenum = res.response.dataCount;\r\n            let params2 = {\r\n                Search: this.searchlist[0].value,\r\n                EquipmentId: this.EquipmentId,\r\n                ExecutionStatus: ['1', '5'],\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res2 = await GetProcessOrderView2(params2);\r\n            this.ActivePOManagemenList = res2.response.data;\r\n            this.Activenum = res2.response.dataCount;\r\n            if (this.activeName == 'Active') {\r\n                this.pageOptions.total = this.Activenum;\r\n            } else if (this.activeName == 'Available') {\r\n                this.pageOptions.total = this.Availablenum;\r\n            }\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            // this.getNumTofather();\r\n        },\r\n        // getNumTofather() {\r\n        //     this.$emit('getNum', this.ActivePOManagemenList);\r\n        // },\r\n        changePagination() {\r\n            let el2 = document.getElementsByClassName(`el-select-dropdown__item`);\r\n            for (let i = 0; i < el2.length; i++) {\r\n                el2[i].innerHTML = el2[i].innerHTML.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            }\r\n        },\r\n        async AutoReport() {\r\n            let res = await AutoReport('', 'reportType=Consume&&equipmentCode=' + this.selectTabelData.EquipmentId);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        async UpdateProduced() {\r\n            let flag = this.Updateinputlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                ExecutionId: this.selectTabelData.ExecutionId,\r\n                LotCode: '',\r\n                ProductionDate: '',\r\n                ExpirationDate: ''\r\n            };\r\n            this.Updateinputlist.forEach(item => {\r\n                if (item.id == 'LotCode') {\r\n                    params[item.id] = item.value + item.value2 + item.value3;\r\n                } else {\r\n                    params[item.id] = item.value;\r\n                }\r\n            });\r\n            params.ExpirationDate = moment(params.ExpirationDate).format('YYYY-MM-DD HH:mm:ss');\r\n            if (params.LotCode.length > 10) {\r\n                Message({\r\n                    message: `${this.$t('Overview.BatchCodeLong')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let res = await PoProducedUpdatePo(params);\r\n            this.GetProcessOrderView();\r\n            this.UpdateModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n\r\n        async UpdateOrderRemark() {\r\n            let params = {\r\n                ID: this.selectTabelData.ProductionOrderId,\r\n                Remark: this.Remark\r\n            };\r\n            let res = await UpdateOrderRemark(params);\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n            this.UpdateRemark = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        async HoldProduced() {\r\n            if (this.EndTime == null || this.EndTime == '') {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                ExecutionId: this.selectTabelData.ExecutionId,\r\n                EndTime: this.EndTime,\r\n                IsComplete: this.isComplete\r\n            };\r\n            let res = await PoProducedHold(params);\r\n            this.GetProcessOrderView();\r\n            this.HoldModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        ResumeBtn() {\r\n            this.startOrder(this.selectTabelData);\r\n        },\r\n        async StopProduced() {\r\n            if (this.EndTime == null || this.EndTime == '') {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            if (this.isComplete == true && this.selectTabelData.NeedQARelease == '1') {\r\n                let flag = this.Completelist.some(item => {\r\n                    if (item.require) {\r\n                        return item.value == '' || item.value == null;\r\n                    }\r\n                });\r\n                if (flag) {\r\n                    Message({\r\n                        message: `${this.$t('Inventory.ToOver')}`,\r\n                        type: 'warning'\r\n                    });\r\n                    return;\r\n                }\r\n            }\r\n            let params = {\r\n                ExecutionId: this.selectTabelData.ExecutionId,\r\n                EndTime: this.EndTime,\r\n                IsComplete: this.isComplete,\r\n                ProduceStatus: this.Completelist[2].value,\r\n                Reason: this.Completelist[3].value\r\n            };\r\n            let res = await PoProducedStop(params);\r\n            this.GetProcessOrderView();\r\n            this.$emit('loadProgress', this.EquipmentId);\r\n            this.stopModel = false;\r\n            if (res.msg.includes('需要取样')) {\r\n                MessageBox.alert(`${res.msg}`, '提示!', {\r\n                    confirmButtonText: '确定',\r\n                    callback: action => {}\r\n                });\r\n            } else {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            }\r\n        },\r\n        stopBtn() {\r\n            this.selectTabelData.Material = this.selectTabelData.MaterialName + '-' + this.selectTabelData.MaterialCode;\r\n            this.selectTabelData.PlanQuantity = this.selectTabelData.TargetQuantity + this.selectTabelData.Unit1;\r\n            this.selectTabelData.ActualQuantity = this.selectTabelData.ActualQty + this.selectTabelData.Unit1;\r\n            this.Completelist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            //console.log(this.Completelist[4]);\r\n            this.Completelist.forEach(item => {\r\n                for (let k in this.selectTabelData) {\r\n                    if (item.id == k) {\r\n                        item.value = this.selectTabelData[k];\r\n                    }\r\n                }\r\n            });\r\n            this.Completelist[3].options = [];\r\n            let PlanQuantity = Number(this.selectTabelData.TargetQuantity);\r\n            let ActualQuantity = Number(this.selectTabelData.ActualQty);\r\n            if (PlanQuantity > ActualQuantity) {\r\n                this.Completelist[2].value = 'NotComplete';\r\n            } else if (PlanQuantity == ActualQuantity) {\r\n                this.Completelist[2].value = 'CompleteAtOnce';\r\n            } else {\r\n                this.Completelist[2].value = 'OverComplete';\r\n            }\r\n            this.ReasonList.forEach(x => {\r\n                if (x.ItemCode == this.Completelist[2].value) {\r\n                    let res = this.Completelist[3].options.find(x1 => x1.ItemCode === this.Completelist[3].value && x1.ItemValue === x.ItemValue);\r\n                    if (res === null || res === undefined || typeof res === 'undefined') {\r\n                        x.key = x.ItemValue;\r\n                        x.label = this._i18n.locale === 'en' ? x.ItemValue : x.ItemName;\r\n                        this.Completelist[3].options.push(x);\r\n                    }\r\n                }\r\n            });\r\n            this.EndTime = new Date();\r\n            this.stopModel = true;\r\n        },\r\n        holdBtn() {\r\n            this.EndTime = new Date();\r\n            this.HoldModel = true;\r\n        },\r\n        updateBtn() {\r\n            this.Updateinputlist.forEach(item => {\r\n                item.value = '';\r\n                if (item.id == 'LotCode') {\r\n                    item.value2 = '';\r\n                    item.value3 = '';\r\n                }\r\n            });\r\n            this.Updatelist.forEach(item => {\r\n                item.value = this.selectTabelData[item.id];\r\n            });\r\n            this.Updatelist[1].value += this.selectTabelData.Unit1;\r\n            this.Updateinputlist[0].value = this.selectTabelData.StartTime;\r\n            //this.Updateinputlist[1].value2 = this.selectTabelData.BatchCode;\r\n            this.Updateinputlist[2].value = this.selectTabelData.ExpirationDate;\r\n            this.getBatchCode2();\r\n            this.UpdateModel = true;\r\n        },\r\n        updateRemarkBtn() {\r\n            this.Updatelist.forEach(item => {\r\n                item.value = this.selectTabelData[item.id];\r\n            });\r\n            this.Updatelist[1].value += this.selectTabelData.Unit1;\r\n            this.Remark = this.selectTabelData.Remark;\r\n            this.UpdateRemark = true;\r\n        },\r\n        async startNextBatchBtn() {\r\n            let params = {\r\n                ExecutionId: this.selectTabelData.ExecutionId,\r\n                Number: this.selectTabelData.Number,\r\n                EquipmentId: this.selectTabelData.EquipmentId\r\n            };\r\n            let res = await StartNextBatch(params);\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n            if (res.msg.includes('需要取样')) {\r\n                MessageBox.alert(`${res.msg}`, '提示!', {\r\n                    confirmButtonText: `${this.$t('GLOBAL._QD')}`,\r\n                    callback: action => {}\r\n                });\r\n            } else {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n            }\r\n            // Message({\r\n            //     message: res.msg,\r\n            //     type: 'success'\r\n            // });\r\n        },\r\n        handleClick(key) {\r\n            this.QuickSearch = '';\r\n            this.timepicker = [];\r\n            this.pageOptions.page = 1;\r\n            this.pageOptions.pageSize = 20;\r\n            this.$refs.ActiveTable.setCurrentRow(null);\r\n            this.searchlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            if (this.activeName == 'Active') {\r\n                this.pageOptions.total = this.Activenum;\r\n            } else if (this.activeName == 'Available') {\r\n                this.pageOptions.total = this.Availablenum;\r\n            } else {\r\n                this.pageOptions.total = this.Histroynum;\r\n            }\r\n        },\r\n        GetDate(id) {\r\n            if (id == 'ProductionDate') {\r\n                if (this.chooseItem.NeedQARelease == '1') {\r\n                    this.Startlist[4].value = this.addDays(this.Startlist[3].value, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);\r\n                } else {\r\n                    this.Startlist[4].value = this.addDays(this.chooseItem.PlanStartTime, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);\r\n                }\r\n            }\r\n        },\r\n        GetDate2(id) {\r\n            if (id == 'ProductionDate') {\r\n                if (this.selectTabelData.NeedQARelease == '1') {\r\n                    this.Updateinputlist[2].value = this.addDays(this.Updateinputlist[0].value, this.selectTabelData.Mhdhb, this.selectTabelData.Iprkz);\r\n                } else {\r\n                    this.Updateinputlist[2].value = this.addDays(this.selectTabelData.PlanStartTime, this.selectTabelData.Mhdhb, this.selectTabelData.Iprkz);\r\n                }\r\n            }\r\n        },\r\n        addDays(date, number, interval) {\r\n            const newDate1 = new Date(date);\r\n            const newDate = new Date(newDate1.getFullYear(), newDate1.getMonth(), newDate1.getDate());\r\n            this.DateAdd(interval, number, newDate);\r\n            newDate.setDate(newDate.getDate() + 1); // 增加一天\r\n            newDate.setSeconds(newDate.getSeconds() - 1); // 减去1秒\r\n            return newDate;\r\n        },\r\n        DateAdd(interval, number, date) {\r\n            switch (interval) {\r\n                case 'Y': {\r\n                    date.setFullYear(date.getFullYear() + number);\r\n                    return date;\r\n                }\r\n                case 'Q': {\r\n                    date.setMonth(date.getMonth() + number * 3);\r\n                    return date;\r\n                }\r\n                case 'M': {\r\n                    date.setMonth(date.getMonth() + number);\r\n                    return date;\r\n                }\r\n                case 'W': {\r\n                    date.setDate(date.getDate() + number * 7);\r\n                    return date;\r\n                }\r\n                case 'D': {\r\n                    date.setDate(date.getDate() + number);\r\n                    return date;\r\n                }\r\n                case 'h': {\r\n                    date.setHours(date.getHours() + number);\r\n                    return date;\r\n                }\r\n                case 'm': {\r\n                    date.setMinutes(date.getMinutes() + number);\r\n                    return date;\r\n                }\r\n                case 's': {\r\n                    date.setSeconds(date.getSeconds() + number);\r\n                    return date;\r\n                }\r\n                default: {\r\n                    date.setDate(date.getDate() + number);\r\n                    return date;\r\n                }\r\n            }\r\n        },\r\n        startOrder(item) {\r\n            if (item.row) {\r\n                this.IsPack = item.row.NeedQARelease;\r\n                //this.getLtext(item.row.ProductionOrderId);\r\n                this.chooseItem = item.row;\r\n                this.chooseItem.isResume = false;\r\n            } else {\r\n                this.chooseItem = item;\r\n                this.chooseItem.isResume = true;\r\n            }\r\n            this.MyGetRunOrder();\r\n            this.Startlist.forEach((item, index) => {\r\n                if (index == 0) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 3) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 2) {\r\n                    item.value = '';\r\n                    item.value2 = '';\r\n                    item.value3 = '';\r\n                } else if (item.id == 'IsUpdateLtxt') {\r\n                    item.value = false;\r\n                } else {\r\n                    item.value = '';\r\n                }\r\n            });\r\n            this.GetDate('ProductionDate');\r\n            this.getBatchList();\r\n            //this.getBatchCode();\r\n        },\r\n        async MyGetRunOrder() {\r\n            let res = await GetRunOrder('', this.EquipmentId);\r\n            this.runningCode = res.response;\r\n            if (this.runningCode == null) {\r\n                this.runningCode = '';\r\n            }\r\n        },\r\n        async ProducedStart() {\r\n            if (this.IsDifferent == true) {\r\n                this.$message.warning('工艺长文本对比不通过,不允许启动工单！');\r\n                return;\r\n            }\r\n            let flag = this.Startlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                SegmentId: this.chooseItem.SegmentId,\r\n                ProductionOrderId: this.chooseItem.ProductionOrderId,\r\n                PoSegmentRequirementId: this.chooseItem.ID,\r\n                BatchId: '',\r\n                LotCode: '',\r\n                EquipmentId: this.EquipmentId,\r\n                StartTime: '',\r\n                ProductionDate: '',\r\n                ExpirationDate: ''\r\n            };\r\n            if (this.chooseItem.isResume == true) {\r\n                params.ExecutionId = this.chooseItem.ExecutionId;\r\n            } else {\r\n                params.ExecutionId = '';\r\n            }\r\n            this.Startlist.forEach(item => {\r\n                if (item.id == 'LotCode') {\r\n                    params[item.id] = item.value + item.value2 + item.value3;\r\n                } else {\r\n                    params[item.id] = item.value;\r\n                }\r\n            });\r\n            //params.ExpirationDate = moment(params.ExpirationDate).format('YYYY-MM-DD HH:mm:ss');\r\n            // if (params.LotCode.length > 10) {\r\n            //     Message({\r\n            //         message: `${this.$t('Overview.BatchCodeLong')}`,\r\n            //         type: 'warning'\r\n            //     });\r\n            //     return;\r\n            // }\r\n            let res;\r\n            if (this.chooseItem.isResume == true) {\r\n                res = await PoProducedResume(params);\r\n            } else {\r\n                res = await PoProducedStart(params);\r\n            }\r\n            this.GetProcessOrderView();\r\n            this.$emit('loadProgress', this.EquipmentId);\r\n            this.StartModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        handleSelectionChange(val) {\r\n            if (val != null) {\r\n                this.selectTabelData = val;\r\n                this.selectTabelData.Material = this.selectTabelData.MaterialName + '-' + this.selectTabelData.MaterialCode;\r\n                this.tablechooselist = 1;\r\n            } else {\r\n                this.selectTabelData = {};\r\n                this.tablechooselist = 0;\r\n            }\r\n        },\r\n        handleSizeChange(val) {\r\n            this.pageOptions.pageSize = val;\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.pageOptions.page = val;\r\n            this.GetProcessOrderView();\r\n            this.getPoExecutionHistroy();\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.POManagement {\r\n    .searchboxtitle {\r\n        font-size: 1.7vh;\r\n        color: #767777;\r\n        padding-bottom: 5px;\r\n        margin-left: 10px;\r\n    }\r\n    .el-tabs {\r\n        height: 97%;\r\n    }\r\n    .subsubtabs {\r\n        .el-tabs--border-card {\r\n            border: 0 !important;\r\n            box-shadow: none !important;\r\n        }\r\n    }\r\n    .paginationbox {\r\n        height: 10vh;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin-top: 10px;\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 47%;\r\n            text-align: right;\r\n        }\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n    .splitdetailbox {\r\n        padding-bottom: 10px;\r\n        border: 1px solid #e8e8e8;\r\n        margin-bottom: 5px;\r\n        .splitdetailboxtitle {\r\n            background: #f5f5f5;\r\n            height: 3.5vh;\r\n            display: flex;\r\n            align-items: center;\r\n            padding-left: 5px;\r\n            font-size: 1.1rem;\r\n            color: #303133;\r\n        }\r\n        .detailsnote {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 0.9rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .detailsnote2 {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 1.2rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .detailsnote3 {\r\n            background-color: #f5fdec;\r\n            border-color: #faecd8;\r\n            color: hsl(135, 55%, 44%);\r\n            padding: 8px;\r\n            font-size: 1.2rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .splitdetailboxtitleTag {\r\n            margin-left: 5px;\r\n            background: #5cb85c;\r\n            color: #fff;\r\n            border-color: #5cb85c;\r\n        }\r\n    }\r\n}\r\n.el-dialog__body {\r\n    .el-input {\r\n        width: 250px !important;\r\n    }\r\n    .longwidthinput {\r\n        .el-input {\r\n            width: 400px !important;\r\n        }\r\n        .el-select {\r\n            width: 400px !important;\r\n        }\r\n    }\r\n    .el-select {\r\n        width: 250px !important;\r\n    }\r\n}\r\n</style>\r\n"]}]}