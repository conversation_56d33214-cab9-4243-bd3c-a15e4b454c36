{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\index.vue", "mtime": 1750254216296}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA0TA;AACA;AACA,SACAA,2BADA,EAEAC,mBAFA,EAGAC,wBAHA,EAIAC,4BAJA,EAKAC,mBALA,EAMAC,oBANA,EAOAC,iBAPA,EAQAC,YARA,EASAC,eATA,EAUAC,gBAVA,EAWAC,WAXA,EAYAC,kBAZA,EAaAC,+BAbA,EAcAC,SAdA,QAeA,6BAfA;AAgBA;AACA;AAEA;AACA;AACA;EACAC;IACAC,iEADA;IAEAC,uDAFA;IAGAC,6CAHA;IAIAC,6CAJA;IAKAC,6CALA;IAMAC,6CANA;IAOAC,qDAPA;IAQAC,uDARA;IASAC,2DATA;IAUAC,iDAVA;IAWAC,qDAXA;IAYAC,2DAZA;IAaAC,yDAbA;IAcAC;EAdA,CADA;;EAiBAC;IACA;MACAC,aADA;MAEAC,cAFA;MAGAC,4BAHA;MAIAC,kCAJA;MAKAC,iBALA;MAMAC,eANA;MAOAC,iBAPA;MAQAC,mBARA;MASAC,YACA;QACAC,oCADA;QAEAC,eAFA;QAGAC,SAHA;QAIAC,cAJA;QAKAC;MALA,CADA,EAQA;QACAJ,gCADA;QAEAC,aAFA;QAGAC,SAHA;QAIAG,aAJA;QAKAD,cALA;QAMAE;MANA,CARA,EAgBA;QACAN,oCADA;QAEAC,aAFA;QAGAC,SAHA;QAIAK,UAJA;QAKAC,UALA;QAMAH,aANA;QAOAD;MAPA,CAhBA,EAyBA;QACAJ,yCADA;QAEAC,oBAFA;QAGAI,aAHA;QAIAH,SAJA;QAKAE;MALA,CAzBA,EAgCA;QACAJ,yCADA;QAEAC,oBAFA;QAGAI,aAHA;QAIAH,SAJA;QAKAE,YALA;QAMAD;MANA,CAhCA,EAwCA;QACAH,yCADA;QAEAC,oBAFA;QAGAC;MAHA,CAxCA,EA6CA;QACAF,mCADA;QAEAC,cAFA;QAGAC,SAHA;QAIAE;MAJA,CA7CA,EAmDA;QACAJ,SADA;QAEAC,kBAFA;QAGAC,YAHA;QAIAE;MAJA,CAnDA,CATA;MAmEAK,aACA;QACAL,aADA;QAEAM,qCAFA;QAGAT,iBAHA;QAIAC;MAJA,CADA,EAOA;QACAE,cADA;QAEAE,UAFA;QAGAK,aAHA;QAIAD,iCAJA;QAKAT,aALA;QAMAC;MANA,CAPA,EAeA;QACAE,aADA;QAEAF,SAFA;QAGAD,kBAHA;QAIAS;MAJA,CAfA,CAnEA;MAyFAE;QACAC,gBADA;QAEAC,eAFA;QAGAC,kBAHA;QAIAC,SAJA;QAKAC,gBALA;QAMAC;MANA,CAzFA;MAiGAC,aAjGA;MAkGAC,iBAlGA;MAmGAC,oCAnGA;MAoGAC,uBApGA;MAqGAC,2BArGA;MAsGAC;QACAC,QADA;QAEAC,OAFA;QAEA;QACAC,YAHA;QAGA;QACAC,YAJA;QAIA;QACAC;MALA,CAtGA;MA6GAC;QACAL,QADA;QAEAC,OAFA;QAEA;QACAC,YAHA;QAGA;QACAC,YAJA;QAIA;QACAC;MALA,CA7GA;MAoHAE,eApHA;MAqHAC,eArHA;MAsHAC,cAtHA;MAuHAC,uBAvHA;MAwHAC,eAxHA;MAyHAC,kBAzHA;MA0HAC,WA1HA;MA2HAC,eA3HA;MA4HAC,eA5HA;MA6HAC,oBA7HA;MA8HAC,gBA9HA;MA+HAC,aA/HA;MAgIAC,kBAhIA;MAiIAC,iBAjIA;MAkIAC,WAlIA;MAmIAC,SAnIA;MAoIAC;IApIA;EAsIA,CAxJA;;EAyJAC;IACA;IACAC;IACA,kEAHA,CAIA;;IACA,+BALA,CAMA;;IACA;IACA;IACA;EACA,CAnKA;;EAoKAC;IACA;MACA;QACA;UACA;QACA;MACA,CAJA;;MAKA;QACAC;MACA;;MACA;QACAC;UACAC,yCADA;UAEAjD;QAFA;QAIA;MACA;;MACA;QACAkD,oCADA;QAEAC,oDAFA;QAGAC,0CAHA;QAIAnB,WAJA;QAKAoB,WALA;QAMAtB,6BANA;QAOAuB,aAPA;QAQAC,kBARA;QASAC;MATA;;MAWA;QACAC;MACA,CAFA,MAEA;QACAA;MACA;;MACA;QACA;UACAA;QACA,CAFA,MAEA;UACAA;QACA;MACA,CANA;MAOAA;;MACA;QACAT;UACAC,+CADA;UAEAjD;QAFA;QAIA;MACA;;MACA;;MACA;QACA0D;MACA,CAFA,MAEA;QACAA;MACA,CApDA,CAqDA;;;MACA;MACA;MACAV;QACAC,gBADA;QAEAjD;MAFA;IAIA,CA7DA;;IA8DA2D;MACA;QACA;UACA;QACA,CAFA,MAEA;UACA;QACA;MACA;IACA,CAtEA;;IAuEAC;MACA;MACA;MACA;MACAf;;MACA;QACA;QACA;QACA;QACA;MACA,CALA,MAKA;QACA;QACA;MACA;;MACA;QACA;UACAgB;QACA,CAFA,MAEA;UACAA;QACA,CAFA,MAEA;UACAA;UACAA;UACAA;QACA,CAJA,MAIA;UACAA;QACA,CAFA,MAEA;UACAA;QACA;MACA,CAdA;MAeA;MACA;IACA,CAtGA;;IAuGA;MACA;MACA;IACA,CA1GA;;IA2GA;MACA;MACA;QACAC,iCADA;QAEAC,iCAFA;QAGAC,oBAHA;QAIA3B;MAJA;MAMA;;MACA;QACAW;UACAC,gBADA;UAEAjD;QAFA;MAIA,CALA,MAKA;QACA;QACA;MACA;IACA,CA7HA;;IA8HA;MACA;QACAoD;MADA;MAGA;MACA;MACA;MACA;IACA,CAtIA;;IAuIAa;MACA;MACA;MACA;MACAC,uCAJA,CAIA;;MACAA,6CALA,CAKA;;MACA;IACA,CA9IA;;IA+IAC;MACA;QACA;UAAA;YACAC;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;;QACA;UAAA;YACAA;YACA;UACA;MApCA;IAsCA,CAtLA;;IAuLA;MACA;MACA;QACA;UACA;QACA;MACA,CAJA;MAKA;IACA,CA/LA;;IAgMA;MACA;MACA;;MACA;QACA;MACA;IACA,CAtMA;;IAuMA;MACA;QACA;QACA;UACAvE;QADA;QAGA;;QACA;UACA;YACA;UACA;;UACA;UACA;QACA;MACA;IACA,CAtNA;;IAuNA;MACA;QACAiE;MADA;MAGA,oDAJA,CAKA;;MACA;;MACA;QACA5E;UACA;YACA;UACA;QACA,CAJA;QAKA;QACA,uBAPA,CAQA;MACA,CATA,MASA;QACA;MACA;IACA,CA1OA;;IA2OAmF;MACA;MACA;MACA;IACA,CA/OA;;IAgPAC;MACA;MACA;MACA;MACA;MACA;QACAT;MACA,CAFA;MAGA;IACA,CAzPA;;IA0PAU;MACA;MACA;IACA,CA7PA;;IA8PAC;MACA;IACA,CAhQA;;IAiQA;MACA;MACA;QACA;MACA;;MACA;QACAC,gCADA;QAEAX,wBAFA;QAGAY,iCAHA;QAIAC,sCAJA;QAKAC,iCALA;QAMArD,oCANA;QAOA+B,6BAPA;QAQAuB;MARA;MAUA;MACA;MACA;MACA;MACAC;MACA;MACAC,yEArBA,CAsBA;IACA,CAxRA;;IAyRA;MACAlC;MACA;QACAf,6CADA;QAEA8C,gCAFA;QAGArD;MAHA;MAKA;MACA;MACA;MACA;MACAuD;MACA;MACAC;IACA,CAvSA;;IAwSAC;MACA;;MACA;QACAC;MACA;IACA,CA7SA;;IA8SA;MACA;QACAnD,6CADA;QAEA8C,YAFA;QAGArD;MAHA;MAMA;;MACA;QACAmC;UACAG;QACA,CAFA;MAGA;;MACA;IACA,CA5TA;;IA6TA;MACA;QACA;MACA;;MACAhB;MACAA,qDALA,CAMA;;MACA;QACAf,6CADA;QAEAE,gCAFA;QAGA4C,gCAHA;QAIArD;MAJA;MAMA;MACAsB;;MACA;QACA;QACAqC;UACArB;QACA,CAFA;QAGA;QACA;QACA;QACA;MACA,CATA,MASA;QACA;QACA;QACA;QACA;MACA;;MAEA;QACA;MACA;;MACA;QACA;MACA;;MACAhB;IACA,CAnWA;;IAoWA;MACA;QACA;MACA;;MACAA,gCAJA,CAKA;;MACA;MACA;QACAf,6CADA;QAEAE,gCAFA;QAGA4C,gCAHA;QAIArD;MAJA;MAMA;;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA;;MACAsB;MACA;;MACA;QACA;QACAqC;UACArB;QACA,CAFA;QAGA;QACA;QACA;QACA;MACA,CATA,MASA;QACA;QACA;QACA;QACA;MACA;;MACA;QACAhB;QACA;;QACA;UACAsC;QACA,CAFA,MAEA;UACAA;QACA;;QACA;UACA,wDADA,CAEA;;UACAtC;UACA;UACAuC;YACA;cACA;YACA;UACA,CAJA;;UAKA;YACA;UACA;QACA;MACA;IACA,CA/ZA;;IAgaAC;MACA;MACA;IACA,CAnaA;;IAoaAC;MACA;MACA;IACA,CAvaA;;IAwaAC;MACA;MACA;IACA,CA3aA;;IA4aAC;MACA;MACA;IACA;;EA/aA;AApKA", "names": ["GetProcessOrderViewSegments", "GetProcessOrderView", "GetEquipmentFunctionView", "GetEquipmentProcessOrderView", "getCheckTippingType", "GetCookieOrderLtexts", "GetBBatchListView", "GetBatchCode", "PoProducedStart", "PoProducedResume", "GetRunOrder", "GetCookOrderLtexts", "GetProcessOrderViewSegmentUnits", "GetPoList", "components", "ParameterDownload", "POManagement", "Consume", "Produce", "Tipping", "Storage", "Performance", "MaterialPrep", "PerformanceEvents", "Logsheets", "Tippingscan", "SampleWeighing", "ProcessText", "CodeDiff", "data", "viewtitle", "timepicker", "AvailablePOManagemenList", "Availableheader", "StartModel", "MyEquipment", "ShowPOList", "MyEquipmentList", "Startlist", "label", "id", "value", "disabled", "type", "require", "option", "value2", "value3", "searchlist", "name", "width", "chooseItem", "ProcessOrder", "isResume", "TargetQuantity", "Unit1", "MaterialCode", "MaterialName", "tableList", "Equipmentlist", "AvailabletableId", "tableId", "header", "pageOptions", "total", "page", "pageSize", "pageCount", "pageSizeitems", "pageOptions2", "activeName", "activeName2", "ActiveList", "EquipmentGroupRowId", "EquipmentId", "RunEquipmentId", "BatchId", "ExecutionId", "runningCode", "isTippingscan", "productionId", "isEdit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EquipmentCode", "IsPack", "Text1", "Text2", "mounted", "console", "methods", "flag", "Message", "message", "SegmentId", "ProductionOrderId", "PoSegmentRequirementId", "LotCode", "StartTime", "ProductionDate", "ExpirationDate", "params", "res", "GetDate", "startOrder", "item", "LineCode", "equipmentCode", "productionDate", "addDays", "newDate", "DateAdd", "date", "<PERSON><PERSON>ch", "<PERSON><PERSON><PERSON>y", "loadProgress", "back", "Search", "Segment", "FillLineCode", "pageIndex", "EndTime", "el", "el3", "changePagination", "el2", "list", "num", "functionlist", "handleSizeChange", "handleCurrentChange", "handleSizeChange2", "handleCurrentChange2"], "sourceRoot": "src/views/Producting/Overview", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"usemystyle overview\">\r\n        <div class=\"InventorySearchBox\">\r\n            <div class=\"searchbox\">\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-back\" @click=\"back()\">{{ this.$t('Overview.Back') }}</el-button>\r\n                <div class=\"searchboxtitle\">\r\n                    {{ viewtitle }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <el-tabs v-model=\"activeName\" type=\"border-card\" @tab-click=\"handleClick\">\r\n            <el-tab-pane :label=\"$t('Overview.Overview')\" name=\"1\">\r\n                <div class=\"tablebox\">\r\n                    <el-table :data=\"tableList\" style=\"width: 100%\" height=\"700\">\r\n                        <el-table-column\r\n                            v-for=\"(item, index) in header\"\r\n                            :key=\"index\"\r\n                            :align=\"item.align\"\r\n                            :prop=\"item.prop ? item.prop : item.value\"\r\n                            :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                            :width=\"item.width\"\r\n                        >\r\n                            <template slot-scope=\"scope\">\r\n                                <span v-if=\"scope.row.ProcessOrder && scope.column.property == 'Complete'\">\r\n                                    <el-progress\r\n                                        :text-inside=\"true\"\r\n                                        color=\"#3dcd58\"\r\n                                        text-color=\"#000000\"\r\n                                        :stroke-width=\"26\"\r\n                                        :percentage=\"Number(((scope.row.Total / scope.row.TargetQuantity) * 100).toFixed(2))\"\r\n                                    ></el-progress>\r\n                                </span>\r\n                                <span v-else-if=\"scope.column.property == 'PlantNode'\">\r\n                                    <div>{{ scope.row.EquipmentCode }}</div>\r\n                                    <div style=\"color: #808080\">{{ scope.row.EquipmentName }}</div>\r\n                                </span>\r\n                                <span v-else-if=\"scope.column.property == 'Material'\">\r\n                                    <div>{{ scope.row.MaterialCode }}</div>\r\n                                    <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                                </span>\r\n                                <span v-else-if=\"scope.column.property == 'BatchQty'\">{{ scope.row.BatchQty }}{{ scope.row.Unit1 }}</span>\r\n                                <span v-else-if=\"scope.column.property == 'Sequence'\">{{ scope.row.ProcessOrder != null ? scope.row.Sequence : '' }}</span>\r\n                                <span v-else>{{ scope.row[item.prop] }}</span>\r\n                            </template>\r\n                        </el-table-column>\r\n                    </el-table>\r\n                    <div class=\"paginationbox\">\r\n                        <el-pagination\r\n                            @size-change=\"handleSizeChange\"\r\n                            @current-change=\"handleCurrentChange\"\r\n                            :current-page=\"pageOptions.page\"\r\n                            :page-sizes=\"pageOptions.pageSizeitems\"\r\n                            :page-size=\"pageOptions.pageSize\"\r\n                            layout=\"total, sizes, prev, pager, next\"\r\n                            :total=\"pageOptions.total\"\r\n                            background\r\n                        ></el-pagination>\r\n                    </div>\r\n                </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane v-if=\"ShowPOList\" :label=\"$t('Overview.POList')\" name=\"2\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"datebox\">\r\n                            <div class=\"datepickbox\">\r\n                                <el-date-picker\r\n                                    v-model=\"timepicker\"\r\n                                    type=\"daterange\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                                    range-separator=\"-\"\r\n                                    :start-placeholder=\"$t('DFM_RL._KSRQ')\"\r\n                                    :end-placeholder=\"$t('DFM_RL._JSRQ')\"\r\n                                ></el-date-picker>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"inputformbox\" :style=\"{ width: item.width }\" v-for=\"(item, index) in searchlist\" :key=\"index\">\r\n                            <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\" :myid=\"item.id\" :placeholder=\"item.name\"></el-input>\r\n                            <el-select :style=\"{ width: item.width }\" v-model=\"item.value\" v-if=\"item.type == 'select'\" :myid=\"item.id\" :placeholder=\"item.name\">\r\n                                <el-option v-for=\"(it, ind) in item.option\" :key=\"ind\" :label=\"it.value\" :value=\"it.key\"></el-option>\r\n                            </el-select>\r\n                        </div>\r\n                        <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                        <el-button size=\"small\" style=\"margin-left: 5px\" icon=\"el-icon-s-help\" @click=\"getempty()\">{{ this.$t('GLOBAL._CZ') }}</el-button>\r\n                    </div>\r\n                </div>\r\n                <div class=\"tablebox\">\r\n                    <el-table :data=\"AvailablePOManagemenList\" style=\"width: 100%\" height=\"670\">\r\n                        <el-table-column\r\n                            v-for=\"(item, index) in Availableheader\"\r\n                            :key=\"index\"\r\n                            :align=\"item.align\"\r\n                            :prop=\"item.prop ? item.prop : item.value\"\r\n                            :label=\"$t(`$vuetify.dataTable.${AvailabletableId}.${item.value}`)\"\r\n                            :width=\"item.width\"\r\n                        >\r\n                            <template slot-scope=\"scope\">\r\n                                <!-- <span v-if=\"scope.column.property == 'operate'\">\r\n                                    <el-button size=\"mini\" class=\"operatebtn\" v-if=\"scope.row.Status > 1 && scope.row.RunningCount == 0\" @click=\"startOrder(scope)\" icon=\"el-icon-video-play\">\r\n                                        {{ $t('Overview.start') }}\r\n                                    </el-button>\r\n                                </span> -->\r\n                                <span v-if=\"scope.column.property == 'PlanStartTime'\">{{ $dayjs(scope.row.PlanStartTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                                <span v-else-if=\"scope.column.property == 'PlanEndTime'\">{{ $dayjs(scope.row.PlanEndTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                                <span v-else-if=\"scope.column.property == 'IsHavePreservative'\">\r\n                                    <i :class=\"scope.row[item.value] === '1' ? 'el-icon-star-on' : ''\"></i>\r\n                                </span>\r\n                                <span v-else-if=\"scope.column.property == 'LineNominalSpeed'\">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>\r\n                                <span v-else>{{ scope.row[item.prop] }}</span>\r\n                            </template>\r\n                        </el-table-column>\r\n                    </el-table>\r\n                    <div class=\"paginationbox\">\r\n                        <el-pagination\r\n                            @size-change=\"handleSizeChange2\"\r\n                            @current-change=\"handleCurrentChange2\"\r\n                            :current-page=\"pageOptions2.page\"\r\n                            :page-sizes=\"pageOptions2.pageSizeitems\"\r\n                            :page-size=\"pageOptions2.pageSize\"\r\n                            layout=\"total, sizes, prev, pager, next\"\r\n                            :total=\"pageOptions2.total\"\r\n                            background\r\n                        ></el-pagination>\r\n                    </div>\r\n                    <el-dialog :title=\"$t('Overview.StartOrder')\" id=\"Startdialog\" :visible.sync=\"StartModel\" :width=\"IsPack == '0' ? '1050px' : '650px'\">\r\n                        <span slot=\"title\" class=\"dialog-title\">\r\n                            <div class=\"dialogtitlebox\">\r\n                                {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.StartOrder') }}\r\n                                <div class=\"dialogsubtitlebox\" style=\"display: inline\">{{ chooseItem.ProcessOrder }}</div>\r\n                            </div>\r\n                        </span>\r\n                        <div class=\"splitdetailbox\">\r\n                            <div class=\"splitdetailboxtitle\">{{ chooseItem.MaterialCode }}-{{ chooseItem.MaterialName }}</div>\r\n                            <div class=\"detailsnote\" v-if=\"runningCode != '' && !chooseItem.isResume\">\r\n                                {{ $t('Overview.Note1') }}\r\n                                <span style=\"font-weight: 600\">{{ runningCode }}</span>\r\n                                {{ $t('Overview.Note2') }}\r\n                            </div>\r\n                            <div style=\"display: flex\">\r\n                                <div :style=\"{ width: IsPack == '0' ? '100%' : '100%' }\">\r\n                                    <div class=\"dialogdetailbox\">\r\n                                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: '10%' }\">{{ $t('Overview.ChooseEquipment') + ' *' }}</div>\r\n                                        <div class=\"dialogdetailsinglevalue \" :style=\"{ width: '87%' }\">\r\n                                            <el-select style=\"width: 92%\" v-model=\"MyEquipment\" @change=\"getMyEquipment()\" filterable>\r\n                                                <el-option v-for=\"(it, index) in MyEquipmentList\" :key=\"index\" :label=\"it.EquipmentName\" :value=\"it.ID\"></el-option>\r\n                                            </el-select>\r\n                                        </div>\r\n                                    </div>\r\n                                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Startlist\" :key=\"index\">\r\n                                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: item.type == 'BatchCode' ? '10%' : '10%' }\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                                        <div class=\"dialogdetailsinglevalue \" :style=\"{ width: item.type == 'BatchCode' || item.type == 'checkBox' ? '80%' : '80%' }\">\r\n                                            <el-input style=\"width: 100%\" v-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                                            <!-- <div class=\"detailsnote2\" v-else-if=\"item.type == 'checkBox' && IsDifferent === true && IsPack === '0'\">\r\n                                                {{ $t('Overview.IsUpdateLtxt') }}\r\n                                            </div>\r\n                                            <div class=\"detailsnote3\" v-else-if=\"item.type == 'checkBox' && IsDifferent === false && IsPack === '0'\">\r\n                                                {{ $t('Overview.TextGood') }}\r\n                                            </div> -->\r\n                                            <!-- <div v-else-if=\"item.type == 'BatchCode'\" style=\"display: flex\">\r\n                                                <el-input v-model=\"item.value\"></el-input>\r\n                                                <el-input v-model=\"item.value2\" disabled></el-input>\r\n                                                <el-input v-model=\"item.value3\"></el-input>\r\n                                                <el-button\r\n                                                    class=\"tablebtn\"\r\n                                                    @click=\"getBatchCode()\"\r\n                                                    size=\"mini\"\r\n                                                    style=\"margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff\"\r\n                                                    icon=\"el-icon-refresh\"\r\n                                                ></el-button>\r\n                                            </div> -->\r\n                                            <el-select style=\"width: 100%\" clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                                                <el-option v-for=\"it in item.option\" :key=\"it.ID\" :label=\"it.Number\" :value=\"it.ID\"></el-option>\r\n                                            </el-select>\r\n                                            <el-date-picker\r\n                                                @change=\"GetDate(item.id)\"\r\n                                                v-else-if=\"item.type == 'date'\"\r\n                                                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                                                :disabled=\"item.disabled\"\r\n                                                v-model=\"item.value\"\r\n                                                type=\"datetime\"\r\n                                                style=\"width: 100%\"\r\n                                            ></el-date-picker>\r\n                                            <span v-else-if=\"item.id == 'TargetQuantity'\">{{ chooseItem.TargetQuantity }}{{ chooseItem.Unit1 }}</span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n<!--                                <div v-if=\"IsPack == '0'\" style=\"height: 100%; margin-top: 10px; padding: 0 10px; box-shadow: none; display: flex; flex-direction: column\">-->\r\n\r\n<!--                                    <span class=\"dialogsubtitlebox\" style=\"margin-bottom: 5px\">工单长文本：</span>-->\r\n<!--                                    <el-input type=\"textarea\" :autosize=\"{ minRows: 5 }\" style=\"width: 370px !important\" :disabled=\"isEdit == false\" v-model=\"Text1\"></el-input>-->\r\n<!--                                    <span class=\"dialogsubtitlebox\" style=\"margin: 5px 0\">配方长文本：</span>-->\r\n<!--                                    <el-input type=\"textarea\" :autosize=\"{ minRows: 5 }\" style=\"width: 370px !important\" :disabled=\"isEdit == false\" v-model=\"Text2\"></el-input>-->\r\n<!--                                </div>-->\r\n                            </div>\r\n                          <div style=\"padding: 18px 18px 0 18px\">\r\n                            <el-row>\r\n                              <el-col :span=\"12\">\r\n                                <span class=\"font-M09 bold\">工单长文本</span>\r\n                              </el-col>\r\n                              <el-col :span=\"12\">\r\n                                <span class=\"font-M09 bold\">配方长文本</span>\r\n                              </el-col>\r\n                            </el-row>\r\n                            <CodeDiff\r\n                                hideHeader\r\n                                :old-string=\"Text1\"\r\n                                :new-string=\"Text2\"\r\n                                output-format=\"side-by-side\"\r\n                            />\r\n                          </div>\r\n\r\n                        </div>\r\n                        <span slot=\"footer\" class=\"dialog-footer\">\r\n                            <el-button style=\"float: left\" v-if=\"chooseItem.isResume\">\r\n                                {{ $t('Overview.bottleneck') }}\r\n                            </el-button>\r\n                            <el-button class=\"tablebtn\" icon=\"el-icon-video-play\" @click=\"ProducedStart()\">\r\n                                {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.Start') }}\r\n                            </el-button>\r\n                            <el-button @click=\"StartModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n                        </span>\r\n                    </el-dialog>\r\n                </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane v-for=\"(item, index) in Equipmentlist\" :key=\"index\" :disabled=\"item.FunctionCodes != '' ? false : true\" :name=\"item.ID\">\r\n                <span slot=\"label\">\r\n                    <i :class=\"item.FunctionCodes != '' ? 'el-icon-s-tools' : 'el-icon-s-grid'\"></i>\r\n                    <span>{{ item.EquipmentName }}</span>\r\n                    <div v-if=\"item.FunctionCodes != ''\" class=\"tabiconbox\">\r\n                        <span v-if=\"item.FunctionCodes.indexOf('POManagement') != -1\">\r\n                            <i\r\n                                :style=\"{ color: item.ProductionOrderId == null ? 'red' : '#3DCD58' }\"\r\n                                :class=\"item.ProductionOrderId == null ? 'iconfont icon-pausecircle-fill' : 'iconfont icon-play-fill'\"\r\n                            ></i>\r\n                        </span>\r\n                        <!-- <i class=\"el-icon-s-marketing\"></i> -->\r\n                    </div>\r\n                </span>\r\n                <div class=\"subtabs\">\r\n                    <div class=\"activeTitle\" v-if=\"ActiveList.length == 0\">{{ $t('Overview.NoActiveProcessOrder') }}</div>\r\n                    <div class=\"activeTitle\" v-else>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ ActiveList[0].ProcessOrder }}({{ ActiveList[0].Number }})</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].Material }}</div>\r\n                        </div>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ ActiveList[0].TargetQuantity }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].Unit1 }}</div>\r\n                        </div>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ ActiveList[0].Speed }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].SpeedUom }}</div>\r\n                        </div>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ $t('Overview.BatchCode') }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].BatchCode }}</div>\r\n                        </div>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ $t('Overview.ExpirationDate') }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].ExpirationDate }}</div>\r\n                        </div>\r\n                               <div class=\"activeBox\" v-if=\"ActiveList[0].StorageTank != ''&&ActiveList[0].StorageTank != null\">\r\n                            <div class=\"activeLabel\">{{ $t('Overview.CGBM') }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].StorageTank }}</div>\r\n                        </div>\r\n                               <div class=\"activeBox\" v-if=\"ActiveList[0].StorageTankOrderGc != ''&&ActiveList[0].StorageTankOrderGc != null\">\r\n                            <div class=\"activeLabel\">{{ $t('Overview.GC') }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].StorageTankOrderGc }}</div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"subtabsbox\">\r\n                        <el-tabs v-model=\"activeName2\" type=\"border-card\" @tab-click=\"handleClick2(item, index)\">\r\n                            <!-- v-if=\"item.functionlist.includes('PO Management') != -1\" -->\r\n                            <!-- <el-tab-pane :label=\"$t('Overview.POManagement')\" name=\"1\">\r\n                                <POManagement ref=\"POManagement\" @getNum=\"getNum\" :EquipmentId=\"EquipmentId\"></POManagement>\r\n                            </el-tab-pane> -->\r\n                            <el-tab-pane :label=\"$t(`Overview.${it.trim()}`)\" v-for=\"(it, ind) in item.functionlist\" :key=\"ind\" :name=\"it.trim()\">\r\n                                <div>\r\n                                    <component\r\n                                        @loadProgress=\"loadProgress\"\r\n                                        :is=\"it.trim() == 'Tipping' ? (isTippingscan ? 'Tippingscan' : 'Tipping') : it.trim()\"\r\n                                        :ref=\"index + it.trim()\"\r\n                                        :BatchId=\"BatchId\"\r\n                                        :EquipmentName=\"item.EquipmentName\"\r\n                                        :ExecutionId=\"ExecutionId\"\r\n                                        :EquipmentId=\"EquipmentId\"\r\n                                        :RunEquipmentId=\"RunEquipmentId\"\r\n                                    ></component>\r\n                                </div>\r\n                            </el-tab-pane>\r\n                            <!-- <el-tab-pane label=\"SampleWeighing\" name=\"SampleWeighing\">\r\n                                <SampleWeighing :ref=\"index + 'SampleWeighing'\" :EquipmentId=\"EquipmentId\"></SampleWeighing>\r\n                            </el-tab-pane> -->\r\n                            <!-- <el-tab-pane label=\"ParameterDownload\" name=\"ParameterDownload\">\r\n                                <ParameterDownload :ref=\"index + 'ParameterDownload'\" :EquipmentId=\"EquipmentId\"></ParameterDownload>\r\n                            </el-tab-pane> -->\r\n                            <!--\r\n                            <el-tab-pane label=\"Performance\" name=\"Performance\">\r\n                                <Performance :ref=\"index + 'Performance'\" :EquipmentId=\"EquipmentId\"></Performance>\r\n                            </el-tab-pane> -->\r\n                            <!-- <el-tab-pane label=\"Tipping\" name=\"Tipping\">\r\n                                <Tipping :ref=\"index + 'Tipping'\"></Tipping>\r\n                            </el-tab-pane> -->\r\n                            <!-- <el-tab-pane label=\"Logsheets\" name=\"Logsheets\">\r\n                                <Logsheets :ref=\"index + 'Logsheets'\"></Logsheets>\r\n                            </el-tab-pane> -->\r\n                        </el-tabs>\r\n                    </div>\r\n                </div>\r\n            </el-tab-pane>\r\n        </el-tabs>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { POManagemenOverview } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetProcessOrderViewSegments,\r\n    GetProcessOrderView,\r\n    GetEquipmentFunctionView,\r\n    GetEquipmentProcessOrderView,\r\n    getCheckTippingType,\r\n    GetCookieOrderLtexts,\r\n    GetBBatchListView,\r\n    GetBatchCode,\r\n    PoProducedStart,\r\n    PoProducedResume,\r\n    GetRunOrder,\r\n    GetCookOrderLtexts,\r\n    GetProcessOrderViewSegmentUnits,\r\n    GetPoList\r\n} from '@/api/Inventory/Overview.js';\r\nimport { POManagemenPoList } from '@/columns/factoryPlant/tableHeaders';\r\nimport moment from 'moment';\r\n\r\nimport { Message } from 'element-ui';\r\nimport { CodeDiff } from 'v-code-diff'\r\nexport default {\r\n    components: {\r\n        ParameterDownload: () => import('./components/ParameterDownload'),\r\n        POManagement: () => import('./components/POManagement'),\r\n        Consume: () => import('./components/Consume'),\r\n        Produce: () => import('./components/Produce'),\r\n        Tipping: () => import('./components/Tipping'),\r\n        Storage: () => import('./components/Storage'),\r\n                Performance: () => import('./components/Performance'),\r\n        MaterialPrep: () => import('./components/MaterialPrep'),\r\n        PerformanceEvents: () => import('./components/Performance'),\r\n        Logsheets: () => import('./components/Logsheets'),\r\n        Tippingscan: () => import('./components/Tippingscan'),\r\n        SampleWeighing: () => import('./components/SampleWeighing'),\r\n        ProcessText: () => import('./components/Processlongtext'),\r\n      CodeDiff\r\n    },\r\n    data() {\r\n        return {\r\n            viewtitle: '',\r\n            timepicker: [],\r\n            AvailablePOManagemenList: [],\r\n            Availableheader: POManagemenPoList,\r\n            StartModel: false,\r\n            MyEquipment: '',\r\n            ShowPOList: false,\r\n            MyEquipmentList: [],\r\n            Startlist: [\r\n                {\r\n                    label: this.$t('Overview.StartTime'),\r\n                    id: 'StartTime',\r\n                    value: '',\r\n                    disabled: true,\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.Batch'),\r\n                    id: 'BatchId',\r\n                    value: '',\r\n                    require: true,\r\n                    type: 'select',\r\n                    option: []\r\n                },\r\n                {\r\n                    label: this.$t('Overview.BatchCode'),\r\n                    id: 'LotCode',\r\n                    value: '',\r\n                    value2: '',\r\n                    value3: '',\r\n                    require: true,\r\n                    type: 'BatchCode'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ExpirationDate'),\r\n                    id: 'ExpirationDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date',\r\n                    disabled: true\r\n                },\r\n                {\r\n                    label: this.$t('Overview.TargetQuantity'),\r\n                    id: 'TargetQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Overview.CrewSize'),\r\n                    id: 'CrewSize',\r\n                    value: '',\r\n                    type: 'input'\r\n                },\r\n                {\r\n                    label: '',\r\n                    id: 'IsUpdateLtxt',\r\n                    value: false,\r\n                    type: 'checkBox'\r\n                }\r\n            ],\r\n            searchlist: [\r\n                {\r\n                    type: 'input',\r\n                    name: this.$t('Overview.QuickSearch'),\r\n                    id: 'QuickSearch',\r\n                    value: ''\r\n                },\r\n                {\r\n                    type: 'select',\r\n                    option: [],\r\n                    width: '20vh',\r\n                    name: this.$t('DFM_JYXM.Segment'),\r\n                    id: 'Segment',\r\n                    value: ''\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'FillLineCode',\r\n                    name: this.$t('$vuetify.dataTable.PRO_POLIST.FillLineCode')\r\n                }\r\n            ],\r\n            chooseItem: {\r\n                ProcessOrder: '',\r\n                isResume: false,\r\n                TargetQuantity: '',\r\n                Unit1: '',\r\n                MaterialCode: '',\r\n                MaterialName: ''\r\n            },\r\n            tableList: [],\r\n            Equipmentlist: [],\r\n            AvailabletableId: 'PRO_POManagement',\r\n            tableId: 'PRO_Overview',\r\n            header: POManagemenOverview,\r\n            pageOptions: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            pageOptions2: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            activeName: '1',\r\n            activeName2: '',\r\n            ActiveList: [],\r\n            EquipmentGroupRowId: '',\r\n            EquipmentId: '',\r\n            RunEquipmentId: '',\r\n            BatchId: '',\r\n            ExecutionId: '',\r\n            runningCode: '',\r\n            isTippingscan: false,\r\n            productionId: '',\r\n            isEdit: false,\r\n            IsDifferent: false,\r\n            EquipmentCode: '',\r\n            IsPack: '1',\r\n            Text1: '',\r\n            Text2: ''\r\n        };\r\n    },\r\n    mounted() {\r\n        this.viewtitle = JSON.parse(this.$route.query.query).Description;\r\n        console.log(this.viewtitle);\r\n        this.EquipmentGroupRowId = JSON.parse(this.$route.query.query).ID;\r\n        // this.FiltersTableData();\r\n        this.GetProcessOrderViewList();\r\n        //this.GetProcessOrderView2();\r\n        this.GetSegment();\r\n        this.GetEquipment();\r\n        this.changePagination();\r\n    },\r\n    methods: {\r\n        async ProducedStart() {\r\n            let flag = this.Startlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (this.MyEquipment == '') {\r\n                flag = true;\r\n            }\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                SegmentId: this.chooseItem.SegmentId,\r\n                ProductionOrderId: this.chooseItem.ProductionOrderId,\r\n                PoSegmentRequirementId: this.chooseItem.ID,\r\n                BatchId: '',\r\n                LotCode: '',\r\n                EquipmentId: this.MyEquipment,\r\n                StartTime: '',\r\n                ProductionDate: '',\r\n                ExpirationDate: ''\r\n            };\r\n            if (this.chooseItem.isResume == true) {\r\n                params.ExecutionId = this.chooseItem.ExecutionId;\r\n            } else {\r\n                params.ExecutionId = '';\r\n            }\r\n            this.Startlist.forEach(item => {\r\n                if (item.id == 'LotCode') {\r\n                    params[item.id] = item.value + item.value2 + item.value3;\r\n                } else {\r\n                    params[item.id] = item.value;\r\n                }\r\n            });\r\n            params.ExpirationDate = moment(params.ExpirationDate).format('YYYY-MM-DD HH:mm:ss');\r\n            if (params.LotCode.length > 10) {\r\n                Message({\r\n                    message: `${this.$t('Overview.BatchCodeLong')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let res;\r\n            if (this.chooseItem.isResume == true) {\r\n                res = await PoProducedResume(params);\r\n            } else {\r\n                res = await PoProducedStart(params);\r\n            }\r\n            //this.GetProcessOrderView2();\r\n            this.loadProgress();\r\n            this.StartModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        GetDate(id) {\r\n            if (id == 'ProductionDate') {\r\n                if (this.chooseItem.NeedQARelease == '1') {\r\n                    this.Startlist[4].value = this.addDays(this.Startlist[3].value, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);\r\n                } else {\r\n                    this.Startlist[4].value = this.addDays(this.chooseItem.PlanStartTime, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);\r\n                }\r\n            }\r\n        },\r\n        startOrder(item) {\r\n            // this.MyEquipment = '';\r\n            // this.EquipmentCode = '';\r\n            this.SegmentUnits(item);\r\n            console.log(item.row);\r\n            if (item.row) {\r\n                this.IsPack = item.row.NeedQARelease;\r\n                this.getLtext(item.row.ProductionOrderId);\r\n                this.chooseItem = item.row;\r\n                this.chooseItem.isResume = false;\r\n            } else {\r\n                this.chooseItem = item;\r\n                this.chooseItem.isResume = true;\r\n            }\r\n            this.Startlist.forEach((item, index) => {\r\n                if (index == 0) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 3) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 2) {\r\n                    item.value = '';\r\n                    item.value2 = '';\r\n                    item.value3 = '';\r\n                } else if (item.id == 'IsUpdateLtxt') {\r\n                    item.value = false;\r\n                } else {\r\n                    item.value = '';\r\n                }\r\n            });\r\n            this.GetDate('ProductionDate');\r\n            this.getBatchList();\r\n        },\r\n        async SegmentUnits(item) {\r\n            let res = await GetProcessOrderViewSegmentUnits('', item.row.Segment);\r\n            this.MyEquipmentList = res.response;\r\n        },\r\n        async getBatchCode() {\r\n            let date = moment(this.Startlist[3].value).format('YYYY-MM-DD HH:mm:ss');\r\n            let p = {\r\n                LineCode: this.Startlist[2].value,\r\n                equipmentCode: this.EquipmentCode,\r\n                productionDate: date,\r\n                productionId: this.chooseItem.ProductionOrderId\r\n            };\r\n            let res = await GetBatchCode(p);\r\n            if (res.response == null) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'warning'\r\n                });\r\n            } else {\r\n                this.Startlist[2].value = res.response.substring(0, 2);\r\n                this.Startlist[2].value2 = res.response.substring(2, 5);\r\n            }\r\n        },\r\n        async getBatchList() {\r\n            let params = {\r\n                PoSegmentRequirementId: this.chooseItem.ID\r\n            };\r\n            let res = await GetBBatchListView(params);\r\n            this.Startlist[1].option = res.response;\r\n            this.Startlist[1].value = this.Startlist[1].option[0].ID;\r\n            this.StartModel = true;\r\n        },\r\n        addDays(date, number, interval) {\r\n            const newDate1 = new Date(date);\r\n            const newDate = new Date(newDate1.getFullYear(), newDate1.getMonth(), newDate1.getDate());\r\n            this.DateAdd(interval, number, newDate);\r\n            newDate.setDate(newDate.getDate() + 1); // 增加一天\r\n            newDate.setSeconds(newDate.getSeconds() - 1); // 减去1秒\r\n            return newDate;\r\n        },\r\n        DateAdd(interval, number, date) {\r\n            switch (interval) {\r\n                case 'Y': {\r\n                    date.setFullYear(date.getFullYear() + number);\r\n                    return date;\r\n                }\r\n                case 'Q': {\r\n                    date.setMonth(date.getMonth() + number * 3);\r\n                    return date;\r\n                }\r\n                case 'M': {\r\n                    date.setMonth(date.getMonth() + number);\r\n                    return date;\r\n                }\r\n                case 'W': {\r\n                    date.setDate(date.getDate() + number * 7);\r\n                    return date;\r\n                }\r\n                case 'D': {\r\n                    date.setDate(date.getDate() + number);\r\n                    return date;\r\n                }\r\n                case 'h': {\r\n                    date.setHours(date.getHours() + number);\r\n                    return date;\r\n                }\r\n                case 'm': {\r\n                    date.setMinutes(date.getMinutes() + number);\r\n                    return date;\r\n                }\r\n                case 's': {\r\n                    date.setSeconds(date.getSeconds() + number);\r\n                    return date;\r\n                }\r\n                default: {\r\n                    date.setDate(date.getDate() + number);\r\n                    return date;\r\n                }\r\n            }\r\n        },\r\n        async getMyEquipment() {\r\n            this.MyGetRunOrder();\r\n            this.MyEquipmentList.forEach(item => {\r\n                if (item.ID == this.MyEquipment) {\r\n                    this.EquipmentCode = item.EquipmentCode;\r\n                }\r\n            });\r\n            this.getBatchCode();\r\n        },\r\n        async MyGetRunOrder() {\r\n            let res = await GetRunOrder('', this.MyEquipment);\r\n            this.runningCode = res.response;\r\n            if (this.runningCode == null) {\r\n                this.runningCode = '';\r\n            }\r\n        },\r\n        async getLtext(id) {\r\n            if (this.IsPack === '0') {\r\n                this.IsDifferent = false;\r\n                let params = {\r\n                    id: id\r\n                };\r\n                let r = await GetCookOrderLtexts(params);\r\n                if (r.response.length == 2) {\r\n                    if (r.msg === '长文本不一致！') {\r\n                        this.IsDifferent = true;\r\n                    }\r\n                    this.Text1 = r.response[0].ProcessData;\r\n                    this.Text2 = r.response[1].ProcessData;\r\n                }\r\n            }\r\n        },\r\n        async GetSegment() {\r\n            let params = {\r\n                LineCode: this.viewtitle\r\n            };\r\n            let res = await GetProcessOrderViewSegments(params);\r\n            // console.log(res,1432345345)\r\n            let data = res.response;\r\n            if (data.length != 0) {\r\n                data.forEach((item, index) => {\r\n                    if (index == 0) {\r\n                        this.searchlist[1].value = item.key;\r\n                    }\r\n                });\r\n                this.searchlist[1].option = data;\r\n                this.ShowPOList = true;\r\n                //this.GetProcessOrderView2();\r\n            } else {\r\n                this.ShowPOList = false;\r\n            }\r\n        },\r\n        getsearch() {\r\n            this.pageOptions2.page = 1;\r\n            this.pageOptions2.pageSize = 20;\r\n            this.GetProcessOrderView2();\r\n        },\r\n        getempty() {\r\n            this.QuickSearch = '';\r\n            this.timepicker = [];\r\n            this.pageOptions2.page = 1;\r\n            this.pageOptions2.pageSize = 20;\r\n            this.searchlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            this.GetProcessOrderView2();\r\n        },\r\n        loadProgress(id) {\r\n            this.GetEquipment();\r\n            this.handleClick();\r\n        },\r\n        back() {\r\n            this.$router.go(-1);\r\n        },\r\n        async GetProcessOrderView2() {\r\n            // console.log('999999');\r\n            if (this.timepicker == null) {\r\n                this.timepicker = [];\r\n            }\r\n            let params = {\r\n                Search: this.searchlist[0].value,\r\n                LineCode: this.viewtitle,\r\n                Segment: this.searchlist[1].value,\r\n                FillLineCode: this.searchlist[2].value,\r\n                pageIndex: this.pageOptions2.page,\r\n                pageSize: this.pageOptions2.pageSize,\r\n                StartTime: this.timepicker[0],\r\n                EndTime: this.timepicker[1]\r\n            };\r\n            let res = await GetPoList(params);\r\n            this.AvailablePOManagemenList = res.response.data;\r\n            this.pageOptions2.total = res.response.dataCount;\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions2.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            // this.getNumTofather();\r\n        },\r\n        async GetProcessOrderViewList() {\r\n            console.log(12);\r\n            let params = {\r\n                EquipmentGroupRowId: this.EquipmentGroupRowId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res = await GetEquipmentProcessOrderView(params);\r\n            this.tableList = res.response.data;\r\n            this.pageOptions.total = res.response.dataCount;\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n        },\r\n        changePagination() {\r\n            let el2 = document.getElementsByClassName(`el-select-dropdown__item`);\r\n            for (let i = 0; i < el2.length; i++) {\r\n                el2[i].innerHTML = el2[i].innerHTML.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            }\r\n        },\r\n        async GetEquipment() {\r\n            let params = {\r\n                EquipmentGroupRowId: this.EquipmentGroupRowId,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n\r\n            let res = await GetEquipmentFunctionView(params);\r\n            if (res.response.length != 0) {\r\n                res.response.forEach(item => {\r\n                    item.functionlist = item.FunctionCodes.split(',');\r\n                });\r\n            }\r\n            this.Equipmentlist = res.response;\r\n        },\r\n        async handleClick2(item, index) {\r\n            if (this.activeName2 == 'Logsheets') {\r\n                this.$refs[index + this.activeName2][0].activeName = '0';\r\n            }\r\n            console.log(this.ActiveList, 1);\r\n            console.log(this.$refs[index + this.activeName2][0]);\r\n            // this.GetEquipment();\r\n            let params = {\r\n                EquipmentGroupRowId: this.EquipmentGroupRowId,\r\n                RunEquipmentId: this.EquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res = await GetEquipmentProcessOrderView(params);\r\n            console.log(res, 222444);\r\n            if (res.response.data.length != 0) {\r\n                let list = res.response.data;\r\n                list.forEach(item => {\r\n                    item.Material = item.MaterialName + '-' + item.MaterialCode;\r\n                });\r\n                this.ActiveList = list;\r\n                this.BatchId = list[0].BatchId;\r\n                this.RunEquipmentId = list[0].RunEquipmentId;\r\n                this.ExecutionId = list[0].ExecutionId;\r\n            } else {\r\n                this.ActiveList = [];\r\n                this.BatchId = '';\r\n                this.RunEquipmentId = '';\r\n                this.ExecutionId = '';\r\n            }\r\n\r\n            if (this.$refs[index + this.activeName2][0].getEquipmentModal) {\r\n                this.$refs[index + this.activeName2][0].getEquipmentModal(item, this.ActiveList[0]);\r\n            }\r\n            if (this.$refs[index + this.activeName2][0].tabBeClick) {\r\n                this.$refs[index + this.activeName2][0].tabBeClick(item);\r\n            }\r\n            console.log(this.ActiveList);\r\n        },\r\n        async handleClick(key) {\r\n            if (key && Number(key.index) == 1) {\r\n                this.GetProcessOrderView2();\r\n            }\r\n            console.log(this.Equipmentlist);\r\n            // this.GetEquipment();\r\n            this.EquipmentId = this.activeName;\r\n            let params = {\r\n                EquipmentGroupRowId: this.EquipmentGroupRowId,\r\n                RunEquipmentId: this.EquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res2 = await getCheckTippingType(JSON.stringify(this.EquipmentId));\r\n            if (res2.response == 'true') {\r\n                this.isTippingscan = true;\r\n            } else {\r\n                this.isTippingscan = false;\r\n            }\r\n            console.log('handleClick');\r\n            let res = await GetEquipmentProcessOrderView(params);\r\n            if (res.response.data.length != 0) {\r\n                let list = res.response.data;\r\n                list.forEach(item => {\r\n                    item.Material = item.MaterialName + '-' + item.MaterialCode;\r\n                });\r\n                this.ActiveList = list;\r\n                this.BatchId = list[0].BatchId;\r\n                this.RunEquipmentId = list[0].RunEquipmentId;\r\n                this.ExecutionId = list[0].ExecutionId;\r\n            } else {\r\n                this.ActiveList = [];\r\n                this.BatchId = '';\r\n                this.RunEquipmentId = '';\r\n                this.ExecutionId = '';\r\n            }\r\n            if (key) {\r\n                console.log(Number(key.index));\r\n                let num = null;\r\n                if (this.ShowPOList) {\r\n                    num = Number(key.index) - 2;\r\n                } else {\r\n                    num = Number(key.index) - 1;\r\n                }\r\n                if (num >= 0) {\r\n                    let functionlist = this.Equipmentlist[num].functionlist;\r\n                    //console.log(this.Equipmentlist[num]);\r\n                    console.log(functionlist);\r\n                    this.activeName2 = functionlist[0];\r\n                    functionlist.forEach(item => {\r\n                        if (item == 'POManagement') {\r\n                            this.activeName2 = 'POManagement';\r\n                        }\r\n                    });\r\n                    if (this.$refs[num + this.activeName2][0].getEquipmentModal) {\r\n                        this.$refs[num + this.activeName2][0].getEquipmentModal(this.Equipmentlist[num], this.ActiveList[0]);\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        handleSizeChange(val) {\r\n            this.pageOptions.pageSize = val;\r\n            this.GetProcessOrderViewList();\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.pageOptions.page = val;\r\n            this.GetProcessOrderViewList();\r\n        },\r\n        handleSizeChange2(val) {\r\n            this.pageOptions2.pageSize = val;\r\n            this.GetProcessOrderView2();\r\n        },\r\n        handleCurrentChange2(val) {\r\n            this.pageOptions2.page = val;\r\n            this.GetProcessOrderView2();\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.overview {\r\n    .searchboxtitle {\r\n        font-size: 1.7vh;\r\n        color: #767777;\r\n        padding-bottom: 5px;\r\n        margin-left: 10px;\r\n    }\r\n\r\n    .el-tabs {\r\n        height: 94%;\r\n    }\r\n    .subtabsbox {\r\n        .el-tabs--border-card {\r\n            border: 0 !important;\r\n            box-shadow: none !important;\r\n        }\r\n    }\r\n    .paginationbox {\r\n        height: 65px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin-top: 10px;\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 47%;\r\n            text-align: right;\r\n        }\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n    .splitdetailbox {\r\n        padding-bottom: 10px;\r\n        border: 1px solid #e8e8e8;\r\n        margin-bottom: 5px;\r\n        .splitdetailboxtitle {\r\n            background: #f5f5f5;\r\n            height: 3.5vh;\r\n            display: flex;\r\n            align-items: center;\r\n            padding-left: 5px;\r\n            font-size: 1.1rem;\r\n            color: #303133;\r\n        }\r\n        .detailsnote {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 0.9rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .detailsnote2 {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 1.2rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .detailsnote3 {\r\n            background-color: #f5fdec;\r\n            border-color: #faecd8;\r\n            color: hsl(135, 55%, 44%);\r\n            padding: 8px;\r\n            font-size: 1.2rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .splitdetailboxtitleTag {\r\n            margin-left: 5px;\r\n            background: #5cb85c;\r\n            color: #fff;\r\n            border-color: #5cb85c;\r\n        }\r\n    }\r\n}\r\n//.el-dialog__body {\r\n//    .el-input {\r\n//        width: 250px !important;\r\n//    }\r\n//    .longwidthinput {\r\n//        .el-input {\r\n//            width: 400px !important;\r\n//        }\r\n//        .el-select {\r\n//            width: 400px !important;\r\n//        }\r\n//    }\r\n//    .el-select {\r\n//        width: 250px !important;\r\n//    }\r\n//}\r\n.code-diff-view{\r\n  margin-top: 5px;\r\n}\r\n</style>\r\n"]}]}