{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopAudit\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopAudit\\index.vue", "mtime": 1750296894458}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAwHA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopAudit", "sourcesContent": ["<template>\n  <div class=\"root\">\n    <div class=\"root-head\">\n      <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n        <div class=\"form-content\">\n          <div class=\"search-area\">\n            <div class=\"search-row\">\n              <el-form-item :label=\"$t('SOP.DocName')\" prop=\"docName\" label-width=\"40px\" class=\"search-form-item\">\n                <el-input v-model=\"searchForm.docName\" :placeholder=\"$t('SOP.EnterDocName')\" clearable size=\"small\"></el-input>\n              </el-form-item>\n\n              <el-form-item :label=\"$t('SOP.DocCode')\" prop=\"docCode\" label-width=\"40px\" class=\"search-form-item\">\n                <el-input v-model=\"searchForm.docCode\" :placeholder=\"$t('SOP.EnterDocCode')\" clearable size=\"small\"></el-input>\n              </el-form-item>\n\n              <el-form-item :label=\"$t('SOP.UploadUser')\" prop=\"uploadUser\" label-width=\"40px\" class=\"search-form-item\">\n                <el-input v-model=\"searchForm.uploadUser\" :placeholder=\"$t('SOP.EnterUploadUser')\" clearable size=\"small\"></el-input>\n              </el-form-item>\n\n              <div class=\"action-buttons\">\n                <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn\" size=\"small\">{{ $t('GLOBAL._CX') }}</el-button>\n                <el-button icon=\"el-icon-refresh\" @click=\"resetForm\" size=\"small\">{{ $t('GLOBAL._CZ') }}</el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </el-form>\n    </div>\n    <div class=\"root-main\">\n      <el-table class=\"mt-3\"\n                :height=\"mainH\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n        <el-table-column\n          type=\"index\"\n          label=\"序号\"\n          width=\"50\"\n          align=\"center\">\n        </el-table-column>\n        <el-table-column v-for=\"(item) in tableName\"\n                         :default-sort=\"{prop: 'date', order: 'descending'}\"\n                         :key=\"item.value\"\n                         :prop=\"item.value\"\n                         :label=\"typeof item.text === 'function' ? item.text() : item.text\"\n                         :width=\"item.width\"\n                         :align=\"item.alignType || 'center'\"\n                         sortable\n                         show-overflow-tooltip\n        >\n          <template slot-scope=\"scope\">\n            <template v-if=\"item.value === 'FileSize'\">\n              {{ formatFileSize(getDocFieldValue(scope.row, item.value)) }}\n            </template>\n            <template v-else-if=\"item.value === 'DocStatus'\">\n              <el-tag :type=\"getStatusType(getDocFieldValue(scope.row, item.value))\" size=\"small\">\n                {{ formatStatus(getDocFieldValue(scope.row, item.value)) }}\n              </el-tag>\n            </template>\n            <template v-else-if=\"item.value === 'OperationType'\">\n              {{ formatOperationType(scope.row[item.value]) }}\n            </template>\n            <template v-else-if=\"item.value === 'AuditResult'\">\n              <el-tag :type=\"getAuditResultType(scope.row[item.value])\" size=\"small\">\n                {{ formatAuditResult(scope.row[item.value]) }}\n              </el-tag>\n            </template>\n            <template v-else-if=\"['DocName', 'DocCode', 'DocVersion', 'FilePath'].includes(item.value)\">\n              {{ getDocFieldValue(scope.row, item.value) }}\n            </template>\n            <template v-else>\n              {{ scope.row[item.value] }}\n            </template>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"operation\" width=\"180\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <div class=\"operation-buttons\">\n              <el-button size=\"mini\" type=\"primary\" @click=\"previewDoc(scope.row)\">{{ $t('SOP.Preview') }}</el-button>\n              <el-button size=\"mini\" type=\"success\" @click=\"approveAudit(scope.row)\"\n                         :disabled=\"scope.row.AuditResult !== null && scope.row.AuditResult !== 0\">{{ $t('SOP.Approve') }}</el-button>\n              <el-button size=\"mini\" type=\"danger\" @click=\"rejectAudit(scope.row)\"\n                         :disabled=\"scope.row.AuditResult !== null && scope.row.AuditResult !== 0\">{{ $t('SOP.Reject') }}</el-button>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    <div class=\"root-footer\">\n      <el-pagination\n          class=\"mt-3\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"searchForm.pageIndex\"\n          :page-sizes=\"[10,20, 50, 100,500]\"\n          :page-size=\"searchForm.pageSize\"\n          layout=\"->,total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          background\n      ></el-pagination>\n    </div>\n    <!-- 审核不通过原因对话框 -->\n    <el-dialog :title=\"$t('SOP.RejectReason')\" :visible.sync=\"rejectDialogVisible\" width=\"500px\"\n               :close-on-click-modal=\"false\" :close-on-press-escape=\"false\">\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" label-width=\"120px\">\n        <el-form-item :label=\"$t('SOP.RejectReason')\" prop=\"auditComment\"\n                      :rules=\"[{ required: true, message: $t('SOP.RejectReasonRequired'), trigger: 'blur' }]\">\n          <el-input type=\"textarea\" v-model=\"rejectForm.auditComment\"\n                    :placeholder=\"$t('SOP.EnterRejectReason')\" :rows=\"4\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rejectDialogVisible = false\">{{ $t('GLOBAL._QX') }}</el-button>\n        <el-button type=\"primary\" @click=\"confirmReject\">{{ $t('GLOBAL._QD') }}</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\nimport {\n    delSopAudit, getSopAuditList, saveSopAuditForm\n} from \"@/api/SOP/sopAudit\";\n\nimport { sopAuditColumn } from '@/api/SOP/sopAudit.js';\n\n\nexport default {\n  name: 'index.vue',\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n        docName: '',\n        docCode: '',\n        uploadUser: ''\n      },\n      total: 0,\n      tableData: [],\n      tableName: sopAuditColumn,\n      loading: false,\n      mainH: 0,\n      buttonOption:{\n        name:'SOP审核',\n        serveIp:'baseURL_DFM',\n        uploadUrl:'/api/SopAudit/ImportData', //导入\n        exportUrl:'/api/SopAudit/ExportData', //导出\n        DownLoadUrl:'/api/SopAudit/DownLoadTemplate', //下载模板\n      },\n      // 审核不通过原因对话框\n      rejectDialogVisible: false,\n      rejectForm: {\n        auditComment: ''\n      },\n      currentAuditRow: null\n    }\n  },\n  mounted() {\n    this.getTableData()\n    this.$nextTick(() => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    })\n    window.onresize = () => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    }\n  },\n  methods: {\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    resetForm() {\n      this.searchForm = {\n        pageIndex: 1,\n        pageSize: 20,\n        docName: '',\n        docCode: '',\n        uploadUser: ''\n      }\n      this.getTableData()\n    },\n\n    delRow(row) {\n      this.$confirms({\n        title: this.$t('GLOBAL._TS'),\n        message: this.$t('GLOBAL._COMFIRM'),\n        confirmText: this.$t('GLOBAL._QD'),\n        cancelText: this.$t('GLOBAL._QX')\n      }).then(async () => {\n        delSopAudit([row.ID]).then(res => {\n          this.$message.success(res.msg)\n          this.getTableData()\n        })\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n\n    getTableData() {\n      getSopAuditList(this.searchForm).then(res => {\n        this.tableData = res.response.data\n        this.total = res.response.dataCount\n      })\n    },\n\n    // 获取Doc字段中的文档信息\n    getDocFieldValue(row, fieldName) {\n      // 如果是文档相关字段，从Doc对象中获取\n      if (['DocName', 'DocCode', 'DocVersion', 'FilePath', 'FileSize', 'DocStatus'].includes(fieldName)) {\n        return row.Doc ? row.Doc[fieldName] : ''\n      }\n      // 其他字段直接从row中获取\n      return row[fieldName]\n    },\n\n    // 预览文档\n    previewDoc(row) {\n      // 这里可以实现文档预览功能\n      this.$message.info(this.$t('SOP.PreviewNotImplemented'))\n    },\n\n    // 通过审核\n    approveAudit(row) {\n      this.$confirm(this.$t('SOP.ConfirmApprove'), this.$t('GLOBAL._TS'), {\n        confirmButtonText: this.$t('GLOBAL._QD'),\n        cancelButtonText: this.$t('GLOBAL._QX'),\n        type: 'warning'\n      }).then(() => {\n        const auditData = {\n          ...row,\n          AuditResult: 2, // 审批通过\n          AuditUserId: this.$store.getters.name,\n          AuditComment: this.$t('SOP.AuditPassed')\n        }\n        saveSopAuditForm(auditData).then(res => {\n          this.$message.success(this.$t('SOP.ApproveSuccess'))\n          this.getTableData()\n        })\n      }).catch(() => {\n        this.$message.info(this.$t('GLOBAL._QXCZ'))\n      })\n    },\n\n    // 不通过审核\n    rejectAudit(row) {\n      this.currentAuditRow = row\n      this.rejectForm.auditComment = ''\n      this.rejectDialogVisible = true\n    },\n\n    // 确认不通过\n    confirmReject() {\n      this.$refs.rejectForm.validate((valid) => {\n        if (valid) {\n          const auditData = {\n            ...this.currentAuditRow,\n            AuditResult: 3, // 审批不通过\n            AuditUserId: this.$store.getters.name,\n            AuditComment: this.rejectForm.auditComment\n          }\n          saveSopAuditForm(auditData).then(res => {\n            this.$message.success(this.$t('SOP.RejectSuccess'))\n            this.rejectDialogVisible = false\n            this.getTableData()\n          })\n        }\n      })\n    },\n\n    // 格式化文件大小\n    formatFileSize(size) {\n      if (!size) return ''\n      const units = ['B', 'KB', 'MB', 'GB']\n      let index = 0\n      while (size >= 1024 && index < units.length - 1) {\n        size /= 1024\n        index++\n      }\n      return `${size.toFixed(2)} ${units[index]}`\n    },\n\n    // 格式化文档状态\n    formatStatus(status) {\n      const statusMap = {\n        1: this.$t('SOP.StatusValid'),\n        0: this.$t('SOP.StatusInvalid')\n      }\n      return statusMap[status] || this.$t('GLOBAL._WZ')\n    },\n\n    // 获取状态类型\n    getStatusType(status) {\n      return status === 1 ? 'success' : 'danger'\n    },\n\n    // 格式化操作类型\n    formatOperationType(type) {\n      const typeMap = {\n        1: this.$t('SOP.OperationCreate'),\n        2: this.$t('SOP.OperationModify'),\n        3: this.$t('SOP.OperationDelete')\n      }\n      return typeMap[type] || this.$t('GLOBAL._WZ')\n    },\n\n    // 格式化审核结果\n    formatAuditResult(result) {\n      const resultMap = {\n        0: this.$t('SOP.AuditPending'),\n        1: this.$t('SOP.AuditInProgress'),\n        2: this.$t('SOP.AuditApproved'),\n        3: this.$t('SOP.AuditRejected')\n      }\n      return resultMap[result] || this.$t('GLOBAL._WZ')\n    },\n\n    // 获取审核结果类型\n    getAuditResultType(result) {\n      const typeMap = {\n        0: 'warning',\n        1: 'primary',\n        2: 'success',\n        3: 'danger'\n      }\n      return typeMap[result] || 'info'\n    }\n  }\n}\n\n//<!-- 移到到src/local/en.json和zh-Hans.json -->\n//\"SopAudit\": {\n//    \"table\": {\n//        \"docId\": \"docId\",\n//        \"operationType\": \"operationType\",\n//        \"oldValue\": \"oldValue\",\n//        \"newValue\": \"newValue\",\n//        \"operatorId\": \"operatorId\",\n//        \"operateTime\": \"operateTime\",\n//        \"clientIp\": \"clientIp\",\n//        \"createdate\": \"createdate\",\n//        \"createuserid\": \"createuserid\",\n//        \"modifydate\": \"modifydate\",\n//        \"modifyuserid\": \"modifyuserid\",\n//        \"deleted\": \"deleted\",\n//    }\n//},\n</script>\n\n<style lang=\"scss\" scoped>\n.el-form-item--small.el-form-item {\n  margin-bottom: 0px;\n}\n\n.search-form-item {\n  .el-form-item__content {\n    width: 180px; // 统一宽度为180px\n  }\n}\n\n.root-head {\n  .search-form {\n    :deep(.el-form) {\n      .el-form-item--small.el-form-item {\n        margin-bottom: 0;\n      }\n    }\n\n    .form-content {\n      padding: 4px;\n\n      .search-area {\n        .search-row {\n          display: flex;\n          align-items: center;\n          gap: 4px;\n\n          .el-form-item {\n            margin: 0;\n            flex: none;\n\n            .el-form-item__label {\n              padding-right: 4px;\n              line-height: 26px;\n              font-size: 12px;\n            }\n\n            .el-form-item__content {\n              line-height: 26px;\n\n              .el-input,\n              .el-select {\n                :deep(.el-input__inner) {\n                  height: 26px;\n                  line-height: 26px;\n                  padding: 0 8px;\n                  font-size: 12px;\n                }\n              }\n            }\n          }\n\n          .action-buttons {\n            display: flex;\n            gap: 4px;\n            margin-left: 4px;\n\n            .el-button {\n              height: 26px;\n              padding: 0 10px;\n              font-size: 12px;\n\n              [class^=\"el-icon-\"] {\n                margin-right: 3px;\n                font-size: 12px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n.mt-8p {\n  margin-top: 8px;\n}\n\n.pd-left {\n  padding-left: 5px\n}\n\n.operation-buttons {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 2px;\n\n  .el-button--mini {\n    padding: 2px;\n    font-size: 11px;\n    border-radius: 3px;\n    min-width: auto;\n  }\n}\n</style>"]}]}