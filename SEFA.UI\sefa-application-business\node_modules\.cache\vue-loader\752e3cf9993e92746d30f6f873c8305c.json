{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\components\\Tipping.vue?vue&type=style&index=0&id=3e72c2e8&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\components\\Tipping.vue", "mtime": 1750254216284}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Tipping.vue"], "names": [], "mappings": ";AAuWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "Tipping.vue", "sourceRoot": "src/views/Producting/Overview/components", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle Tipping precheck\">\r\n        <div class=\"InventorySearchBox\">\r\n            <!-- <div class=\"searchbox\">\r\n                <div :class=\"'searchtipbox status' + TippingStatus\">{{ this.$t('POListTipping.Tipping') }}：{{ filiterTippingStatus(TippingStatus) }}</div>\r\n            </div> -->\r\n            <div class=\"searchbox\">\r\n                <!--  -->\r\n                <el-button @click=\"startTip()\" class=\"tablebtn\" :disabled=\"PrepStatus == '0' ? false : true\" style=\"margin-left: 5px; width: 120px\" size=\"small\" icon=\"el-icon-caret-right\">\r\n                    {{ this.$t('POListTipping.StartTipping') }}\r\n                </el-button>\r\n                <!--  -->\r\n                <el-button @click=\"startScan()\" class=\"tablebtn\" :disabled=\"PrepStatus == '1' ? false : true\" style=\"margin-left: 5px; width: 120px\" size=\"small\" icon=\"el-icon-full-screen\">\r\n                    {{ this.$t('POListTipping.Scan') }}\r\n                </el-button>\r\n                <el-button @click=\"TippingOver()\" class=\"tablebtn\" :disabled=\"PrepStatus == '2' ? false : true\" style=\"margin-left: 5px; width: 120px\" size=\"small\">\r\n                    {{ this.$t('POListTipping.TippingOver') }}\r\n                </el-button>\r\n                <el-button @click=\"TippingOver(true)\" class=\"tablebtn\" :disabled=\"PrepStatus == '1' ? false : true\" style=\"margin-left: 5px; width: 120px\" size=\"small\">\r\n                    {{ this.$t('POListTipping.ForcedCompletion') }}\r\n                </el-button>\r\n                <div class=\"searchtipbox\" v-if=\"PrepStatus == '0'\" style=\"background: #e1f5f6; color: #426777; font-weight: 600\">{{ this.$t('POListTipping.TippingText') }}</div>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <div class=\"searchboxTitle\" style=\"font-size: 16px\" v-if=\"Content != ''\">DCS当前投料信号：{{ Content }}</div>\r\n                <div class=\"searchboxTitle\" style=\"font-size: 16px\" v-if=\"ContentValue != ''\">允许投料属性值：{{ ContentValue }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"tablebox\">\r\n            <el-table :data=\"tableList\" style=\"width: 100%\" height=\"560\">\r\n                <el-table-column\r\n                    v-for=\"(item, index) in header\"\r\n                    :fixed=\"item.fixed ? item.fixed : false\"\r\n                    :key=\"index\"\r\n                    :align=\"item.align\"\r\n                    :prop=\"item.prop ? item.prop : item.value\"\r\n                    :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                    :width=\"item.width\"\r\n                >\r\n                    <template v-slot:header=\"scope\">\r\n                        <span v-if=\"item.icon\">\r\n                            <i :class=\"item.icon\"></i>\r\n                        </span>\r\n                        <span v-if=\"!item.icon\">{{ scope.column.label }}</span>\r\n                    </template>\r\n                    <template slot-scope=\"scope\">\r\n                        <i class=\"el-icon-document\" v-if=\"scope.column.property == 'detail'\" @click=\"opendetailmodel(scope.row)\"></i>\r\n                        <span v-if=\"scope.column.property != 'detail'\">\r\n                            <span v-if=\"scope.column.property == 'Quantity'\">{{ scope.row.Quantity }}{{ scope.row.Unit1 }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity2'\">{{ scope.row.Quantity2 }}{{ scope.row.Unit1 }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'PrepStatus'\">\r\n                                <span v-if=\"Number(scope.row.PrepStatus) >= 7\">\r\n                                    <i class=\"el-icon-check\"></i>\r\n                                </span>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity4'\">{{ scope.row.Quantity4 }}{{ scope.row.Unit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n            <div class=\"paginationbox\">\r\n                <el-pagination\r\n                    @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageOptions.page\"\r\n                    :page-sizes=\"pageOptions.pageSizeitems\"\r\n                    :page-size=\"pageOptions.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next, jumper\"\r\n                    :total=\"pageOptions.total\"\r\n                    background\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <QRcode :ref=\"'QRcode' + EquipmentId\" @getQRcodesRes=\"getQRcodesRes\"></QRcode>\r\n        <el-drawer size=\"55%\" :wrapperClosable=\"false\" @close=\"closeDraw\" :title=\"$t('POListTipping.Tipping')\" :visible.sync=\"detailShow\" direction=\"rtl\">\r\n            <div class=\"InventorySearchBox\">\r\n                <div class=\"searchbox\">\r\n                    <div class=\"inputformbox\" size=\"small\" style=\"width: 300px\">\r\n                        <el-input :placeholder=\"$t('precheck.TraceCode')\" v-model=\"TraceCode\" @keyup.enter.native=\"searchInventory()\">\r\n                            <template slot=\"append\"><i slot=\"suffix\" class=\"el-icon-full-screen\" @click=\"searchInventory()\"></i></template>\r\n                        </el-input>\r\n                    </div>\r\n                    <el-button class=\"tablebtn\" icon=\"el-icon-refresh-left\" @click=\"ShowQRCode()\">\r\n                        {{ $t('Consume.Scan') }}\r\n                    </el-button>\r\n                    <div class=\"preparaStatusbox\" style=\"font-size: 16px\">{{ $t('GLOBAL.Number') }}：{{ count }}</div>\r\n                </div>\r\n            </div>\r\n            <el-table :data=\"drawertableList\" style=\"width: 100%\">\r\n                <el-table-column\r\n                    v-for=\"(item, index) in drawerheader\"\r\n                    :key=\"index\"\r\n                    :fixed=\"item.fixed ? item.fixed : false\"\r\n                    :align=\"item.align\"\r\n                    :prop=\"item.prop ? item.prop : item.value\"\r\n                    :label=\"$t(`$vuetify.dataTable.${tableId2}.${item.value}`)\"\r\n                    :width=\"item.width\"\r\n                >\r\n                    <template v-slot:header=\"scope\">\r\n                        <span v-if=\"item.icon\">\r\n                            <i :class=\"item.icon\"></i>\r\n                        </span>\r\n                        <span v-if=\"!item.icon\">{{ scope.column.label }}</span>\r\n                    </template>\r\n                    <template slot-scope=\"scope\">\r\n                        <span v-if=\"scope.column.property != 'detail'\">\r\n                            <span v-if=\"scope.column.property == 'Material'\">\r\n                                <div>{{ scope.row.MaterialCode }}</div>\r\n                                <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.Quantity }}{{ scope.row.Unit1 }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Precheckestatus'\">\r\n                                <div\r\n                                    class=\"preparaTableStatusbox\"\r\n                                    style=\"color: black\"\r\n                                    :style=\"{ background: scope.row.Precheckestatus == '0' ? '#FFA500' : scope.row.Precheckestatus == '1' ? '#FFA500' : '#3DCD58' }\"\r\n                                >\r\n                                    {{ scope.row.Precheckestatus == '0' ? '未检查' : scope.row.Precheckestatus == '1' ? '未投料' : '已投料' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n        </el-drawer>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { POManagemenTipping, TippingDrawColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { MygetContentValue, MygetContent, TippingCount, GetTippingSclist, GetBatchEntity, GetTippingMlistView, OverTipping, StartTipping, ScanTipping } from '@/api/Inventory/Overview.js';\r\n\r\nexport default {\r\n    name: 'Consume',\r\n    data() {\r\n        return {\r\n            QuickSearch: '',\r\n            TippingStatus: 1,\r\n            pageOptions: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            header: POManagemenTipping,\r\n            tableId2: 'INV_YJC',\r\n            tableList: [],\r\n            tableId: 'PRO_Tipping',\r\n            PrepStatus: '',\r\n            Content: '',\r\n            ContentValue: '',\r\n            countFlag: false,\r\n            detailShow: false,\r\n            TraceCode: '',\r\n            count: '',\r\n            drawertableList: [],\r\n            drawerheader: TippingDrawColumn,\r\n            BatchId: '',\r\n            RunEquipmentId: '',\r\n            SortOrder: '',\r\n            PoExecutionId: '',\r\n            EquipmentId: ''\r\n        };\r\n    },\r\n    mounted() {\r\n        this.changePagination();\r\n    },\r\n    methods: {\r\n        ShowQRCode() {\r\n            this.$refs[`QRcode${this.EquipmentId}`].getQRcode();\r\n        },\r\n        // 获取查询结果\r\n        getQRcodesRes(val) {\r\n            console.log(val, 123123);\r\n            this.TraceCode = val.text;\r\n            this.searchInventory();\r\n        },\r\n        getEquipmentModal(item, Equipmentitem) {\r\n            if (Equipmentitem) {\r\n                this.EquipmentId = Equipmentitem.ID;\r\n                this.BatchId = Equipmentitem.BatchId;\r\n                this.RunEquipmentId = Equipmentitem.RunEquipmentId;\r\n                this.PoExecutionId = Equipmentitem.ExecutionId;\r\n                this.getContent();\r\n            } else {\r\n                this.BatchId = '';\r\n                this.RunEquipmentId = '';\r\n                this.PoExecutionId = '';\r\n            }\r\n            this.GetTippinglist();\r\n            this.BatchEntity();\r\n        },\r\n        async getContent() {\r\n            let params = {\r\n                ExecutionId: this.PoExecutionId,\r\n                Name: 'RequestFeeding'\r\n            };\r\n            let res = await MygetContent(params);\r\n            let data = res.response;\r\n            this.Content = data.Content;\r\n        },\r\n        async getContentValue() {\r\n            let params = {\r\n                EquipmentId: this.EquipmentId,\r\n                FunctionCode: 'Tipping',\r\n                PropertyCode: 'ButtonEnable'\r\n            };\r\n            let res = await MygetContentValue(params);\r\n            if (res.response == null) {\r\n                res.response = '';\r\n            }\r\n            this.ContentValue = res.response;\r\n        },\r\n        async TippingOver(parm) {\r\n            let params = {\r\n                SortOrder: this.SortOrder,\r\n                BatchId: this.BatchId,\r\n                IsForcedCompletion: false,\r\n                RunEquipmentId: this.RunEquipmentId,\r\n                PoExecutionId: this.PoExecutionId\r\n            };\r\n            if (parm) {\r\n                params.IsForcedCompletion = parm;\r\n            }\r\n            let res = await OverTipping(params);\r\n            if (res.success) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n                this.BatchEntity();\r\n            }\r\n        },\r\n        getsearch() {\r\n            // this.MyGetTippingSclist();\r\n            this.GetTippinglist();\r\n            this.BatchEntity();\r\n        },\r\n        async BatchEntity() {\r\n            if (this.BatchId != '') {\r\n                let params = {\r\n                    BatchId: this.BatchId,\r\n                    RunEquipmentId: this.RunEquipmentId,\r\n                    PoExecutionId: this.PoExecutionId\r\n                };\r\n                let res = await GetBatchEntity(params);\r\n                if (res) {\r\n                    this.SortOrder = res.response.SortOrder;\r\n                    this.PrepStatus = res.response.Status;\r\n                }\r\n            } else {\r\n                this.PrepStatus = '';\r\n            }\r\n        },\r\n        closeDraw() {\r\n            this.GetTippinglist();\r\n        },\r\n        async startScan() {\r\n            this.reLoadScan();\r\n            this.detailShow = true;\r\n        },\r\n        async searchInventory() {\r\n            if (this.TraceCode === '' || this.TraceCode === null) {\r\n                Message({\r\n                    message: `${this.$t('ConsumptionHistory.NonSSCC')}`,\r\n                    type: 'error'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                Tracecode: this.TraceCode,\r\n                RunEquipmentId: this.RunEquipmentId,\r\n                PoExecutionId: this.PoExecutionId\r\n            };\r\n            let res = await ScanTipping(params);\r\n            if (res.success) {\r\n                this.TraceCode = '';\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'success'\r\n                });\r\n                this.reLoadScan();\r\n                this.GetTippinglist();\r\n                this.BatchEntity();\r\n            }\r\n        },\r\n        async reLoadScan() {\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                SortOrder: this.SortOrder\r\n            };\r\n            let res = await GetTippingSclist(params);\r\n            this.drawertableList = res.response;\r\n            let params2 = {\r\n                BatchId: this.BatchId,\r\n                SortOrder: this.SortOrder\r\n            };\r\n            let res2 = await TippingCount(params2);\r\n            this.count = res2.response;\r\n            if (this.count.split('/')[0] == this.count.split('/')[1]) {\r\n                this.countFlag = true;\r\n            } else {\r\n                this.countFlag = false;\r\n            }\r\n        },\r\n        async startTip() {\r\n            let parmas = {\r\n                SortOrder: this.SortOrder,\r\n                BatchId: this.BatchId,\r\n                RunEquipmentId: this.RunEquipmentId,\r\n                PoExecutionId: this.PoExecutionId\r\n            };\r\n            let res = await StartTipping(parmas);\r\n            this.GetTippinglist();\r\n            this.BatchEntity();\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        changePagination() {\r\n            let el2 = document.getElementsByClassName(`el-select-dropdown__item`);\r\n            for (let i = 0; i < el2.length; i++) {\r\n                el2[i].innerHTML = el2[i].innerHTML.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            }\r\n        },\r\n        async GetTippinglist() {\r\n            let params = {\r\n                BatchId: this.BatchId,\r\n                RunEquipmentId: this.RunEquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res = await GetTippingMlistView(params);\r\n            if (res) {\r\n                this.tableList = res.response.data;\r\n                this.pageOptions.total = res.response.dataCount;\r\n            }\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n        },\r\n        handleSizeChange(val) {\r\n            this.pageOptions.pageSize = val;\r\n            this.GetTippinglist();\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.pageOptions.page = val;\r\n            this.GetTippinglist();\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.Tipping {\r\n    .searchtipbox {\r\n        margin: 0 5px;\r\n        height: 30px;\r\n        padding: 0 2vh;\r\n        border-radius: 5px;\r\n        display: flex;\r\n        margin-bottom: 0.5vh;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: #fff;\r\n        font-size: 14px;\r\n    }\r\n    .searchboxtitle {\r\n        font-size: 1.7vh;\r\n        color: #767777;\r\n        padding-bottom: 5px;\r\n        margin-left: 10px;\r\n    }\r\n\r\n    .el-tabs {\r\n        height: 97%;\r\n    }\r\n\r\n    .subsubtabs {\r\n        .el-tabs--border-card {\r\n            border: 0 !important;\r\n            box-shadow: none !important;\r\n        }\r\n    }\r\n\r\n    .paginationbox {\r\n        height: 10vh;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin-top: 10px;\r\n\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 47%;\r\n            text-align: right;\r\n        }\r\n\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n\r\n    .splitdetailbox {\r\n        padding-bottom: 10px;\r\n        border: 1px solid #e8e8e8;\r\n        margin-bottom: 5px;\r\n\r\n        .splitdetailboxtitle {\r\n            background: #f5f5f5;\r\n            height: 3.5vh;\r\n            display: flex;\r\n            align-items: center;\r\n            padding-left: 5px;\r\n            font-size: 1.1rem;\r\n            color: #303133;\r\n        }\r\n\r\n        .detailsnote {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 0.9rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n\r\n        .splitdetailboxtitleTag {\r\n            margin-left: 5px;\r\n            background: #5cb85c;\r\n            color: #fff;\r\n            border-color: #5cb85c;\r\n        }\r\n    }\r\n}\r\n\r\n#Tipping {\r\n    .el-input {\r\n        width: 250px !important;\r\n    }\r\n\r\n    .el-select {\r\n        width: 250px !important;\r\n    }\r\n}\r\n</style>\r\n"]}]}