{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\productionManagement\\ResourceDefinition\\components\\drawer.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\productionManagement\\ResourceDefinition\\components\\drawer.vue", "mtime": 1750254216359}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAiEA;AACA;AACA;AACA;AACA;AACA;AACA;EACAA,cADA;EAEAC;IACAC,mBADA;IAEAC,eAFA;IAGAC,sBAHA;IAIAC,cAJA;IAKAC;EALA,CAFA;;EASAC;IACA;MACAC,cADA;MAEAC,aAFA;MAGAC,aAHA;MAIAC,cAJA;MAKAC,aALA;MAMAC,aANA;MAOAC,iBAPA;MAQAC,sDARA;MASAC,8CATA;MAUAC,6DAVA;MAWAC,WAXA;MAYAC,cAZA;MAaAC,gBAbA;MAcAC;IAdA;EAgBA,CA1BA;;EA2BAC;IACAC;MACA;MACA;MACA;QACA;MACA,CAFA;IAGA,CAPA;;IAQAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CAfA;;IAgBAC;MACA;IACA,CAlBA;;IAmBAC;MACA;QACA;UACA;UACA;UACA;;QACA;UACA;UACA;UACA;;QACA;UACA;UACA;UACA;;QACA;UACA;MAdA;IAgBA,CApCA;;IAqCAC;MACA;QACA;UACAC;UACA;UACA;;QACA;UACAA;UACA;UACA;;QACA;UACAA;UACA;UACA;;QACA;UACA;MAdA;IAgBA,CAtDA;;IAuDA;MAAAC;IAAA;MACA;QACAC,4BADA;QAEAC,mCAFA;QAGAC,kCAHA;QAIAC;MAJA,GAKAC,IALA,CAKA;QACA;UACA;YACAC;cAAAC;YAAA;cACA;cACA;YACA,CAHA;YAIA;;UACA;YACAD;cAAAC;YAAA;cACA;cACA;YACA,CAHA;YAIA;;UACA;YACAC;cAAAD;YAAA;cACA;cACA;YACA,CAHA;YAIA;;UACA;YACA;QApBA;MAsBA,CA5BA,EA4BAE,KA5BA,CA4BAC;QACAC;MACA,CA9BA;IAgCA,CAxFA;;IAyFA;IACAC;MACA;QACA;UACA;UACA;;QACA;UACA;UACA;;QACA;UACA;UACA;;QACA;UACA;MAXA;IAaA,CAxGA;;IAyGA;IACA;MACA;MACA;QAAAC;MAAA;QACAC,QADA;QAEAC,mCAFA;QAGA;MAHA;MAKA;MACA;IACA,CAnHA;;IAoHA;MACA;MACA;QAAAF;MAAA;QACAC,QADA;QAEAC,mCAFA;QAGA;MAHA;MAKA;MACA;IACA,CA7HA;;IA8HA;MACA;MACA;QAAAF;MAAA;QACAE,mCADA;QAEA;MAFA;MAIA;MACA;IACA,CAtIA;;IAuIA;IACAC;MACA;;MACA;QACA;UAAAC;UAAAC;QAAA;MACA;IACA,CA7IA;;IA8IA;IACAC;MACA;IACA,CAjJA;;IAkJAC;MACA;MACA;IACA;;EArJA;AA3BA", "names": ["name", "components", "FormDialogOperation", "FromDialogPhase", "FromDialogPhaseMachine", "SalesContainer", "ForemDialogSales", "data", "searchForm", "tableData", "tableName", "loading", "drawer", "tabIndex", "sapEquipmentId", "hansObjOperation", "hans<PERSON>bj<PERSON><PERSON><PERSON>", "hansObjPhaseMachine", "parentId", "currentRow", "showSales", "salesData", "methods", "showSalesView", "show", "handleClose", "changeTabs", "showDialog", "val", "ID", "title", "message", "confirmText", "cancelText", "then", "deleteOperation", "msg", "deleteSapSegmentEquipment", "catch", "err", "console", "refreshTableData", "response", "Level", "SapEquipmentId", "initTableHead", "field", "label", "getSearchBtn", "showSalesDialog"], "sourceRoot": "src/views/productionManagement/ResourceDefinition/components", "sources": ["drawer.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-drawer class=\"drawer\" :visible.sync=\"drawer\" :direction=\"'rtl'\" :before-close=\"handleClose\" size=\"50%\"\r\n      :with-header=\"!showSales\">\r\n      <div slot=\"title\" class=\"title-box\">\r\n        <span>{{ `${currentRow.Name} | ${currentRow.Code}` }}</span>\r\n      </div>\r\n      <section v-if=\"!showSales\">\r\n        <el-tabs v-model=\"tabIndex\" type=\"border-card\" @tab-click=\"changeTabs\">\r\n          <el-tab-pane label=\"工段\" name=\"1\"></el-tab-pane>\r\n          <el-tab-pane label=\"工序\" name=\"2\"></el-tab-pane>\r\n          <el-tab-pane label=\"工序与设备\" name=\"3\"></el-tab-pane>\r\n        </el-tabs>\r\n        <div class=\"InventorySearchBox mt5\">\r\n          <div class=\"searchbox pd5\">\r\n            <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\r\n              <el-form-item :label=\"$t('GLOBAL._SSL')\">\r\n                <el-input clearable v-model=\"searchForm.Key\"></el-input>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button icon=\"el-icon-search\" @click=\"getSearchBtn\">{{ $t('GLOBAL._CX') }}</el-button>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">{{\r\n                  $t('GLOBAL._XZ') }}\r\n                </el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n        </div>\r\n        <div class=\"table-box\">\r\n          <el-table v-loading=\"loading\" :data=\"tableData\" element-loading-text=\"拼命加载中\"\r\n            element-loading-spinner=\"el-icon-loading\" style=\"width: 100%\" height=\"83vh\">\r\n            <el-table-column v-for=\"(item, index) in tableName\" :key=\"index\" :prop=\"item.field\" :label=\"item.label\">\r\n              <template slot-scope=\"scope\">\r\n                <div v-if=\"item.field === 'SegmentName'\" class=\"combination\">\r\n                  <i v-if=\"tabIndex === '2'\" class=\"el-icon-document\" @click=\"showSalesView(scope.row)\"></i>\r\n                  <span>\r\n                   {{ scope.row.SegmentName }}\r\n                  </span>\r\n                </div>\r\n                <span v-else> {{ scope.row[item.field] }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._BJ') }}</el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </section>\r\n      <section v-else>\r\n        <SalesContainer ref=\"sales\" @show-sales=\"showSales = false\" :data=\"salesData\" @show-dialog=\"showSalesDialog\" />\r\n      </section>\r\n    </el-drawer>\r\n    <FormDialogOperation @saveForm=\"refreshTableData\" ref=\"operation\" />\r\n    <FromDialogPhase @saveForm=\"refreshTableData\" ref=\"phase\" />\r\n    <FromDialogPhaseMachine @saveForm=\"refreshTableData\" ref=\"phaseMachine\" />\r\n    <ForemDialogSales ref=\"salesDialog\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getSapSegmentList, getSapSegment, getSapSegmentEquipmentList, deleteOperation, deleteSapSegmentEquipment } from '@/api/productionManagement/Formula';\r\nimport FormDialogOperation from './form-dialog-operation.vue'\r\nimport FromDialogPhase from './form-dialog-phase.vue';\r\nimport FromDialogPhaseMachine from './form-dialog-phaseMachine'\r\nimport SalesContainer from './salesContainer.vue'\r\nimport ForemDialogSales from './form-dialog-sales.vue'\r\nexport default {\r\n  name: 'drawer',\r\n  components: {\r\n    FormDialogOperation,\r\n    FromDialogPhase,\r\n    FromDialogPhaseMachine,\r\n    SalesContainer,\r\n    ForemDialogSales\r\n  },\r\n  data() {\r\n    return {\r\n      searchForm: {},\r\n      tableData: [],\r\n      tableName: [],\r\n      loading: false,\r\n      drawer: false,\r\n      tabIndex: '1',\r\n      sapEquipmentId: 0,\r\n      hansObjOperation: this.$t('Formula.Resouce_Operation'),\r\n      hansObjPhase: this.$t('Formula.Resouce_Phase'),\r\n      hansObjPhaseMachine: this.$t('Formula.Resouce_Phase_Machine'),\r\n      parentId: 0,\r\n      currentRow: {},\r\n      showSales: false,\r\n      salesData: {}\r\n    }\r\n  },\r\n  methods: {\r\n    showSalesView(row) {\r\n      this.salesData = row\r\n      this.showSales = true\r\n      this.$nextTick(_ => {\r\n        this.$refs.sales.getTableData()\r\n      })\r\n    },\r\n    show(data) {\r\n      this.tabIndex = '1'\r\n      this.sapEquipmentId = data.ID\r\n      this.currentRow = data\r\n      this.getOperationData()\r\n      this.initTableHead(this.hansObjOperation)\r\n      this.drawer = true\r\n    },\r\n    handleClose() {\r\n      this.drawer = false\r\n    },\r\n    changeTabs() {\r\n      switch (this.tabIndex) {\r\n        case '1':\r\n          this.initTableHead(this.hansObjOperation)\r\n          this.getOperationData()\r\n          break;\r\n        case '2':\r\n          this.initTableHead(this.hansObjPhase)\r\n          this.getPhaseData()\r\n          break;\r\n        case '3':\r\n          this.initTableHead(this.hansObjPhaseMachine)\r\n          this.getPhaseMechineData()\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    showDialog(val) {\r\n      switch (this.tabIndex) {\r\n        case '1':\r\n          val.SapEquipmentId = this.sapEquipmentId\r\n          this.$refs.operation.show(val)\r\n          break;\r\n        case '2':\r\n          val.SapEquipmentId = this.sapEquipmentId\r\n          this.$refs.phase.show(val)\r\n          break;\r\n        case '3':\r\n          val.SapEquipmentId = this.sapEquipmentId\r\n          this.$refs.phaseMachine.show(val)\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    async delRow({ ID }) {\r\n      this.$confirms({\r\n        title: this.$t('GLOBAL._TS'),\r\n        message: this.$t('GLOBAL._COMFIRM'),\r\n        confirmText: this.$t('GLOBAL._QD'),\r\n        cancelText: this.$t('GLOBAL._QX')\r\n      }).then(() => {\r\n        switch (this.tabIndex) {\r\n          case '1':\r\n            deleteOperation([ID]).then(({ msg }) => {\r\n              this.$message.success(msg)\r\n              this.getOperationData()\r\n            })\r\n            break;\r\n          case '2':\r\n            deleteOperation([ID]).then(({ msg }) => {\r\n              this.$message.success(msg)\r\n              this.getPhaseData()\r\n            })\r\n            break;\r\n          case '3':\r\n            deleteSapSegmentEquipment([ID]).then(({ msg }) => {\r\n              this.$message.success(msg)\r\n              this.getPhaseMechineData()\r\n            })\r\n            break;\r\n          default:\r\n            break;\r\n        }\r\n      }).catch(err => {\r\n        console.log(err);\r\n      });\r\n\r\n    },\r\n    // emit刷新数据\r\n    refreshTableData() {\r\n      switch (this.tabIndex) {\r\n        case '1':\r\n          this.getOperationData()\r\n          break;\r\n        case '2':\r\n          this.getPhaseData()\r\n          break;\r\n        case '3':\r\n          this.getPhaseMechineData()\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    // 获取数据\r\n    async getOperationData() {\r\n      this.loading = true\r\n      const { response } = await getSapSegmentList({\r\n        Level: 1,\r\n        SapEquipmentId: this.sapEquipmentId,\r\n        ...this.searchForm\r\n      })\r\n      this.loading = false\r\n      this.tableData = response\r\n    },\r\n    async getPhaseData() {\r\n      this.loading = true\r\n      const { response } = await getSapSegmentList({\r\n        Level: 2,\r\n        SapEquipmentId: this.sapEquipmentId,\r\n        ...this.searchForm\r\n      })\r\n      this.loading = false\r\n      this.tableData = response\r\n    },\r\n    async getPhaseMechineData() {\r\n      this.loading = true\r\n      const { response } = await getSapSegmentEquipmentList({\r\n        SapEquipmentId: this.sapEquipmentId,\r\n        ...this.searchForm\r\n      })\r\n      this.loading = false\r\n      this.tableData = response\r\n    },\r\n    // 初始化表头\r\n    initTableHead(obj) {\r\n      this.tableName = []\r\n      for (let key in obj) {\r\n        this.tableName.push({ field: key, label: obj[key] })\r\n      }\r\n    },\r\n    // 搜索\r\n    getSearchBtn() {\r\n      this.refreshTableData()\r\n    },\r\n    showSalesDialog(row) {\r\n      // console.log(row);\r\n      this.$refs.salesDialog.show(row)\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.drawer {\r\n  :deep(.el-drawer__body) {\r\n    background-color: #FFFFFF;\r\n    overflow-y: hidden\r\n  }\r\n\r\n  :deep(.el-form--inline) {\r\n    height: 32px;\r\n  }\r\n\r\n  .title-box {\r\n    font-size: 18px;\r\n    color: #909399;\r\n  }\r\n\r\n  .mt5 {\r\n    margin-top: 5px;\r\n  }\r\n\r\n  .pd5 {\r\n    padding: 5px;\r\n  }\r\n\r\n  .table-box {\r\n    padding: 0 10px;\r\n\r\n    .combination {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n\r\n    i {\r\n      margin-right: 5px;\r\n      font-size: 15px !important;\r\n      color: #67c23a;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}