{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\api\\SOP\\sopAudit.js", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\api\\SOP\\sopAudit.js", "mtime": 1750249203957}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\eslint-loader\\index.js", "mtime": 1743379020994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "configUrl", "baseURL", "process", "env", "VUE_APP_SERVE", "baseURL_DFM", "getSopAuditList", "data", "url", "method", "saveSopAuditForm", "getSopAuditDetail", "id", "delSopAudit", "i18n", "sopAuditColumn", "text", "t", "value", "width"], "sources": ["C:/work/syngentagroup/SEFA_XZD/SEFA.UI/sefa-application-business/src/api/SOP/sopAudit.js"], "sourcesContent": ["import request from '@/util/request'\nimport { configUrl } from '@/config'\nconst baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM\n\n/**\n * 文档变更审计表分页查询\n * @param {查询条件} data\n */\nexport function getSopAuditList(data) {\n    return request({\n        url: baseURL + '/api/SopAudit/GetPageList',\n        method: 'post',\n        data\n    })\n}\n\n/**\n * 保存文档变更审计表\n * @param data\n */\nexport function saveSopAuditForm(data) {\n    return request({\n        url: baseURL + '/api/SopAudit/SaveForm',\n        method: 'post',\n        data\n    })\n}\n\n/**\n * 获取文档变更审计表详情\n * @param {Id}\n */\nexport function getSopAuditDetail(id) {\n    return request({\n        url: baseURL + '/api/SopAudit/GetEntity',\n        method: 'post',\n        data: id\n    })\n}\n\n/**\n * 删除文档变更审计表\n * @param {主键} data\n */\nexport function delSopAudit(data) {\n    return request({\n        url: baseURL + '/api/SopAudit/Delete',\n        method: 'post',\n        data\n    })\n}\n\nimport i18n from '@/plugins/i18n';\n\n// SOP审核页面列配置 - 包含与SOP文件管理一致的文档信息栏位加上操作栏\nexport const sopAuditColumn = [\n    { text: () => i18n.t('SOP.DocName'), value: 'DocName', width: '200px' },\n    { text: () => i18n.t('SOP.DocCode'), value: 'DocCode', width: '160px' },\n    { text: () => i18n.t('SOP.DocVersion'), value: 'DocVersion', width: '120px' },\n    { text: () => i18n.t('SOP.FilePath'), value: 'FilePath', width: '200px' },\n    { text: () => i18n.t('SOP.FileSize'), value: 'FileSize', width: '120px' },\n    { text: () => i18n.t('SOP.DocStatus'), value: 'DocStatus', width: '100px' },\n    { text: () => i18n.t('SOP.OperationType'), value: 'OperationType', width: '120px' },\n    { text: () => i18n.t('SOP.OperatorId'), value: 'OperatorId', width: '120px' },\n    { text: () => i18n.t('SOP.OperateTime'), value: 'OperateTime', width: '160px' },\n    { text: () => i18n.t('SOP.AuditResult'), value: 'AuditResult', width: '120px' }\n];\n\n\n"], "mappings": "AAAA,OAAOA,OAAP,MAAoB,gBAApB;AACA,SAASC,SAAT,QAA0B,UAA1B;AACA,MAAMC,OAAO,GAAGD,SAAS,CAACE,OAAO,CAACC,GAAR,CAAYC,aAAb,CAAT,CAAqCC,WAArD;AAEA;AACA;AACA;AACA;;AACA,OAAO,SAASC,eAAT,CAAyBC,IAAzB,EAA+B;EAClC,OAAOR,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,2BADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF;EAHW,CAAD,CAAd;AAKH;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASG,gBAAT,CAA0BH,IAA1B,EAAgC;EACnC,OAAOR,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,wBADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF;EAHW,CAAD,CAAd;AAKH;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASI,iBAAT,CAA2BC,EAA3B,EAA+B;EAClC,OAAOb,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,yBADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF,IAAI,EAAEK;EAHK,CAAD,CAAd;AAKH;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASC,WAAT,CAAqBN,IAArB,EAA2B;EAC9B,OAAOR,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,sBADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF;EAHW,CAAD,CAAd;AAKH;AAED,OAAOO,IAAP,MAAiB,gBAAjB,C,CAEA;;AACA,OAAO,MAAMC,cAAc,GAAG,CAC1B;EAAEC,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,aAAP,CAAd;EAAqCC,KAAK,EAAE,SAA5C;EAAuDC,KAAK,EAAE;AAA9D,CAD0B,EAE1B;EAAEH,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,aAAP,CAAd;EAAqCC,KAAK,EAAE,SAA5C;EAAuDC,KAAK,EAAE;AAA9D,CAF0B,EAG1B;EAAEH,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,gBAAP,CAAd;EAAwCC,KAAK,EAAE,YAA/C;EAA6DC,KAAK,EAAE;AAApE,CAH0B,EAI1B;EAAEH,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,cAAP,CAAd;EAAsCC,KAAK,EAAE,UAA7C;EAAyDC,KAAK,EAAE;AAAhE,CAJ0B,EAK1B;EAAEH,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,cAAP,CAAd;EAAsCC,KAAK,EAAE,UAA7C;EAAyDC,KAAK,EAAE;AAAhE,CAL0B,EAM1B;EAAEH,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,eAAP,CAAd;EAAuCC,KAAK,EAAE,WAA9C;EAA2DC,KAAK,EAAE;AAAlE,CAN0B,EAO1B;EAAEH,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,mBAAP,CAAd;EAA2CC,KAAK,EAAE,eAAlD;EAAmEC,KAAK,EAAE;AAA1E,CAP0B,EAQ1B;EAAEH,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,gBAAP,CAAd;EAAwCC,KAAK,EAAE,YAA/C;EAA6DC,KAAK,EAAE;AAApE,CAR0B,EAS1B;EAAEH,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,iBAAP,CAAd;EAAyCC,KAAK,EAAE,aAAhD;EAA+DC,KAAK,EAAE;AAAtE,CAT0B,EAU1B;EAAEH,IAAI,EAAE,MAAMF,IAAI,CAACG,CAAL,CAAO,iBAAP,CAAd;EAAyCC,KAAK,EAAE,aAAhD;EAA+DC,KAAK,EAAE;AAAtE,CAV0B,CAAvB"}]}