{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\index.vue?vue&type=style&index=0&id=0814010e&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\index.vue", "mtime": 1750254216296}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAy6BA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/Producting/Overview", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle overview\">\r\n        <div class=\"InventorySearchBox\">\r\n            <div class=\"searchbox\">\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-back\" @click=\"back()\">{{ this.$t('Overview.Back') }}</el-button>\r\n                <div class=\"searchboxtitle\">\r\n                    {{ viewtitle }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <el-tabs v-model=\"activeName\" type=\"border-card\" @tab-click=\"handleClick\">\r\n            <el-tab-pane :label=\"$t('Overview.Overview')\" name=\"1\">\r\n                <div class=\"tablebox\">\r\n                    <el-table :data=\"tableList\" style=\"width: 100%\" height=\"700\">\r\n                        <el-table-column\r\n                            v-for=\"(item, index) in header\"\r\n                            :key=\"index\"\r\n                            :align=\"item.align\"\r\n                            :prop=\"item.prop ? item.prop : item.value\"\r\n                            :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                            :width=\"item.width\"\r\n                        >\r\n                            <template slot-scope=\"scope\">\r\n                                <span v-if=\"scope.row.ProcessOrder && scope.column.property == 'Complete'\">\r\n                                    <el-progress\r\n                                        :text-inside=\"true\"\r\n                                        color=\"#3dcd58\"\r\n                                        text-color=\"#000000\"\r\n                                        :stroke-width=\"26\"\r\n                                        :percentage=\"Number(((scope.row.Total / scope.row.TargetQuantity) * 100).toFixed(2))\"\r\n                                    ></el-progress>\r\n                                </span>\r\n                                <span v-else-if=\"scope.column.property == 'PlantNode'\">\r\n                                    <div>{{ scope.row.EquipmentCode }}</div>\r\n                                    <div style=\"color: #808080\">{{ scope.row.EquipmentName }}</div>\r\n                                </span>\r\n                                <span v-else-if=\"scope.column.property == 'Material'\">\r\n                                    <div>{{ scope.row.MaterialCode }}</div>\r\n                                    <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                                </span>\r\n                                <span v-else-if=\"scope.column.property == 'BatchQty'\">{{ scope.row.BatchQty }}{{ scope.row.Unit1 }}</span>\r\n                                <span v-else-if=\"scope.column.property == 'Sequence'\">{{ scope.row.ProcessOrder != null ? scope.row.Sequence : '' }}</span>\r\n                                <span v-else>{{ scope.row[item.prop] }}</span>\r\n                            </template>\r\n                        </el-table-column>\r\n                    </el-table>\r\n                    <div class=\"paginationbox\">\r\n                        <el-pagination\r\n                            @size-change=\"handleSizeChange\"\r\n                            @current-change=\"handleCurrentChange\"\r\n                            :current-page=\"pageOptions.page\"\r\n                            :page-sizes=\"pageOptions.pageSizeitems\"\r\n                            :page-size=\"pageOptions.pageSize\"\r\n                            layout=\"total, sizes, prev, pager, next\"\r\n                            :total=\"pageOptions.total\"\r\n                            background\r\n                        ></el-pagination>\r\n                    </div>\r\n                </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane v-if=\"ShowPOList\" :label=\"$t('Overview.POList')\" name=\"2\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"datebox\">\r\n                            <div class=\"datepickbox\">\r\n                                <el-date-picker\r\n                                    v-model=\"timepicker\"\r\n                                    type=\"daterange\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                                    range-separator=\"-\"\r\n                                    :start-placeholder=\"$t('DFM_RL._KSRQ')\"\r\n                                    :end-placeholder=\"$t('DFM_RL._JSRQ')\"\r\n                                ></el-date-picker>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"inputformbox\" :style=\"{ width: item.width }\" v-for=\"(item, index) in searchlist\" :key=\"index\">\r\n                            <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\" :myid=\"item.id\" :placeholder=\"item.name\"></el-input>\r\n                            <el-select :style=\"{ width: item.width }\" v-model=\"item.value\" v-if=\"item.type == 'select'\" :myid=\"item.id\" :placeholder=\"item.name\">\r\n                                <el-option v-for=\"(it, ind) in item.option\" :key=\"ind\" :label=\"it.value\" :value=\"it.key\"></el-option>\r\n                            </el-select>\r\n                        </div>\r\n                        <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                        <el-button size=\"small\" style=\"margin-left: 5px\" icon=\"el-icon-s-help\" @click=\"getempty()\">{{ this.$t('GLOBAL._CZ') }}</el-button>\r\n                    </div>\r\n                </div>\r\n                <div class=\"tablebox\">\r\n                    <el-table :data=\"AvailablePOManagemenList\" style=\"width: 100%\" height=\"670\">\r\n                        <el-table-column\r\n                            v-for=\"(item, index) in Availableheader\"\r\n                            :key=\"index\"\r\n                            :align=\"item.align\"\r\n                            :prop=\"item.prop ? item.prop : item.value\"\r\n                            :label=\"$t(`$vuetify.dataTable.${AvailabletableId}.${item.value}`)\"\r\n                            :width=\"item.width\"\r\n                        >\r\n                            <template slot-scope=\"scope\">\r\n                                <!-- <span v-if=\"scope.column.property == 'operate'\">\r\n                                    <el-button size=\"mini\" class=\"operatebtn\" v-if=\"scope.row.Status > 1 && scope.row.RunningCount == 0\" @click=\"startOrder(scope)\" icon=\"el-icon-video-play\">\r\n                                        {{ $t('Overview.start') }}\r\n                                    </el-button>\r\n                                </span> -->\r\n                                <span v-if=\"scope.column.property == 'PlanStartTime'\">{{ $dayjs(scope.row.PlanStartTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                                <span v-else-if=\"scope.column.property == 'PlanEndTime'\">{{ $dayjs(scope.row.PlanEndTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                                <span v-else-if=\"scope.column.property == 'IsHavePreservative'\">\r\n                                    <i :class=\"scope.row[item.value] === '1' ? 'el-icon-star-on' : ''\"></i>\r\n                                </span>\r\n                                <span v-else-if=\"scope.column.property == 'LineNominalSpeed'\">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>\r\n                                <span v-else>{{ scope.row[item.prop] }}</span>\r\n                            </template>\r\n                        </el-table-column>\r\n                    </el-table>\r\n                    <div class=\"paginationbox\">\r\n                        <el-pagination\r\n                            @size-change=\"handleSizeChange2\"\r\n                            @current-change=\"handleCurrentChange2\"\r\n                            :current-page=\"pageOptions2.page\"\r\n                            :page-sizes=\"pageOptions2.pageSizeitems\"\r\n                            :page-size=\"pageOptions2.pageSize\"\r\n                            layout=\"total, sizes, prev, pager, next\"\r\n                            :total=\"pageOptions2.total\"\r\n                            background\r\n                        ></el-pagination>\r\n                    </div>\r\n                    <el-dialog :title=\"$t('Overview.StartOrder')\" id=\"Startdialog\" :visible.sync=\"StartModel\" :width=\"IsPack == '0' ? '1050px' : '650px'\">\r\n                        <span slot=\"title\" class=\"dialog-title\">\r\n                            <div class=\"dialogtitlebox\">\r\n                                {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.StartOrder') }}\r\n                                <div class=\"dialogsubtitlebox\" style=\"display: inline\">{{ chooseItem.ProcessOrder }}</div>\r\n                            </div>\r\n                        </span>\r\n                        <div class=\"splitdetailbox\">\r\n                            <div class=\"splitdetailboxtitle\">{{ chooseItem.MaterialCode }}-{{ chooseItem.MaterialName }}</div>\r\n                            <div class=\"detailsnote\" v-if=\"runningCode != '' && !chooseItem.isResume\">\r\n                                {{ $t('Overview.Note1') }}\r\n                                <span style=\"font-weight: 600\">{{ runningCode }}</span>\r\n                                {{ $t('Overview.Note2') }}\r\n                            </div>\r\n                            <div style=\"display: flex\">\r\n                                <div :style=\"{ width: IsPack == '0' ? '100%' : '100%' }\">\r\n                                    <div class=\"dialogdetailbox\">\r\n                                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: '10%' }\">{{ $t('Overview.ChooseEquipment') + ' *' }}</div>\r\n                                        <div class=\"dialogdetailsinglevalue \" :style=\"{ width: '87%' }\">\r\n                                            <el-select style=\"width: 92%\" v-model=\"MyEquipment\" @change=\"getMyEquipment()\" filterable>\r\n                                                <el-option v-for=\"(it, index) in MyEquipmentList\" :key=\"index\" :label=\"it.EquipmentName\" :value=\"it.ID\"></el-option>\r\n                                            </el-select>\r\n                                        </div>\r\n                                    </div>\r\n                                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Startlist\" :key=\"index\">\r\n                                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: item.type == 'BatchCode' ? '10%' : '10%' }\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                                        <div class=\"dialogdetailsinglevalue \" :style=\"{ width: item.type == 'BatchCode' || item.type == 'checkBox' ? '80%' : '80%' }\">\r\n                                            <el-input style=\"width: 100%\" v-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                                            <!-- <div class=\"detailsnote2\" v-else-if=\"item.type == 'checkBox' && IsDifferent === true && IsPack === '0'\">\r\n                                                {{ $t('Overview.IsUpdateLtxt') }}\r\n                                            </div>\r\n                                            <div class=\"detailsnote3\" v-else-if=\"item.type == 'checkBox' && IsDifferent === false && IsPack === '0'\">\r\n                                                {{ $t('Overview.TextGood') }}\r\n                                            </div> -->\r\n                                            <!-- <div v-else-if=\"item.type == 'BatchCode'\" style=\"display: flex\">\r\n                                                <el-input v-model=\"item.value\"></el-input>\r\n                                                <el-input v-model=\"item.value2\" disabled></el-input>\r\n                                                <el-input v-model=\"item.value3\"></el-input>\r\n                                                <el-button\r\n                                                    class=\"tablebtn\"\r\n                                                    @click=\"getBatchCode()\"\r\n                                                    size=\"mini\"\r\n                                                    style=\"margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff\"\r\n                                                    icon=\"el-icon-refresh\"\r\n                                                ></el-button>\r\n                                            </div> -->\r\n                                            <el-select style=\"width: 100%\" clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                                                <el-option v-for=\"it in item.option\" :key=\"it.ID\" :label=\"it.Number\" :value=\"it.ID\"></el-option>\r\n                                            </el-select>\r\n                                            <el-date-picker\r\n                                                @change=\"GetDate(item.id)\"\r\n                                                v-else-if=\"item.type == 'date'\"\r\n                                                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                                                :disabled=\"item.disabled\"\r\n                                                v-model=\"item.value\"\r\n                                                type=\"datetime\"\r\n                                                style=\"width: 100%\"\r\n                                            ></el-date-picker>\r\n                                            <span v-else-if=\"item.id == 'TargetQuantity'\">{{ chooseItem.TargetQuantity }}{{ chooseItem.Unit1 }}</span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n<!--                                <div v-if=\"IsPack == '0'\" style=\"height: 100%; margin-top: 10px; padding: 0 10px; box-shadow: none; display: flex; flex-direction: column\">-->\r\n\r\n<!--                                    <span class=\"dialogsubtitlebox\" style=\"margin-bottom: 5px\">工单长文本：</span>-->\r\n<!--                                    <el-input type=\"textarea\" :autosize=\"{ minRows: 5 }\" style=\"width: 370px !important\" :disabled=\"isEdit == false\" v-model=\"Text1\"></el-input>-->\r\n<!--                                    <span class=\"dialogsubtitlebox\" style=\"margin: 5px 0\">配方长文本：</span>-->\r\n<!--                                    <el-input type=\"textarea\" :autosize=\"{ minRows: 5 }\" style=\"width: 370px !important\" :disabled=\"isEdit == false\" v-model=\"Text2\"></el-input>-->\r\n<!--                                </div>-->\r\n                            </div>\r\n                          <div style=\"padding: 18px 18px 0 18px\">\r\n                            <el-row>\r\n                              <el-col :span=\"12\">\r\n                                <span class=\"font-M09 bold\">工单长文本</span>\r\n                              </el-col>\r\n                              <el-col :span=\"12\">\r\n                                <span class=\"font-M09 bold\">配方长文本</span>\r\n                              </el-col>\r\n                            </el-row>\r\n                            <CodeDiff\r\n                                hideHeader\r\n                                :old-string=\"Text1\"\r\n                                :new-string=\"Text2\"\r\n                                output-format=\"side-by-side\"\r\n                            />\r\n                          </div>\r\n\r\n                        </div>\r\n                        <span slot=\"footer\" class=\"dialog-footer\">\r\n                            <el-button style=\"float: left\" v-if=\"chooseItem.isResume\">\r\n                                {{ $t('Overview.bottleneck') }}\r\n                            </el-button>\r\n                            <el-button class=\"tablebtn\" icon=\"el-icon-video-play\" @click=\"ProducedStart()\">\r\n                                {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.Start') }}\r\n                            </el-button>\r\n                            <el-button @click=\"StartModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n                        </span>\r\n                    </el-dialog>\r\n                </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane v-for=\"(item, index) in Equipmentlist\" :key=\"index\" :disabled=\"item.FunctionCodes != '' ? false : true\" :name=\"item.ID\">\r\n                <span slot=\"label\">\r\n                    <i :class=\"item.FunctionCodes != '' ? 'el-icon-s-tools' : 'el-icon-s-grid'\"></i>\r\n                    <span>{{ item.EquipmentName }}</span>\r\n                    <div v-if=\"item.FunctionCodes != ''\" class=\"tabiconbox\">\r\n                        <span v-if=\"item.FunctionCodes.indexOf('POManagement') != -1\">\r\n                            <i\r\n                                :style=\"{ color: item.ProductionOrderId == null ? 'red' : '#3DCD58' }\"\r\n                                :class=\"item.ProductionOrderId == null ? 'iconfont icon-pausecircle-fill' : 'iconfont icon-play-fill'\"\r\n                            ></i>\r\n                        </span>\r\n                        <!-- <i class=\"el-icon-s-marketing\"></i> -->\r\n                    </div>\r\n                </span>\r\n                <div class=\"subtabs\">\r\n                    <div class=\"activeTitle\" v-if=\"ActiveList.length == 0\">{{ $t('Overview.NoActiveProcessOrder') }}</div>\r\n                    <div class=\"activeTitle\" v-else>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ ActiveList[0].ProcessOrder }}({{ ActiveList[0].Number }})</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].Material }}</div>\r\n                        </div>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ ActiveList[0].TargetQuantity }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].Unit1 }}</div>\r\n                        </div>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ ActiveList[0].Speed }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].SpeedUom }}</div>\r\n                        </div>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ $t('Overview.BatchCode') }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].BatchCode }}</div>\r\n                        </div>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ $t('Overview.ExpirationDate') }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].ExpirationDate }}</div>\r\n                        </div>\r\n                               <div class=\"activeBox\" v-if=\"ActiveList[0].StorageTank != ''&&ActiveList[0].StorageTank != null\">\r\n                            <div class=\"activeLabel\">{{ $t('Overview.CGBM') }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].StorageTank }}</div>\r\n                        </div>\r\n                               <div class=\"activeBox\" v-if=\"ActiveList[0].StorageTankOrderGc != ''&&ActiveList[0].StorageTankOrderGc != null\">\r\n                            <div class=\"activeLabel\">{{ $t('Overview.GC') }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].StorageTankOrderGc }}</div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"subtabsbox\">\r\n                        <el-tabs v-model=\"activeName2\" type=\"border-card\" @tab-click=\"handleClick2(item, index)\">\r\n                            <!-- v-if=\"item.functionlist.includes('PO Management') != -1\" -->\r\n                            <!-- <el-tab-pane :label=\"$t('Overview.POManagement')\" name=\"1\">\r\n                                <POManagement ref=\"POManagement\" @getNum=\"getNum\" :EquipmentId=\"EquipmentId\"></POManagement>\r\n                            </el-tab-pane> -->\r\n                            <el-tab-pane :label=\"$t(`Overview.${it.trim()}`)\" v-for=\"(it, ind) in item.functionlist\" :key=\"ind\" :name=\"it.trim()\">\r\n                                <div>\r\n                                    <component\r\n                                        @loadProgress=\"loadProgress\"\r\n                                        :is=\"it.trim() == 'Tipping' ? (isTippingscan ? 'Tippingscan' : 'Tipping') : it.trim()\"\r\n                                        :ref=\"index + it.trim()\"\r\n                                        :BatchId=\"BatchId\"\r\n                                        :EquipmentName=\"item.EquipmentName\"\r\n                                        :ExecutionId=\"ExecutionId\"\r\n                                        :EquipmentId=\"EquipmentId\"\r\n                                        :RunEquipmentId=\"RunEquipmentId\"\r\n                                    ></component>\r\n                                </div>\r\n                            </el-tab-pane>\r\n                            <!-- <el-tab-pane label=\"SampleWeighing\" name=\"SampleWeighing\">\r\n                                <SampleWeighing :ref=\"index + 'SampleWeighing'\" :EquipmentId=\"EquipmentId\"></SampleWeighing>\r\n                            </el-tab-pane> -->\r\n                            <!-- <el-tab-pane label=\"ParameterDownload\" name=\"ParameterDownload\">\r\n                                <ParameterDownload :ref=\"index + 'ParameterDownload'\" :EquipmentId=\"EquipmentId\"></ParameterDownload>\r\n                            </el-tab-pane> -->\r\n                            <!--\r\n                            <el-tab-pane label=\"Performance\" name=\"Performance\">\r\n                                <Performance :ref=\"index + 'Performance'\" :EquipmentId=\"EquipmentId\"></Performance>\r\n                            </el-tab-pane> -->\r\n                            <!-- <el-tab-pane label=\"Tipping\" name=\"Tipping\">\r\n                                <Tipping :ref=\"index + 'Tipping'\"></Tipping>\r\n                            </el-tab-pane> -->\r\n                            <!-- <el-tab-pane label=\"Logsheets\" name=\"Logsheets\">\r\n                                <Logsheets :ref=\"index + 'Logsheets'\"></Logsheets>\r\n                            </el-tab-pane> -->\r\n                        </el-tabs>\r\n                    </div>\r\n                </div>\r\n            </el-tab-pane>\r\n        </el-tabs>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { POManagemenOverview } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetProcessOrderViewSegments,\r\n    GetProcessOrderView,\r\n    GetEquipmentFunctionView,\r\n    GetEquipmentProcessOrderView,\r\n    getCheckTippingType,\r\n    GetCookieOrderLtexts,\r\n    GetBBatchListView,\r\n    GetBatchCode,\r\n    PoProducedStart,\r\n    PoProducedResume,\r\n    GetRunOrder,\r\n    GetCookOrderLtexts,\r\n    GetProcessOrderViewSegmentUnits,\r\n    GetPoList\r\n} from '@/api/Inventory/Overview.js';\r\nimport { POManagemenPoList } from '@/columns/factoryPlant/tableHeaders';\r\nimport moment from 'moment';\r\n\r\nimport { Message } from 'element-ui';\r\nimport { CodeDiff } from 'v-code-diff'\r\nexport default {\r\n    components: {\r\n        ParameterDownload: () => import('./components/ParameterDownload'),\r\n        POManagement: () => import('./components/POManagement'),\r\n        Consume: () => import('./components/Consume'),\r\n        Produce: () => import('./components/Produce'),\r\n        Tipping: () => import('./components/Tipping'),\r\n        Storage: () => import('./components/Storage'),\r\n                Performance: () => import('./components/Performance'),\r\n        MaterialPrep: () => import('./components/MaterialPrep'),\r\n        PerformanceEvents: () => import('./components/Performance'),\r\n        Logsheets: () => import('./components/Logsheets'),\r\n        Tippingscan: () => import('./components/Tippingscan'),\r\n        SampleWeighing: () => import('./components/SampleWeighing'),\r\n        ProcessText: () => import('./components/Processlongtext'),\r\n      CodeDiff\r\n    },\r\n    data() {\r\n        return {\r\n            viewtitle: '',\r\n            timepicker: [],\r\n            AvailablePOManagemenList: [],\r\n            Availableheader: POManagemenPoList,\r\n            StartModel: false,\r\n            MyEquipment: '',\r\n            ShowPOList: false,\r\n            MyEquipmentList: [],\r\n            Startlist: [\r\n                {\r\n                    label: this.$t('Overview.StartTime'),\r\n                    id: 'StartTime',\r\n                    value: '',\r\n                    disabled: true,\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.Batch'),\r\n                    id: 'BatchId',\r\n                    value: '',\r\n                    require: true,\r\n                    type: 'select',\r\n                    option: []\r\n                },\r\n                {\r\n                    label: this.$t('Overview.BatchCode'),\r\n                    id: 'LotCode',\r\n                    value: '',\r\n                    value2: '',\r\n                    value3: '',\r\n                    require: true,\r\n                    type: 'BatchCode'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ExpirationDate'),\r\n                    id: 'ExpirationDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date',\r\n                    disabled: true\r\n                },\r\n                {\r\n                    label: this.$t('Overview.TargetQuantity'),\r\n                    id: 'TargetQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Overview.CrewSize'),\r\n                    id: 'CrewSize',\r\n                    value: '',\r\n                    type: 'input'\r\n                },\r\n                {\r\n                    label: '',\r\n                    id: 'IsUpdateLtxt',\r\n                    value: false,\r\n                    type: 'checkBox'\r\n                }\r\n            ],\r\n            searchlist: [\r\n                {\r\n                    type: 'input',\r\n                    name: this.$t('Overview.QuickSearch'),\r\n                    id: 'QuickSearch',\r\n                    value: ''\r\n                },\r\n                {\r\n                    type: 'select',\r\n                    option: [],\r\n                    width: '20vh',\r\n                    name: this.$t('DFM_JYXM.Segment'),\r\n                    id: 'Segment',\r\n                    value: ''\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'FillLineCode',\r\n                    name: this.$t('$vuetify.dataTable.PRO_POLIST.FillLineCode')\r\n                }\r\n            ],\r\n            chooseItem: {\r\n                ProcessOrder: '',\r\n                isResume: false,\r\n                TargetQuantity: '',\r\n                Unit1: '',\r\n                MaterialCode: '',\r\n                MaterialName: ''\r\n            },\r\n            tableList: [],\r\n            Equipmentlist: [],\r\n            AvailabletableId: 'PRO_POManagement',\r\n            tableId: 'PRO_Overview',\r\n            header: POManagemenOverview,\r\n            pageOptions: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            pageOptions2: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            activeName: '1',\r\n            activeName2: '',\r\n            ActiveList: [],\r\n            EquipmentGroupRowId: '',\r\n            EquipmentId: '',\r\n            RunEquipmentId: '',\r\n            BatchId: '',\r\n            ExecutionId: '',\r\n            runningCode: '',\r\n            isTippingscan: false,\r\n            productionId: '',\r\n            isEdit: false,\r\n            IsDifferent: false,\r\n            EquipmentCode: '',\r\n            IsPack: '1',\r\n            Text1: '',\r\n            Text2: ''\r\n        };\r\n    },\r\n    mounted() {\r\n        this.viewtitle = JSON.parse(this.$route.query.query).Description;\r\n        console.log(this.viewtitle);\r\n        this.EquipmentGroupRowId = JSON.parse(this.$route.query.query).ID;\r\n        // this.FiltersTableData();\r\n        this.GetProcessOrderViewList();\r\n        //this.GetProcessOrderView2();\r\n        this.GetSegment();\r\n        this.GetEquipment();\r\n        this.changePagination();\r\n    },\r\n    methods: {\r\n        async ProducedStart() {\r\n            let flag = this.Startlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (this.MyEquipment == '') {\r\n                flag = true;\r\n            }\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                SegmentId: this.chooseItem.SegmentId,\r\n                ProductionOrderId: this.chooseItem.ProductionOrderId,\r\n                PoSegmentRequirementId: this.chooseItem.ID,\r\n                BatchId: '',\r\n                LotCode: '',\r\n                EquipmentId: this.MyEquipment,\r\n                StartTime: '',\r\n                ProductionDate: '',\r\n                ExpirationDate: ''\r\n            };\r\n            if (this.chooseItem.isResume == true) {\r\n                params.ExecutionId = this.chooseItem.ExecutionId;\r\n            } else {\r\n                params.ExecutionId = '';\r\n            }\r\n            this.Startlist.forEach(item => {\r\n                if (item.id == 'LotCode') {\r\n                    params[item.id] = item.value + item.value2 + item.value3;\r\n                } else {\r\n                    params[item.id] = item.value;\r\n                }\r\n            });\r\n            params.ExpirationDate = moment(params.ExpirationDate).format('YYYY-MM-DD HH:mm:ss');\r\n            if (params.LotCode.length > 10) {\r\n                Message({\r\n                    message: `${this.$t('Overview.BatchCodeLong')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let res;\r\n            if (this.chooseItem.isResume == true) {\r\n                res = await PoProducedResume(params);\r\n            } else {\r\n                res = await PoProducedStart(params);\r\n            }\r\n            //this.GetProcessOrderView2();\r\n            this.loadProgress();\r\n            this.StartModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        GetDate(id) {\r\n            if (id == 'ProductionDate') {\r\n                if (this.chooseItem.NeedQARelease == '1') {\r\n                    this.Startlist[4].value = this.addDays(this.Startlist[3].value, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);\r\n                } else {\r\n                    this.Startlist[4].value = this.addDays(this.chooseItem.PlanStartTime, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);\r\n                }\r\n            }\r\n        },\r\n        startOrder(item) {\r\n            // this.MyEquipment = '';\r\n            // this.EquipmentCode = '';\r\n            this.SegmentUnits(item);\r\n            console.log(item.row);\r\n            if (item.row) {\r\n                this.IsPack = item.row.NeedQARelease;\r\n                this.getLtext(item.row.ProductionOrderId);\r\n                this.chooseItem = item.row;\r\n                this.chooseItem.isResume = false;\r\n            } else {\r\n                this.chooseItem = item;\r\n                this.chooseItem.isResume = true;\r\n            }\r\n            this.Startlist.forEach((item, index) => {\r\n                if (index == 0) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 3) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 2) {\r\n                    item.value = '';\r\n                    item.value2 = '';\r\n                    item.value3 = '';\r\n                } else if (item.id == 'IsUpdateLtxt') {\r\n                    item.value = false;\r\n                } else {\r\n                    item.value = '';\r\n                }\r\n            });\r\n            this.GetDate('ProductionDate');\r\n            this.getBatchList();\r\n        },\r\n        async SegmentUnits(item) {\r\n            let res = await GetProcessOrderViewSegmentUnits('', item.row.Segment);\r\n            this.MyEquipmentList = res.response;\r\n        },\r\n        async getBatchCode() {\r\n            let date = moment(this.Startlist[3].value).format('YYYY-MM-DD HH:mm:ss');\r\n            let p = {\r\n                LineCode: this.Startlist[2].value,\r\n                equipmentCode: this.EquipmentCode,\r\n                productionDate: date,\r\n                productionId: this.chooseItem.ProductionOrderId\r\n            };\r\n            let res = await GetBatchCode(p);\r\n            if (res.response == null) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'warning'\r\n                });\r\n            } else {\r\n                this.Startlist[2].value = res.response.substring(0, 2);\r\n                this.Startlist[2].value2 = res.response.substring(2, 5);\r\n            }\r\n        },\r\n        async getBatchList() {\r\n            let params = {\r\n                PoSegmentRequirementId: this.chooseItem.ID\r\n            };\r\n            let res = await GetBBatchListView(params);\r\n            this.Startlist[1].option = res.response;\r\n            this.Startlist[1].value = this.Startlist[1].option[0].ID;\r\n            this.StartModel = true;\r\n        },\r\n        addDays(date, number, interval) {\r\n            const newDate1 = new Date(date);\r\n            const newDate = new Date(newDate1.getFullYear(), newDate1.getMonth(), newDate1.getDate());\r\n            this.DateAdd(interval, number, newDate);\r\n            newDate.setDate(newDate.getDate() + 1); // 增加一天\r\n            newDate.setSeconds(newDate.getSeconds() - 1); // 减去1秒\r\n            return newDate;\r\n        },\r\n        DateAdd(interval, number, date) {\r\n            switch (interval) {\r\n                case 'Y': {\r\n                    date.setFullYear(date.getFullYear() + number);\r\n                    return date;\r\n                }\r\n                case 'Q': {\r\n                    date.setMonth(date.getMonth() + number * 3);\r\n                    return date;\r\n                }\r\n                case 'M': {\r\n                    date.setMonth(date.getMonth() + number);\r\n                    return date;\r\n                }\r\n                case 'W': {\r\n                    date.setDate(date.getDate() + number * 7);\r\n                    return date;\r\n                }\r\n                case 'D': {\r\n                    date.setDate(date.getDate() + number);\r\n                    return date;\r\n                }\r\n                case 'h': {\r\n                    date.setHours(date.getHours() + number);\r\n                    return date;\r\n                }\r\n                case 'm': {\r\n                    date.setMinutes(date.getMinutes() + number);\r\n                    return date;\r\n                }\r\n                case 's': {\r\n                    date.setSeconds(date.getSeconds() + number);\r\n                    return date;\r\n                }\r\n                default: {\r\n                    date.setDate(date.getDate() + number);\r\n                    return date;\r\n                }\r\n            }\r\n        },\r\n        async getMyEquipment() {\r\n            this.MyGetRunOrder();\r\n            this.MyEquipmentList.forEach(item => {\r\n                if (item.ID == this.MyEquipment) {\r\n                    this.EquipmentCode = item.EquipmentCode;\r\n                }\r\n            });\r\n            this.getBatchCode();\r\n        },\r\n        async MyGetRunOrder() {\r\n            let res = await GetRunOrder('', this.MyEquipment);\r\n            this.runningCode = res.response;\r\n            if (this.runningCode == null) {\r\n                this.runningCode = '';\r\n            }\r\n        },\r\n        async getLtext(id) {\r\n            if (this.IsPack === '0') {\r\n                this.IsDifferent = false;\r\n                let params = {\r\n                    id: id\r\n                };\r\n                let r = await GetCookOrderLtexts(params);\r\n                if (r.response.length == 2) {\r\n                    if (r.msg === '长文本不一致！') {\r\n                        this.IsDifferent = true;\r\n                    }\r\n                    this.Text1 = r.response[0].ProcessData;\r\n                    this.Text2 = r.response[1].ProcessData;\r\n                }\r\n            }\r\n        },\r\n        async GetSegment() {\r\n            let params = {\r\n                LineCode: this.viewtitle\r\n            };\r\n            let res = await GetProcessOrderViewSegments(params);\r\n            // console.log(res,1432345345)\r\n            let data = res.response;\r\n            if (data.length != 0) {\r\n                data.forEach((item, index) => {\r\n                    if (index == 0) {\r\n                        this.searchlist[1].value = item.key;\r\n                    }\r\n                });\r\n                this.searchlist[1].option = data;\r\n                this.ShowPOList = true;\r\n                //this.GetProcessOrderView2();\r\n            } else {\r\n                this.ShowPOList = false;\r\n            }\r\n        },\r\n        getsearch() {\r\n            this.pageOptions2.page = 1;\r\n            this.pageOptions2.pageSize = 20;\r\n            this.GetProcessOrderView2();\r\n        },\r\n        getempty() {\r\n            this.QuickSearch = '';\r\n            this.timepicker = [];\r\n            this.pageOptions2.page = 1;\r\n            this.pageOptions2.pageSize = 20;\r\n            this.searchlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            this.GetProcessOrderView2();\r\n        },\r\n        loadProgress(id) {\r\n            this.GetEquipment();\r\n            this.handleClick();\r\n        },\r\n        back() {\r\n            this.$router.go(-1);\r\n        },\r\n        async GetProcessOrderView2() {\r\n            // console.log('999999');\r\n            if (this.timepicker == null) {\r\n                this.timepicker = [];\r\n            }\r\n            let params = {\r\n                Search: this.searchlist[0].value,\r\n                LineCode: this.viewtitle,\r\n                Segment: this.searchlist[1].value,\r\n                FillLineCode: this.searchlist[2].value,\r\n                pageIndex: this.pageOptions2.page,\r\n                pageSize: this.pageOptions2.pageSize,\r\n                StartTime: this.timepicker[0],\r\n                EndTime: this.timepicker[1]\r\n            };\r\n            let res = await GetPoList(params);\r\n            this.AvailablePOManagemenList = res.response.data;\r\n            this.pageOptions2.total = res.response.dataCount;\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions2.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            // this.getNumTofather();\r\n        },\r\n        async GetProcessOrderViewList() {\r\n            console.log(12);\r\n            let params = {\r\n                EquipmentGroupRowId: this.EquipmentGroupRowId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res = await GetEquipmentProcessOrderView(params);\r\n            this.tableList = res.response.data;\r\n            this.pageOptions.total = res.response.dataCount;\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n        },\r\n        changePagination() {\r\n            let el2 = document.getElementsByClassName(`el-select-dropdown__item`);\r\n            for (let i = 0; i < el2.length; i++) {\r\n                el2[i].innerHTML = el2[i].innerHTML.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            }\r\n        },\r\n        async GetEquipment() {\r\n            let params = {\r\n                EquipmentGroupRowId: this.EquipmentGroupRowId,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n\r\n            let res = await GetEquipmentFunctionView(params);\r\n            if (res.response.length != 0) {\r\n                res.response.forEach(item => {\r\n                    item.functionlist = item.FunctionCodes.split(',');\r\n                });\r\n            }\r\n            this.Equipmentlist = res.response;\r\n        },\r\n        async handleClick2(item, index) {\r\n            if (this.activeName2 == 'Logsheets') {\r\n                this.$refs[index + this.activeName2][0].activeName = '0';\r\n            }\r\n            console.log(this.ActiveList, 1);\r\n            console.log(this.$refs[index + this.activeName2][0]);\r\n            // this.GetEquipment();\r\n            let params = {\r\n                EquipmentGroupRowId: this.EquipmentGroupRowId,\r\n                RunEquipmentId: this.EquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res = await GetEquipmentProcessOrderView(params);\r\n            console.log(res, 222444);\r\n            if (res.response.data.length != 0) {\r\n                let list = res.response.data;\r\n                list.forEach(item => {\r\n                    item.Material = item.MaterialName + '-' + item.MaterialCode;\r\n                });\r\n                this.ActiveList = list;\r\n                this.BatchId = list[0].BatchId;\r\n                this.RunEquipmentId = list[0].RunEquipmentId;\r\n                this.ExecutionId = list[0].ExecutionId;\r\n            } else {\r\n                this.ActiveList = [];\r\n                this.BatchId = '';\r\n                this.RunEquipmentId = '';\r\n                this.ExecutionId = '';\r\n            }\r\n\r\n            if (this.$refs[index + this.activeName2][0].getEquipmentModal) {\r\n                this.$refs[index + this.activeName2][0].getEquipmentModal(item, this.ActiveList[0]);\r\n            }\r\n            if (this.$refs[index + this.activeName2][0].tabBeClick) {\r\n                this.$refs[index + this.activeName2][0].tabBeClick(item);\r\n            }\r\n            console.log(this.ActiveList);\r\n        },\r\n        async handleClick(key) {\r\n            if (key && Number(key.index) == 1) {\r\n                this.GetProcessOrderView2();\r\n            }\r\n            console.log(this.Equipmentlist);\r\n            // this.GetEquipment();\r\n            this.EquipmentId = this.activeName;\r\n            let params = {\r\n                EquipmentGroupRowId: this.EquipmentGroupRowId,\r\n                RunEquipmentId: this.EquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res2 = await getCheckTippingType(JSON.stringify(this.EquipmentId));\r\n            if (res2.response == 'true') {\r\n                this.isTippingscan = true;\r\n            } else {\r\n                this.isTippingscan = false;\r\n            }\r\n            console.log('handleClick');\r\n            let res = await GetEquipmentProcessOrderView(params);\r\n            if (res.response.data.length != 0) {\r\n                let list = res.response.data;\r\n                list.forEach(item => {\r\n                    item.Material = item.MaterialName + '-' + item.MaterialCode;\r\n                });\r\n                this.ActiveList = list;\r\n                this.BatchId = list[0].BatchId;\r\n                this.RunEquipmentId = list[0].RunEquipmentId;\r\n                this.ExecutionId = list[0].ExecutionId;\r\n            } else {\r\n                this.ActiveList = [];\r\n                this.BatchId = '';\r\n                this.RunEquipmentId = '';\r\n                this.ExecutionId = '';\r\n            }\r\n            if (key) {\r\n                console.log(Number(key.index));\r\n                let num = null;\r\n                if (this.ShowPOList) {\r\n                    num = Number(key.index) - 2;\r\n                } else {\r\n                    num = Number(key.index) - 1;\r\n                }\r\n                if (num >= 0) {\r\n                    let functionlist = this.Equipmentlist[num].functionlist;\r\n                    //console.log(this.Equipmentlist[num]);\r\n                    console.log(functionlist);\r\n                    this.activeName2 = functionlist[0];\r\n                    functionlist.forEach(item => {\r\n                        if (item == 'POManagement') {\r\n                            this.activeName2 = 'POManagement';\r\n                        }\r\n                    });\r\n                    if (this.$refs[num + this.activeName2][0].getEquipmentModal) {\r\n                        this.$refs[num + this.activeName2][0].getEquipmentModal(this.Equipmentlist[num], this.ActiveList[0]);\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        handleSizeChange(val) {\r\n            this.pageOptions.pageSize = val;\r\n            this.GetProcessOrderViewList();\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.pageOptions.page = val;\r\n            this.GetProcessOrderViewList();\r\n        },\r\n        handleSizeChange2(val) {\r\n            this.pageOptions2.pageSize = val;\r\n            this.GetProcessOrderView2();\r\n        },\r\n        handleCurrentChange2(val) {\r\n            this.pageOptions2.page = val;\r\n            this.GetProcessOrderView2();\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.overview {\r\n    .searchboxtitle {\r\n        font-size: 1.7vh;\r\n        color: #767777;\r\n        padding-bottom: 5px;\r\n        margin-left: 10px;\r\n    }\r\n\r\n    .el-tabs {\r\n        height: 94%;\r\n    }\r\n    .subtabsbox {\r\n        .el-tabs--border-card {\r\n            border: 0 !important;\r\n            box-shadow: none !important;\r\n        }\r\n    }\r\n    .paginationbox {\r\n        height: 65px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin-top: 10px;\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 47%;\r\n            text-align: right;\r\n        }\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n    .splitdetailbox {\r\n        padding-bottom: 10px;\r\n        border: 1px solid #e8e8e8;\r\n        margin-bottom: 5px;\r\n        .splitdetailboxtitle {\r\n            background: #f5f5f5;\r\n            height: 3.5vh;\r\n            display: flex;\r\n            align-items: center;\r\n            padding-left: 5px;\r\n            font-size: 1.1rem;\r\n            color: #303133;\r\n        }\r\n        .detailsnote {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 0.9rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .detailsnote2 {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 1.2rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .detailsnote3 {\r\n            background-color: #f5fdec;\r\n            border-color: #faecd8;\r\n            color: hsl(135, 55%, 44%);\r\n            padding: 8px;\r\n            font-size: 1.2rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .splitdetailboxtitleTag {\r\n            margin-left: 5px;\r\n            background: #5cb85c;\r\n            color: #fff;\r\n            border-color: #5cb85c;\r\n        }\r\n    }\r\n}\r\n//.el-dialog__body {\r\n//    .el-input {\r\n//        width: 250px !important;\r\n//    }\r\n//    .longwidthinput {\r\n//        .el-input {\r\n//            width: 400px !important;\r\n//        }\r\n//        .el-select {\r\n//            width: 400px !important;\r\n//        }\r\n//    }\r\n//    .el-select {\r\n//        width: 250px !important;\r\n//    }\r\n//}\r\n.code-diff-view{\r\n  margin-top: 5px;\r\n}\r\n</style>\r\n"]}]}