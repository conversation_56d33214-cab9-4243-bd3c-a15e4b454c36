{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDoc\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDoc\\index.vue", "mtime": 1750295689849}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAqKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopDoc", "sourcesContent": ["<template>\n  <div class=\"root usemystyle\">\n    <div class=\"root-layout\" v-loading=\"initLoading\">\n      <split-pane \n        :min-percent=\"15\" \n        :max-percent=\"40\" \n        :default-percent=\"20\" \n        split=\"vertical\">\n        <template slot=\"pane1\">\n          <div class=\"root-left\">\n            <div class=\"tree-toolbar\">\n              <el-button-group>\n                <el-button \n                  size=\"small\" \n                  type=\"primary\"\n                  icon=\"el-icon-plus\"\n                  @click=\"addSopDirChild({})\">新建</el-button>\n                <el-button \n                  size=\"small\"\n                  icon=\"el-icon-refresh\"\n                  @click=\"getDirTree\">刷新</el-button>\n                <el-button \n                  size=\"small\"\n                  @click=\"expandAll\">展开</el-button>\n                <el-button \n                  size=\"small\"\n                  @click=\"collapseAll\">收起</el-button>\n              </el-button-group>\n            </div>\n            <el-tree\n              ref=\"tree\"\n              :data=\"treeData\"\n              :props=\"defaultProps\"\n              highlight-current\n              @node-click=\"handleNodeClick\"\n              v-loading=\"treeLoading\">\n              <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\n                <div style=\"line-height: 22px;\">\n                  <div class=\"tree-title\">{{ node.data.name }}</div>\n                </div>\n                <span class=\"tree-node-actions\">\n                  <el-button type=\"text\" size=\"mini\" @click.stop=\"() => showSopDirDialog(data)\">\n                    <i class=\"el-icon-edit\"></i>\n                  </el-button>\n                  <el-button type=\"text\" size=\"mini\" @click.stop=\"() => addSopDirChild(data)\">\n                    <i class=\"el-icon-plus\"></i>\n                  </el-button>\n                  <el-button type=\"text\" size=\"mini\" @click.stop=\"() => deleteSopDir(data)\">\n                    <i class=\"el-icon-delete\"></i>\n                  </el-button>\n                </span>\n              </span>\n            </el-tree>\n          </div>\n        </template>\n        <template slot=\"pane2\">\n          <div class=\"root-right\">\n            <div class=\"InventorySearchBox\">\n              <div class=\"search-form\">\n                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                  <div class=\"form-content\">\n                    <div class=\"search-area\">\n                      <div class=\"search-row\">\n                            <el-form-item label=\"名称\" prop=\"docName\" label-width=\"40px\" class=\"search-form-item\">\n                              <el-input v-model=\"searchForm.docName\" placeholder=\"输入名称\" clearable size=\"small\">                            \n                              </el-input>\n                            </el-form-item>\n                            <el-form-item label=\"编码\" prop=\"docCode\" label-width=\"40px\" class=\"search-form-item\">\n                              <el-input v-model=\"searchForm.docCode\" placeholder=\"输入编码\" clearable size=\"small\"></el-input>\n                            </el-form-item>\n                            <el-form-item label=\"版本\" prop=\"docVersion\" label-width=\"40px\" class=\"search-form-item\">\n                              <el-input v-model=\"searchForm.docVersion\" placeholder=\"输入版本\" clearable size=\"small\"></el-input>\n                            </el-form-item>\n                            <el-form-item label=\"状态\" prop=\"docStatus\" label-width=\"40px\" class=\"search-form-item\">\n                              <el-select v-model=\"searchForm.docStatus\" placeholder=\"选择\" clearable size=\"small\">\n                                <el-option label=\"有效\" :value=\"1\"></el-option>\n                                <el-option label=\"无效\" :value=\"0\"></el-option>\n                              </el-select>\n                            </el-form-item>\n                        <div class=\"action-buttons\">\n                          <el-button type=\"success\" size=\"small\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">新增</el-button>\n                          <el-button slot=\"append\" type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn()\" size=\"small\">查询</el-button>\n                          <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"resetForm\">重置</el-button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </el-form>\n              </div>\n            </div>\n            <div class=\"root-main\">\n              <el-table class=\"mt-3\"\n                        :height=\"700\"\n                        border\n                        v-loading=\"tableLoading\"\n                        :data=\"tableData\"\n                        style=\"width: 100%; border-radius: 4px;\"\n                        :empty-text=\"'暂无数据'\">\n                <el-table-column\n                  type=\"index\"\n                  label=\"序号\"\n                  width=\"50\"\n                  align=\"center\">\n                </el-table-column>\n                <el-table-column v-for=\"(item) in tableName\"\n                                 :default-sort=\"{prop: item.value, order: 'descending'}\"\n                                 :key=\"item.value\"\n                                 :prop=\"item.value\"\n                                 :label=\"typeof item.text === 'function' ? item.text() : item.text\"\n                                 :width=\"item.width\"\n                                 :align=\"item.alignType || 'center'\"\n                                 sortable\n                                 show-overflow-tooltip>\n                  <template slot-scope=\"scope\">\n                    <template v-if=\"item.value === 'FileSize'\">\n                      {{ formatFileSize(scope.row[item.value]) }}\n                    </template>\n                    <template v-else-if=\"item.value === 'DocStatus'\">\n                      <el-tag :type=\"getStatusType(scope.row[item.value])\" size=\"small\">\n                        {{ formatStatus(scope.row[item.value]) }}\n                      </el-tag>\n                    </template>\n                    <template v-else>\n                      {{ scope.row[item.value] }}\n                    </template>\n                  </template>\n                </el-table-column>\n                <el-table-column prop=\"operation\" :min-width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n                  <template slot-scope=\"scope\">\n                    <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._CK') }}</el-button>\n                    <el-button size=\"mini\" type=\"text\" @click=\"handleDownload(scope.row)\">{{ $t('GLOBAL.Download') }}</el-button>\n                    <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>\n                    <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._shenqing') }}</el-button>               \n                  </template>\n                </el-table-column>\n              </el-table>\n            </div>\n          </div>\n        </template>\n      </split-pane>\n      <div class=\"root-footer\">\n        <el-pagination\n            class=\"mt-3\"\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n            :current-page=\"searchForm.pageIndex\"\n            :page-sizes=\"[10,20, 50, 100,500]\"\n            :page-size=\"searchForm.pageSize\"\n            layout=\"->,total, sizes, prev, pager, next, jumper\"\n            :total=\"total\"\n            background\n        ></el-pagination>\n      </div>\n      <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\" :treeData=\"treeData\"></form-dialog>\n      <sop-dir-form-dialog \n        :visible.sync=\"sopDirDialogVisible\" \n        :form-data=\"sopDirDialogForm\" \n        @saveForm=\"getDirTree\" \n        ref=\"sopDirFormDialog\">\n      </sop-dir-form-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss'\nimport FormDialog from './form-dialog'\nimport TreeSelect from '../components/tree-select'\nimport SplitPane from '../components/split-pane'\nimport {\n    delSopDoc, getSopDocList, downloadSopDoc\n} from \"@/api/SOP/sopDoc\";\nimport { getSopDirTree, saveSopDirForm, delSopDir } from \"@/api/SOP/sopDir\";\nimport SopDirFormDialog from \"@/views/SOP/sopDir/form-dialog\";\nimport { sopDocColumns } from '@/columns/SOP/sopDoc.js';\n\nexport default {\n  name: 'index.vue',\n  components: {\n    FormDialog,\n    SopDirFormDialog,\n    SplitPane\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n        dirId: '',\n        docName: '',\n        docCode: '',\n        docStatus: '',\n        deleted: ''\n      },\n      total: 0,\n      tableData: [],\n      hansObj: this.$t('SOP文档主表.table'),\n      tableName: [],\n      tableLoading: false,\n      treeLoading: false,\n      initLoading: false,\n      tableOption: [],\n      mainH: 0,\n      buttonOption: {\n        name:'SOP文档主表',\n        serveIp:'baseURL_DFM',\n        uploadUrl:'/api/SopDoc/ImportData',\n        exportUrl:'/api/SopDoc/ExportData',\n        DownLoadUrl:'/api/SopDoc/DownLoadTemplate',\n      },\n      treeData: [],\n      defaultProps: {\n        children: 'children',\n        label:  function (node){\n          return node.data.DirName\n        } \n      },\n      docStatusOptions: [],\n      deletedOptions: [],\n      sopDirDialogVisible: false,\n      sopDirDialogForm: {},\n      sopDirFormLoading: false\n    }\n  },\n  async mounted() {\n    try {\n      this.initLoading = true\n      this.getZHHans()\n      await Promise.all([\n        this.getDictData('docStatus', 'docStatusOptions'),\n        this.getDictData('deletedStatus', 'deletedOptions')\n      ])\n      await this.getDirTree()\n      await this.getTableData()\n      this.$nextTick(() => {\n        this.mainH = this.$webHeight(\n          document.getElementsByClassName('root')[0].clientHeight,\n          document.getElementsByClassName('root')[0].clientHeight\n        )\n      })\n    } catch (err) {\n      console.error('页面初始化失败:', err)\n      this.$message.error('页面初始化失败，请刷新重试')\n    } finally {\n      this.initLoading = false\n    }\n  },\n  beforeDestroy() {\n    window.onresize = null\n  },\n  methods: {\n    // 格式化文件大小\n    formatFileSize(size) {\n      if (!size) return '0 B'\n      const units = ['B', 'KB', 'MB', 'GB', 'TB']\n      let index = 0\n      let fileSize = parseFloat(size)\n      while (fileSize >= 1024 && index < units.length - 1) {\n        fileSize /= 1024\n        index++\n      }\n      return `${fileSize.toFixed(2)} ${units[index]}`\n    },\n\n    // 获取状态对应的类型\n    getStatusType(status) {\n      switch (status) {\n        case 1: return 'success'\n        case 2: return 'warning'\n        case 0: return 'info'\n        default: return ''\n      }\n    },\n\n    // 格式化状态\n    formatStatus(status) {\n      switch (status) {\n        case 1: return this.$t('SOP.StatusValid')\n        case 2: return this.$t('SOP.AuditPending')\n        case 0: return this.$t('SOP.StatusInvalid')\n        default: return this.$t('GLOBAL._WZ')\n      }\n    },\n\n    async getDictData(dictType, targetKey) {\n      try {\n        this[targetKey] = await this.$getNewDataDictionary(dictType)\n      } catch (err) {\n        console.error(`获取${dictType}字典数据失败:`, err)\n        this.$message.error('获取字典数据失败')\n        throw err\n      }\n    },\n\n    getZHHans() {\n      this.tableName = sopDocColumns\n    },\n\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    resetForm() {\n      this.searchForm = {\n        pageIndex: 1,\n        pageSize: 20,\n        dirId: '',\n        docName: '',\n        docCode: '',\n        docStatus: '',\n        deleted: ''\n      }\n      this.getTableData()\n    },\n\n    async delRow(row) {\n      try {\n        await this.$confirms({\n          title: this.$t('GLOBAL._TS'),\n          message: this.$t('GLOBAL._COMFIRM'),\n          confirmText: this.$t('GLOBAL._QD'),\n          cancelText: this.$t('GLOBAL._QX')\n        })\n        const res = await delSopDoc([row.ID])\n        if (res.success) {\n          this.$message.success(res.msg || '删除成功')\n          this.getTableData()\n        } else {\n          this.$message.error(res.msg || '删除失败')\n        }\n      } catch (err) {\n        if (err) {\n          console.error('删除数据失败:', err)\n          this.$message.error('删除失败')\n        }\n      }\n    },\n\n    async getTableData(data) {\n      this.tableLoading = true\n      try {\n        const res = await getSopDocList(this.searchForm)\n        if (res.success) {\n          this.tableData = res.response.data || []\n          this.total = res.response.dataCount || 0\n        } else {\n          this.$message.error(res.msg || '获取数据失败')\n        }\n      } catch (err) {\n        console.error('获取表格数据失败:', err)\n        this.$message.error('获取数据失败')\n        throw err\n      } finally {\n        this.tableLoading = false\n      }\n    },\n    \n    async getDirTree() {\n      this.treeLoading = true\n      try {\n        const res = await getSopDirTree()\n        if (res.success) {\n          this.treeData = res.response || []\n        } else {\n          this.$message.error(res.msg || '获取目录树失败')\n        }\n      } catch (err) {\n        console.error('获取目录树失败:', err)\n        this.$message.error('获取目录树失败')\n        throw err\n      } finally {\n        this.treeLoading = false\n      }\n    },\n    \n    handleNodeClick(data) {\n      // 保留分页配置,重置其他搜索条件\n      const { pageIndex, pageSize } = this.searchForm\n      this.searchForm = {\n        pageIndex,\n        pageSize,\n        dirId: data.id,\n        docName: '',\n        docCode: '',\n        docStatus: '',\n        deleted: ''\n      }\n      this.getTableData()\n    },\n    \n    showSopDirDialog(data) {\n      this.sopDirDialogForm = { ...data }\n      this.sopDirDialogVisible = true\n      this.$nextTick(() => {\n        this.$refs.sopDirFormDialog.show(data, 'show')\n      })\n    },\n    \n    addSopDirChild(data) {\n      this.sopDirDialogForm = { parentId: data.id }\n      this.sopDirDialogVisible = true\n      this.$nextTick(() => {\n        this.$refs.sopDirFormDialog.show(data, 'add')\n      })\n    },\n    \n    async deleteSopDir(data) {\n      try {\n        await this.$confirms({\n          title: this.$t('GLOBAL._TS'),\n          message: '确定删除该目录吗？',\n          confirmText: this.$t('GLOBAL._QD'),\n          cancelText: this.$t('GLOBAL._QX')\n        })\n        const res = await delSopDir([data.id])\n        if (res.success) {\n          this.$message.success(res.msg || '删除成功')\n          await this.getDirTree()\n        } else {\n          this.$message.error(res.msg || '删除失败')\n        }\n      } catch (err) {\n        if (err) {\n          console.error('删除目录失败:', err)\n          this.$message.error('删除失败')\n        }\n      }\n    },\n\n    // 展开所有节点\n    expandAll() {\n      this.expandNodes(this.$refs.tree.store.root, true)          \n    },\n\n    // 收起所有节点\n    collapseAll() {\n      this.expandNodes(this.$refs.tree.store.root, false)\n    },\n\n    // 处理文件下载\n    async handleDownload(row) {\n      try {\n        const res = await downloadSopDoc(row.FileUuid)\n        // 创建下载链接\n        const blob = new Blob([res], { type: res.type })\n        // const fileName = row.DocName.split('.').slice(0, -1).join('.') // 移除最后一个扩展名\n        const link = document.createElement('a')\n        link.href = window.URL.createObjectURL(blob)\n        link.download = row.DocName\n        link.click()\n        window.URL.revokeObjectURL(link.href)\n      } catch (err) {\n        console.error('文件下载失败:', err)\n        this.$message.error('文件下载失败')\n      }\n    },\n    //树节点展开关闭\n    expandNodes(node, type){\n      node.expanded = type;\n      for(let i = 0; i<node.childNodes.length; i++){\n        node.childNodes[i].expanded = type;\n        if(node.childNodes[i].childNodes.length > 0){\n          this.expandNodes(node.childNodes[i], type);\n        }\n      }\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n.search-form-item {\n  .el-form-item__content {\n    width: 180px; // 统一宽度为180px\n  }\n}\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .InventorySearchBox {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .search-form {\n      :deep(.el-form) {\n        .el-form-item--small.el-form-item {\n          margin-bottom: 0;\n        }\n      }\n\n      .form-content {\n        padding: 4px;\n        \n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .el-form-item {\n              margin: 0;\n              flex: none;\n              \n              .el-form-item__label {\n                padding-right: 4px;\n                line-height: 26px;\n                font-size: 12px;\n              }\n\n              .el-form-item__content {\n                line-height: 26px;\n\n                .el-input,\n                .el-select {\n                  :deep(.el-input__inner) {\n                    height: 26px;\n                    line-height: 26px;\n                    padding: 0 8px;\n                    font-size: 12px;\n                  }\n\n                  :deep(.el-input-group__append) {\n                    padding: 0;\n                    .el-button {\n                      padding: 0 10px;\n                      height: 26px;\n                      border: none;\n                    }\n                  }\n                }\n              }\n            }\n\n            .el-button {\n              height: 26px;\n              padding: 0 10px;\n              font-size: 12px;\n              margin-left: 4px;\n              \n              [class^=\"el-icon-\"] {\n                margin-right: 3px;\n                font-size: 12px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .el-table {\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n}\n</style>\n"]}]}