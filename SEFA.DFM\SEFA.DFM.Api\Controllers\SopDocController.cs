﻿using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.IServices;
using SEFA.DFM.Model.ViewModels.ENUM;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [AllowAnonymous]
    public class SopDocController : BaseApiController
    {
        /// <summary>
        /// SopDoc
        /// </summary>
        private readonly ISopDocServices _sopDocServices;

        private readonly IMinioFileService _minioFileServices;
        private readonly ISopAuditServices _isopauthServices;
        private readonly IUser _user;

        public SopDocController(ISopDocServices SopDocServices, IMinioFileService minioFileServices,
            ISopAuditServices isopauthServices,
            IUser user)
        {
            _minioFileServices = minioFileServices;
            _sopDocServices = SopDocServices;
            _isopauthServices = isopauthServices;
            _user = user;
        }

        [HttpPost]
        public async Task<MessageModel<string>> BatchAdd([FromBody] List<SopDocEntity> docs)
        {
            if (docs == null || docs.Count == 0)
            {
                return Failed("请求数据不能为空");
            }

            var result = await _sopDocServices.BatchAddAsync(docs);
            if (result)
            {
                return Success("", "批量新增成功");
            }

            return Failed("批量新增失败");
        }

        /// <summary>
        /// 根据文档编码获取最新版本号
        /// </summary>
        /// <param name="docCode">文档编码</param>
        /// <returns>最新版本号</returns>
        [HttpGet]
        public async Task<MessageModel<string>> GetLatestVersionByCode([FromQuery] string docCode)
        {
            var version = await _sopDocServices.GetLatestVersionByCode(docCode);
            return Success(version, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<SopDocEntity>>> GetList([FromBody] SopDocRequestModel reqModel)
        {
            var data = await _sopDocServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<SopDocEntity>>> GetPageList([FromBody] SopDocRequestModel reqModel)
        {
            var data = await _sopDocServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<SopDocEntity>> GetEntity(string id)
        {
            var data = await _sopDocServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] SopDocEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _sopDocServices.Add(request) > 0;
                var clientIp = HttpContext.Connection.RemoteIpAddress?.ToString();
                SopAuditEntity sopAuditEntity = new SopAuditEntity()
                {
                    DocId = request.ID,
                    OperationType = (int)OperationType.Create,
                    OperatorId = _user.Name,
                    OperateTime = DateTime.Now,
                    ClientIp = clientIp,
                    Deleted = 0
                };
                sopAuditEntity.CreateCustomGuid(_user.Name);
                await _isopauthServices.Add(sopAuditEntity);
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _sopDocServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] SopDocEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _sopDocServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _sopDocServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [Consumes("multipart/form-data")]
        [HttpPost]
        public async Task<MessageModel<string>> Upload(IFormFile file)
        {
            var data = new MessageModel<string>();
            try
            {
                var fileName = await _minioFileServices.Upload(file);
                return Success(fileName, "上传成功");
            }
            catch (Exception e)
            {
                return Failed(e.Message, "上传失败");
            }
        }

        [HttpGet("{fileName}")]
        public async Task<ActionResult> Download(string fileName)
        {
            var stream = await _minioFileServices.Download(fileName);
            //return File(stream, "application/octet-stream", apkName.ItemValue);
            return File(stream, GetContentType(fileName));
        }

        #region 获取文件ContentType类型

        private static string GetContentType(string fileName)
        {
            if (fileName.Contains(".jpg"))
            {
                return "image/jpg";
            }
            else if (fileName.Contains(".jpeg"))
            {
                return "image/jpeg";
            }
            else if (fileName.Contains(".png"))
            {
                return "image/png";
            }
            else if (fileName.Contains(".gif"))
            {
                return "image/gif";
            }
            else if (fileName.Contains(".pdf"))
            {
                return "application/pdf";
            }
            else if (fileName.Contains(".docx"))
            {
                return "application/msword";
            }
            else if (fileName.Contains(".txt"))
            {
                return "text/plain";
            }
            else
            {
                return "application/octet-stream";
            }
        }

        #endregion 获取文件类型
    }
    //public class SopDocRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}