{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Producting\\Overview\\index.vue", "mtime": 1750254216296}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgJ0Avdmlld3MvSW52ZW50b3J5L215c3R5bGUuc2Nzcyc7DQppbXBvcnQgeyBQT01hbmFnZW1lbk92ZXJ2aWV3IH0gZnJvbSAnQC9jb2x1bW5zL2ZhY3RvcnlQbGFudC90YWJsZUhlYWRlcnMnOw0KaW1wb3J0IHsNCiAgICBHZXRQcm9jZXNzT3JkZXJWaWV3U2VnbWVudHMsDQogICAgR2V0UHJvY2Vzc09yZGVyVmlldywNCiAgICBHZXRFcXVpcG1lbnRGdW5jdGlvblZpZXcsDQogICAgR2V0RXF1aXBtZW50UHJvY2Vzc09yZGVyVmlldywNCiAgICBnZXRDaGVja1RpcHBpbmdUeXBlLA0KICAgIEdldENvb2tpZU9yZGVyTHRleHRzLA0KICAgIEdldEJCYXRjaExpc3RWaWV3LA0KICAgIEdldEJhdGNoQ29kZSwNCiAgICBQb1Byb2R1Y2VkU3RhcnQsDQogICAgUG9Qcm9kdWNlZFJlc3VtZSwNCiAgICBHZXRSdW5PcmRlciwNCiAgICBHZXRDb29rT3JkZXJMdGV4dHMsDQogICAgR2V0UHJvY2Vzc09yZGVyVmlld1NlZ21lbnRVbml0cywNCiAgICBHZXRQb0xpc3QNCn0gZnJvbSAnQC9hcGkvSW52ZW50b3J5L092ZXJ2aWV3LmpzJzsNCmltcG9ydCB7IFBPTWFuYWdlbWVuUG9MaXN0IH0gZnJvbSAnQC9jb2x1bW5zL2ZhY3RvcnlQbGFudC90YWJsZUhlYWRlcnMnOw0KaW1wb3J0IG1vbWVudCBmcm9tICdtb21lbnQnOw0KDQppbXBvcnQgeyBNZXNzYWdlIH0gZnJvbSAnZWxlbWVudC11aSc7DQppbXBvcnQgeyBDb2RlRGlmZiB9IGZyb20gJ3YtY29kZS1kaWZmJw0KZXhwb3J0IGRlZmF1bHQgew0KICAgIGNvbXBvbmVudHM6IHsNCiAgICAgICAgUGFyYW1ldGVyRG93bmxvYWQ6ICgpID0+IGltcG9ydCgnLi9jb21wb25lbnRzL1BhcmFtZXRlckRvd25sb2FkJyksDQogICAgICAgIFBPTWFuYWdlbWVudDogKCkgPT4gaW1wb3J0KCcuL2NvbXBvbmVudHMvUE9NYW5hZ2VtZW50JyksDQogICAgICAgIENvbnN1bWU6ICgpID0+IGltcG9ydCgnLi9jb21wb25lbnRzL0NvbnN1bWUnKSwNCiAgICAgICAgUHJvZHVjZTogKCkgPT4gaW1wb3J0KCcuL2NvbXBvbmVudHMvUHJvZHVjZScpLA0KICAgICAgICBUaXBwaW5nOiAoKSA9PiBpbXBvcnQoJy4vY29tcG9uZW50cy9UaXBwaW5nJyksDQogICAgICAgIFN0b3JhZ2U6ICgpID0+IGltcG9ydCgnLi9jb21wb25lbnRzL1N0b3JhZ2UnKSwNCiAgICAgICAgICAgICAgICBQZXJmb3JtYW5jZTogKCkgPT4gaW1wb3J0KCcuL2NvbXBvbmVudHMvUGVyZm9ybWFuY2UnKSwNCiAgICAgICAgTWF0ZXJpYWxQcmVwOiAoKSA9PiBpbXBvcnQoJy4vY29tcG9uZW50cy9NYXRlcmlhbFByZXAnKSwNCiAgICAgICAgUGVyZm9ybWFuY2VFdmVudHM6ICgpID0+IGltcG9ydCgnLi9jb21wb25lbnRzL1BlcmZvcm1hbmNlJyksDQogICAgICAgIExvZ3NoZWV0czogKCkgPT4gaW1wb3J0KCcuL2NvbXBvbmVudHMvTG9nc2hlZXRzJyksDQogICAgICAgIFRpcHBpbmdzY2FuOiAoKSA9PiBpbXBvcnQoJy4vY29tcG9uZW50cy9UaXBwaW5nc2NhbicpLA0KICAgICAgICBTYW1wbGVXZWlnaGluZzogKCkgPT4gaW1wb3J0KCcuL2NvbXBvbmVudHMvU2FtcGxlV2VpZ2hpbmcnKSwNCiAgICAgICAgUHJvY2Vzc1RleHQ6ICgpID0+IGltcG9ydCgnLi9jb21wb25lbnRzL1Byb2Nlc3Nsb25ndGV4dCcpLA0KICAgICAgQ29kZURpZmYNCiAgICB9LA0KICAgIGRhdGEoKSB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgICB2aWV3dGl0bGU6ICcnLA0KICAgICAgICAgICAgdGltZXBpY2tlcjogW10sDQogICAgICAgICAgICBBdmFpbGFibGVQT01hbmFnZW1lbkxpc3Q6IFtdLA0KICAgICAgICAgICAgQXZhaWxhYmxlaGVhZGVyOiBQT01hbmFnZW1lblBvTGlzdCwNCiAgICAgICAgICAgIFN0YXJ0TW9kZWw6IGZhbHNlLA0KICAgICAgICAgICAgTXlFcXVpcG1lbnQ6ICcnLA0KICAgICAgICAgICAgU2hvd1BPTGlzdDogZmFsc2UsDQogICAgICAgICAgICBNeUVxdWlwbWVudExpc3Q6IFtdLA0KICAgICAgICAgICAgU3RhcnRsaXN0OiBbDQogICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICBsYWJlbDogdGhpcy4kdCgnT3ZlcnZpZXcuU3RhcnRUaW1lJyksDQogICAgICAgICAgICAgICAgICAgIGlkOiAnU3RhcnRUaW1lJywNCiAgICAgICAgICAgICAgICAgICAgdmFsdWU6ICcnLA0KICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2RhdGUnDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgIGxhYmVsOiB0aGlzLiR0KCdPdmVydmlldy5CYXRjaCcpLA0KICAgICAgICAgICAgICAgICAgICBpZDogJ0JhdGNoSWQnLA0KICAgICAgICAgICAgICAgICAgICB2YWx1ZTogJycsDQogICAgICAgICAgICAgICAgICAgIHJlcXVpcmU6IHRydWUsDQogICAgICAgICAgICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLA0KICAgICAgICAgICAgICAgICAgICBvcHRpb246IFtdDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgIGxhYmVsOiB0aGlzLiR0KCdPdmVydmlldy5CYXRjaENvZGUnKSwNCiAgICAgICAgICAgICAgICAgICAgaWQ6ICdMb3RDb2RlJywNCiAgICAgICAgICAgICAgICAgICAgdmFsdWU6ICcnLA0KICAgICAgICAgICAgICAgICAgICB2YWx1ZTI6ICcnLA0KICAgICAgICAgICAgICAgICAgICB2YWx1ZTM6ICcnLA0KICAgICAgICAgICAgICAgICAgICByZXF1aXJlOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICB0eXBlOiAnQmF0Y2hDb2RlJw0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICBsYWJlbDogdGhpcy4kdCgnT3ZlcnZpZXcuUHJvZHVjdGlvbkRhdGUnKSwNCiAgICAgICAgICAgICAgICAgICAgaWQ6ICdQcm9kdWN0aW9uRGF0ZScsDQogICAgICAgICAgICAgICAgICAgIHJlcXVpcmU6IHRydWUsDQogICAgICAgICAgICAgICAgICAgIHZhbHVlOiAnJywNCiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2RhdGUnDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgIGxhYmVsOiB0aGlzLiR0KCdPdmVydmlldy5FeHBpcmF0aW9uRGF0ZScpLA0KICAgICAgICAgICAgICAgICAgICBpZDogJ0V4cGlyYXRpb25EYXRlJywNCiAgICAgICAgICAgICAgICAgICAgcmVxdWlyZTogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgdmFsdWU6ICcnLA0KICAgICAgICAgICAgICAgICAgICB0eXBlOiAnZGF0ZScsDQogICAgICAgICAgICAgICAgICAgIGRpc2FibGVkOiB0cnVlDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgIGxhYmVsOiB0aGlzLiR0KCdPdmVydmlldy5UYXJnZXRRdWFudGl0eScpLA0KICAgICAgICAgICAgICAgICAgICBpZDogJ1RhcmdldFF1YW50aXR5JywNCiAgICAgICAgICAgICAgICAgICAgdmFsdWU6ICcnDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgIGxhYmVsOiB0aGlzLiR0KCdPdmVydmlldy5DcmV3U2l6ZScpLA0KICAgICAgICAgICAgICAgICAgICBpZDogJ0NyZXdTaXplJywNCiAgICAgICAgICAgICAgICAgICAgdmFsdWU6ICcnLA0KICAgICAgICAgICAgICAgICAgICB0eXBlOiAnaW5wdXQnDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgIGxhYmVsOiAnJywNCiAgICAgICAgICAgICAgICAgICAgaWQ6ICdJc1VwZGF0ZUx0eHQnLA0KICAgICAgICAgICAgICAgICAgICB2YWx1ZTogZmFsc2UsDQogICAgICAgICAgICAgICAgICAgIHR5cGU6ICdjaGVja0JveCcNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICBdLA0KICAgICAgICAgICAgc2VhcmNobGlzdDogWw0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2lucHV0JywNCiAgICAgICAgICAgICAgICAgICAgbmFtZTogdGhpcy4kdCgnT3ZlcnZpZXcuUXVpY2tTZWFyY2gnKSwNCiAgICAgICAgICAgICAgICAgICAgaWQ6ICdRdWlja1NlYXJjaCcsDQogICAgICAgICAgICAgICAgICAgIHZhbHVlOiAnJw0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICB0eXBlOiAnc2VsZWN0JywNCiAgICAgICAgICAgICAgICAgICAgb3B0aW9uOiBbXSwNCiAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICcyMHZoJywNCiAgICAgICAgICAgICAgICAgICAgbmFtZTogdGhpcy4kdCgnREZNX0pZWE0uU2VnbWVudCcpLA0KICAgICAgICAgICAgICAgICAgICBpZDogJ1NlZ21lbnQnLA0KICAgICAgICAgICAgICAgICAgICB2YWx1ZTogJycNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2lucHV0JywNCiAgICAgICAgICAgICAgICAgICAgdmFsdWU6ICcnLA0KICAgICAgICAgICAgICAgICAgICBpZDogJ0ZpbGxMaW5lQ29kZScsDQogICAgICAgICAgICAgICAgICAgIG5hbWU6IHRoaXMuJHQoJyR2dWV0aWZ5LmRhdGFUYWJsZS5QUk9fUE9MSVNULkZpbGxMaW5lQ29kZScpDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgXSwNCiAgICAgICAgICAgIGNob29zZUl0ZW06IHsNCiAgICAgICAgICAgICAgICBQcm9jZXNzT3JkZXI6ICcnLA0KICAgICAgICAgICAgICAgIGlzUmVzdW1lOiBmYWxzZSwNCiAgICAgICAgICAgICAgICBUYXJnZXRRdWFudGl0eTogJycsDQogICAgICAgICAgICAgICAgVW5pdDE6ICcnLA0KICAgICAgICAgICAgICAgIE1hdGVyaWFsQ29kZTogJycsDQogICAgICAgICAgICAgICAgTWF0ZXJpYWxOYW1lOiAnJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHRhYmxlTGlzdDogW10sDQogICAgICAgICAgICBFcXVpcG1lbnRsaXN0OiBbXSwNCiAgICAgICAgICAgIEF2YWlsYWJsZXRhYmxlSWQ6ICdQUk9fUE9NYW5hZ2VtZW50JywNCiAgICAgICAgICAgIHRhYmxlSWQ6ICdQUk9fT3ZlcnZpZXcnLA0KICAgICAgICAgICAgaGVhZGVyOiBQT01hbmFnZW1lbk92ZXJ2aWV3LA0KICAgICAgICAgICAgcGFnZU9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgICB0b3RhbDogMCwNCiAgICAgICAgICAgICAgICBwYWdlOiAxLCAvLyDlvZPliY3pobXnoIENCiAgICAgICAgICAgICAgICBwYWdlU2l6ZTogMjAsIC8vIOS4gOmhteaVsOaNrg0KICAgICAgICAgICAgICAgIHBhZ2VDb3VudDogMSwgLy8g6aG156CB5YiG6aG15pWwDQogICAgICAgICAgICAgICAgcGFnZVNpemVpdGVtczogWzEwLCAyMCwgNTAsIDEwMCwgNTAwXQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHBhZ2VPcHRpb25zMjogew0KICAgICAgICAgICAgICAgIHRvdGFsOiAwLA0KICAgICAgICAgICAgICAgIHBhZ2U6IDEsIC8vIOW9k+WJjemhteeggQ0KICAgICAgICAgICAgICAgIHBhZ2VTaXplOiAyMCwgLy8g5LiA6aG15pWw5o2uDQogICAgICAgICAgICAgICAgcGFnZUNvdW50OiAxLCAvLyDpobXnoIHliIbpobXmlbANCiAgICAgICAgICAgICAgICBwYWdlU2l6ZWl0ZW1zOiBbMTAsIDIwLCA1MCwgMTAwLCA1MDBdDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYWN0aXZlTmFtZTogJzEnLA0KICAgICAgICAgICAgYWN0aXZlTmFtZTI6ICcnLA0KICAgICAgICAgICAgQWN0aXZlTGlzdDogW10sDQogICAgICAgICAgICBFcXVpcG1lbnRHcm91cFJvd0lkOiAnJywNCiAgICAgICAgICAgIEVxdWlwbWVudElkOiAnJywNCiAgICAgICAgICAgIFJ1bkVxdWlwbWVudElkOiAnJywNCiAgICAgICAgICAgIEJhdGNoSWQ6ICcnLA0KICAgICAgICAgICAgRXhlY3V0aW9uSWQ6ICcnLA0KICAgICAgICAgICAgcnVubmluZ0NvZGU6ICcnLA0KICAgICAgICAgICAgaXNUaXBwaW5nc2NhbjogZmFsc2UsDQogICAgICAgICAgICBwcm9kdWN0aW9uSWQ6ICcnLA0KICAgICAgICAgICAgaXNFZGl0OiBmYWxzZSwNCiAgICAgICAgICAgIElzRGlmZmVyZW50OiBmYWxzZSwNCiAgICAgICAgICAgIEVxdWlwbWVudENvZGU6ICcnLA0KICAgICAgICAgICAgSXNQYWNrOiAnMScsDQogICAgICAgICAgICBUZXh0MTogJycsDQogICAgICAgICAgICBUZXh0MjogJycNCiAgICAgICAgfTsNCiAgICB9LA0KICAgIG1vdW50ZWQoKSB7DQogICAgICAgIHRoaXMudmlld3RpdGxlID0gSlNPTi5wYXJzZSh0aGlzLiRyb3V0ZS5xdWVyeS5xdWVyeSkuRGVzY3JpcHRpb247DQogICAgICAgIGNvbnNvbGUubG9nKHRoaXMudmlld3RpdGxlKTsNCiAgICAgICAgdGhpcy5FcXVpcG1lbnRHcm91cFJvd0lkID0gSlNPTi5wYXJzZSh0aGlzLiRyb3V0ZS5xdWVyeS5xdWVyeSkuSUQ7DQogICAgICAgIC8vIHRoaXMuRmlsdGVyc1RhYmxlRGF0YSgpOw0KICAgICAgICB0aGlzLkdldFByb2Nlc3NPcmRlclZpZXdMaXN0KCk7DQogICAgICAgIC8vdGhpcy5HZXRQcm9jZXNzT3JkZXJWaWV3MigpOw0KICAgICAgICB0aGlzLkdldFNlZ21lbnQoKTsNCiAgICAgICAgdGhpcy5HZXRFcXVpcG1lbnQoKTsNCiAgICAgICAgdGhpcy5jaGFuZ2VQYWdpbmF0aW9uKCk7DQogICAgfSwNCiAgICBtZXRob2RzOiB7DQogICAgICAgIGFzeW5jIFByb2R1Y2VkU3RhcnQoKSB7DQogICAgICAgICAgICBsZXQgZmxhZyA9IHRoaXMuU3RhcnRsaXN0LnNvbWUoaXRlbSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKGl0ZW0ucmVxdWlyZSkgew0KICAgICAgICAgICAgICAgICAgICByZXR1cm4gaXRlbS52YWx1ZSA9PSAnJzsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIGlmICh0aGlzLk15RXF1aXBtZW50ID09ICcnKSB7DQogICAgICAgICAgICAgICAgZmxhZyA9IHRydWU7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBpZiAoZmxhZykgew0KICAgICAgICAgICAgICAgIE1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBgJHt0aGlzLiR0KCdJbnZlbnRvcnkuVG9PdmVyJyl9YCwNCiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgbGV0IHBhcmFtcyA9IHsNCiAgICAgICAgICAgICAgICBTZWdtZW50SWQ6IHRoaXMuY2hvb3NlSXRlbS5TZWdtZW50SWQsDQogICAgICAgICAgICAgICAgUHJvZHVjdGlvbk9yZGVySWQ6IHRoaXMuY2hvb3NlSXRlbS5Qcm9kdWN0aW9uT3JkZXJJZCwNCiAgICAgICAgICAgICAgICBQb1NlZ21lbnRSZXF1aXJlbWVudElkOiB0aGlzLmNob29zZUl0ZW0uSUQsDQogICAgICAgICAgICAgICAgQmF0Y2hJZDogJycsDQogICAgICAgICAgICAgICAgTG90Q29kZTogJycsDQogICAgICAgICAgICAgICAgRXF1aXBtZW50SWQ6IHRoaXMuTXlFcXVpcG1lbnQsDQogICAgICAgICAgICAgICAgU3RhcnRUaW1lOiAnJywNCiAgICAgICAgICAgICAgICBQcm9kdWN0aW9uRGF0ZTogJycsDQogICAgICAgICAgICAgICAgRXhwaXJhdGlvbkRhdGU6ICcnDQogICAgICAgICAgICB9Ow0KICAgICAgICAgICAgaWYgKHRoaXMuY2hvb3NlSXRlbS5pc1Jlc3VtZSA9PSB0cnVlKSB7DQogICAgICAgICAgICAgICAgcGFyYW1zLkV4ZWN1dGlvbklkID0gdGhpcy5jaG9vc2VJdGVtLkV4ZWN1dGlvbklkOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICBwYXJhbXMuRXhlY3V0aW9uSWQgPSAnJzsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHRoaXMuU3RhcnRsaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKGl0ZW0uaWQgPT0gJ0xvdENvZGUnKSB7DQogICAgICAgICAgICAgICAgICAgIHBhcmFtc1tpdGVtLmlkXSA9IGl0ZW0udmFsdWUgKyBpdGVtLnZhbHVlMiArIGl0ZW0udmFsdWUzOw0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgIHBhcmFtc1tpdGVtLmlkXSA9IGl0ZW0udmFsdWU7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgICBwYXJhbXMuRXhwaXJhdGlvbkRhdGUgPSBtb21lbnQocGFyYW1zLkV4cGlyYXRpb25EYXRlKS5mb3JtYXQoJ1lZWVktTU0tREQgSEg6bW06c3MnKTsNCiAgICAgICAgICAgIGlmIChwYXJhbXMuTG90Q29kZS5sZW5ndGggPiAxMCkgew0KICAgICAgICAgICAgICAgIE1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBgJHt0aGlzLiR0KCdPdmVydmlldy5CYXRjaENvZGVMb25nJyl9YCwNCiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgbGV0IHJlczsNCiAgICAgICAgICAgIGlmICh0aGlzLmNob29zZUl0ZW0uaXNSZXN1bWUgPT0gdHJ1ZSkgew0KICAgICAgICAgICAgICAgIHJlcyA9IGF3YWl0IFBvUHJvZHVjZWRSZXN1bWUocGFyYW1zKTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgcmVzID0gYXdhaXQgUG9Qcm9kdWNlZFN0YXJ0KHBhcmFtcyk7DQogICAgICAgICAgICB9DQogICAgICAgICAgICAvL3RoaXMuR2V0UHJvY2Vzc09yZGVyVmlldzIoKTsNCiAgICAgICAgICAgIHRoaXMubG9hZFByb2dyZXNzKCk7DQogICAgICAgICAgICB0aGlzLlN0YXJ0TW9kZWwgPSBmYWxzZTsNCiAgICAgICAgICAgIE1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5tc2csDQogICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgR2V0RGF0ZShpZCkgew0KICAgICAgICAgICAgaWYgKGlkID09ICdQcm9kdWN0aW9uRGF0ZScpIHsNCiAgICAgICAgICAgICAgICBpZiAodGhpcy5jaG9vc2VJdGVtLk5lZWRRQVJlbGVhc2UgPT0gJzEnKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuU3RhcnRsaXN0WzRdLnZhbHVlID0gdGhpcy5hZGREYXlzKHRoaXMuU3RhcnRsaXN0WzNdLnZhbHVlLCB0aGlzLmNob29zZUl0ZW0uTWhkaGIsIHRoaXMuY2hvb3NlSXRlbS5JcHJreik7DQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5TdGFydGxpc3RbNF0udmFsdWUgPSB0aGlzLmFkZERheXModGhpcy5jaG9vc2VJdGVtLlBsYW5TdGFydFRpbWUsIHRoaXMuY2hvb3NlSXRlbS5NaGRoYiwgdGhpcy5jaG9vc2VJdGVtLklwcmt6KTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHN0YXJ0T3JkZXIoaXRlbSkgew0KICAgICAgICAgICAgLy8gdGhpcy5NeUVxdWlwbWVudCA9ICcnOw0KICAgICAgICAgICAgLy8gdGhpcy5FcXVpcG1lbnRDb2RlID0gJyc7DQogICAgICAgICAgICB0aGlzLlNlZ21lbnRVbml0cyhpdGVtKTsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKGl0ZW0ucm93KTsNCiAgICAgICAgICAgIGlmIChpdGVtLnJvdykgew0KICAgICAgICAgICAgICAgIHRoaXMuSXNQYWNrID0gaXRlbS5yb3cuTmVlZFFBUmVsZWFzZTsNCiAgICAgICAgICAgICAgICB0aGlzLmdldEx0ZXh0KGl0ZW0ucm93LlByb2R1Y3Rpb25PcmRlcklkKTsNCiAgICAgICAgICAgICAgICB0aGlzLmNob29zZUl0ZW0gPSBpdGVtLnJvdzsNCiAgICAgICAgICAgICAgICB0aGlzLmNob29zZUl0ZW0uaXNSZXN1bWUgPSBmYWxzZTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdGhpcy5jaG9vc2VJdGVtID0gaXRlbTsNCiAgICAgICAgICAgICAgICB0aGlzLmNob29zZUl0ZW0uaXNSZXN1bWUgPSB0cnVlOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy5TdGFydGxpc3QuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAoaW5kZXggPT0gMCkgew0KICAgICAgICAgICAgICAgICAgICBpdGVtLnZhbHVlID0gbW9tZW50KG5ldyBEYXRlKCkpLmZvcm1hdCgnWVlZWS1NTS1ERCBISDptbTpzcycpOw0KICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoaW5kZXggPT0gMykgew0KICAgICAgICAgICAgICAgICAgICBpdGVtLnZhbHVlID0gbW9tZW50KG5ldyBEYXRlKCkpLmZvcm1hdCgnWVlZWS1NTS1ERCBISDptbTpzcycpOw0KICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoaW5kZXggPT0gMikgew0KICAgICAgICAgICAgICAgICAgICBpdGVtLnZhbHVlID0gJyc7DQogICAgICAgICAgICAgICAgICAgIGl0ZW0udmFsdWUyID0gJyc7DQogICAgICAgICAgICAgICAgICAgIGl0ZW0udmFsdWUzID0gJyc7DQogICAgICAgICAgICAgICAgfSBlbHNlIGlmIChpdGVtLmlkID09ICdJc1VwZGF0ZUx0eHQnKSB7DQogICAgICAgICAgICAgICAgICAgIGl0ZW0udmFsdWUgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICBpdGVtLnZhbHVlID0gJyc7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB0aGlzLkdldERhdGUoJ1Byb2R1Y3Rpb25EYXRlJyk7DQogICAgICAgICAgICB0aGlzLmdldEJhdGNoTGlzdCgpOw0KICAgICAgICB9LA0KICAgICAgICBhc3luYyBTZWdtZW50VW5pdHMoaXRlbSkgew0KICAgICAgICAgICAgbGV0IHJlcyA9IGF3YWl0IEdldFByb2Nlc3NPcmRlclZpZXdTZWdtZW50VW5pdHMoJycsIGl0ZW0ucm93LlNlZ21lbnQpOw0KICAgICAgICAgICAgdGhpcy5NeUVxdWlwbWVudExpc3QgPSByZXMucmVzcG9uc2U7DQogICAgICAgIH0sDQogICAgICAgIGFzeW5jIGdldEJhdGNoQ29kZSgpIHsNCiAgICAgICAgICAgIGxldCBkYXRlID0gbW9tZW50KHRoaXMuU3RhcnRsaXN0WzNdLnZhbHVlKS5mb3JtYXQoJ1lZWVktTU0tREQgSEg6bW06c3MnKTsNCiAgICAgICAgICAgIGxldCBwID0gew0KICAgICAgICAgICAgICAgIExpbmVDb2RlOiB0aGlzLlN0YXJ0bGlzdFsyXS52YWx1ZSwNCiAgICAgICAgICAgICAgICBlcXVpcG1lbnRDb2RlOiB0aGlzLkVxdWlwbWVudENvZGUsDQogICAgICAgICAgICAgICAgcHJvZHVjdGlvbkRhdGU6IGRhdGUsDQogICAgICAgICAgICAgICAgcHJvZHVjdGlvbklkOiB0aGlzLmNob29zZUl0ZW0uUHJvZHVjdGlvbk9yZGVySWQNCiAgICAgICAgICAgIH07DQogICAgICAgICAgICBsZXQgcmVzID0gYXdhaXQgR2V0QmF0Y2hDb2RlKHApOw0KICAgICAgICAgICAgaWYgKHJlcy5yZXNwb25zZSA9PSBudWxsKSB7DQogICAgICAgICAgICAgICAgTWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5tc2csDQogICAgICAgICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICB0aGlzLlN0YXJ0bGlzdFsyXS52YWx1ZSA9IHJlcy5yZXNwb25zZS5zdWJzdHJpbmcoMCwgMik7DQogICAgICAgICAgICAgICAgdGhpcy5TdGFydGxpc3RbMl0udmFsdWUyID0gcmVzLnJlc3BvbnNlLnN1YnN0cmluZygyLCA1KTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgYXN5bmMgZ2V0QmF0Y2hMaXN0KCkgew0KICAgICAgICAgICAgbGV0IHBhcmFtcyA9IHsNCiAgICAgICAgICAgICAgICBQb1NlZ21lbnRSZXF1aXJlbWVudElkOiB0aGlzLmNob29zZUl0ZW0uSUQNCiAgICAgICAgICAgIH07DQogICAgICAgICAgICBsZXQgcmVzID0gYXdhaXQgR2V0QkJhdGNoTGlzdFZpZXcocGFyYW1zKTsNCiAgICAgICAgICAgIHRoaXMuU3RhcnRsaXN0WzFdLm9wdGlvbiA9IHJlcy5yZXNwb25zZTsNCiAgICAgICAgICAgIHRoaXMuU3RhcnRsaXN0WzFdLnZhbHVlID0gdGhpcy5TdGFydGxpc3RbMV0ub3B0aW9uWzBdLklEOw0KICAgICAgICAgICAgdGhpcy5TdGFydE1vZGVsID0gdHJ1ZTsNCiAgICAgICAgfSwNCiAgICAgICAgYWRkRGF5cyhkYXRlLCBudW1iZXIsIGludGVydmFsKSB7DQogICAgICAgICAgICBjb25zdCBuZXdEYXRlMSA9IG5ldyBEYXRlKGRhdGUpOw0KICAgICAgICAgICAgY29uc3QgbmV3RGF0ZSA9IG5ldyBEYXRlKG5ld0RhdGUxLmdldEZ1bGxZZWFyKCksIG5ld0RhdGUxLmdldE1vbnRoKCksIG5ld0RhdGUxLmdldERhdGUoKSk7DQogICAgICAgICAgICB0aGlzLkRhdGVBZGQoaW50ZXJ2YWwsIG51bWJlciwgbmV3RGF0ZSk7DQogICAgICAgICAgICBuZXdEYXRlLnNldERhdGUobmV3RGF0ZS5nZXREYXRlKCkgKyAxKTsgLy8g5aKe5Yqg5LiA5aSpDQogICAgICAgICAgICBuZXdEYXRlLnNldFNlY29uZHMobmV3RGF0ZS5nZXRTZWNvbmRzKCkgLSAxKTsgLy8g5YeP5Y67Meenkg0KICAgICAgICAgICAgcmV0dXJuIG5ld0RhdGU7DQogICAgICAgIH0sDQogICAgICAgIERhdGVBZGQoaW50ZXJ2YWwsIG51bWJlciwgZGF0ZSkgew0KICAgICAgICAgICAgc3dpdGNoIChpbnRlcnZhbCkgew0KICAgICAgICAgICAgICAgIGNhc2UgJ1knOiB7DQogICAgICAgICAgICAgICAgICAgIGRhdGUuc2V0RnVsbFllYXIoZGF0ZS5nZXRGdWxsWWVhcigpICsgbnVtYmVyKTsNCiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRhdGU7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGNhc2UgJ1EnOiB7DQogICAgICAgICAgICAgICAgICAgIGRhdGUuc2V0TW9udGgoZGF0ZS5nZXRNb250aCgpICsgbnVtYmVyICogMyk7DQogICAgICAgICAgICAgICAgICAgIHJldHVybiBkYXRlOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBjYXNlICdNJzogew0KICAgICAgICAgICAgICAgICAgICBkYXRlLnNldE1vbnRoKGRhdGUuZ2V0TW9udGgoKSArIG51bWJlcik7DQogICAgICAgICAgICAgICAgICAgIHJldHVybiBkYXRlOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBjYXNlICdXJzogew0KICAgICAgICAgICAgICAgICAgICBkYXRlLnNldERhdGUoZGF0ZS5nZXREYXRlKCkgKyBudW1iZXIgKiA3KTsNCiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRhdGU7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGNhc2UgJ0QnOiB7DQogICAgICAgICAgICAgICAgICAgIGRhdGUuc2V0RGF0ZShkYXRlLmdldERhdGUoKSArIG51bWJlcik7DQogICAgICAgICAgICAgICAgICAgIHJldHVybiBkYXRlOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBjYXNlICdoJzogew0KICAgICAgICAgICAgICAgICAgICBkYXRlLnNldEhvdXJzKGRhdGUuZ2V0SG91cnMoKSArIG51bWJlcik7DQogICAgICAgICAgICAgICAgICAgIHJldHVybiBkYXRlOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBjYXNlICdtJzogew0KICAgICAgICAgICAgICAgICAgICBkYXRlLnNldE1pbnV0ZXMoZGF0ZS5nZXRNaW51dGVzKCkgKyBudW1iZXIpOw0KICAgICAgICAgICAgICAgICAgICByZXR1cm4gZGF0ZTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgY2FzZSAncyc6IHsNCiAgICAgICAgICAgICAgICAgICAgZGF0ZS5zZXRTZWNvbmRzKGRhdGUuZ2V0U2Vjb25kcygpICsgbnVtYmVyKTsNCiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRhdGU7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGRlZmF1bHQ6IHsNCiAgICAgICAgICAgICAgICAgICAgZGF0ZS5zZXREYXRlKGRhdGUuZ2V0RGF0ZSgpICsgbnVtYmVyKTsNCiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRhdGU7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBhc3luYyBnZXRNeUVxdWlwbWVudCgpIHsNCiAgICAgICAgICAgIHRoaXMuTXlHZXRSdW5PcmRlcigpOw0KICAgICAgICAgICAgdGhpcy5NeUVxdWlwbWVudExpc3QuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgICAgICBpZiAoaXRlbS5JRCA9PSB0aGlzLk15RXF1aXBtZW50KSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuRXF1aXBtZW50Q29kZSA9IGl0ZW0uRXF1aXBtZW50Q29kZTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIHRoaXMuZ2V0QmF0Y2hDb2RlKCk7DQogICAgICAgIH0sDQogICAgICAgIGFzeW5jIE15R2V0UnVuT3JkZXIoKSB7DQogICAgICAgICAgICBsZXQgcmVzID0gYXdhaXQgR2V0UnVuT3JkZXIoJycsIHRoaXMuTXlFcXVpcG1lbnQpOw0KICAgICAgICAgICAgdGhpcy5ydW5uaW5nQ29kZSA9IHJlcy5yZXNwb25zZTsNCiAgICAgICAgICAgIGlmICh0aGlzLnJ1bm5pbmdDb2RlID09IG51bGwpIHsNCiAgICAgICAgICAgICAgICB0aGlzLnJ1bm5pbmdDb2RlID0gJyc7DQogICAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIGFzeW5jIGdldEx0ZXh0KGlkKSB7DQogICAgICAgICAgICBpZiAodGhpcy5Jc1BhY2sgPT09ICcwJykgew0KICAgICAgICAgICAgICAgIHRoaXMuSXNEaWZmZXJlbnQgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICBsZXQgcGFyYW1zID0gew0KICAgICAgICAgICAgICAgICAgICBpZDogaWQNCiAgICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgICAgIGxldCByID0gYXdhaXQgR2V0Q29va09yZGVyTHRleHRzKHBhcmFtcyk7DQogICAgICAgICAgICAgICAgaWYgKHIucmVzcG9uc2UubGVuZ3RoID09IDIpIHsNCiAgICAgICAgICAgICAgICAgICAgaWYgKHIubXNnID09PSAn6ZW/5paH5pys5LiN5LiA6Ie077yBJykgew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5Jc0RpZmZlcmVudCA9IHRydWU7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgdGhpcy5UZXh0MSA9IHIucmVzcG9uc2VbMF0uUHJvY2Vzc0RhdGE7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuVGV4dDIgPSByLnJlc3BvbnNlWzFdLlByb2Nlc3NEYXRhOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgYXN5bmMgR2V0U2VnbWVudCgpIHsNCiAgICAgICAgICAgIGxldCBwYXJhbXMgPSB7DQogICAgICAgICAgICAgICAgTGluZUNvZGU6IHRoaXMudmlld3RpdGxlDQogICAgICAgICAgICB9Ow0KICAgICAgICAgICAgbGV0IHJlcyA9IGF3YWl0IEdldFByb2Nlc3NPcmRlclZpZXdTZWdtZW50cyhwYXJhbXMpOw0KICAgICAgICAgICAgLy8gY29uc29sZS5sb2cocmVzLDE0MzIzNDUzNDUpDQogICAgICAgICAgICBsZXQgZGF0YSA9IHJlcy5yZXNwb25zZTsNCiAgICAgICAgICAgIGlmIChkYXRhLmxlbmd0aCAhPSAwKSB7DQogICAgICAgICAgICAgICAgZGF0YS5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgICAgICAgICBpZiAoaW5kZXggPT0gMCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zZWFyY2hsaXN0WzFdLnZhbHVlID0gaXRlbS5rZXk7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICB0aGlzLnNlYXJjaGxpc3RbMV0ub3B0aW9uID0gZGF0YTsNCiAgICAgICAgICAgICAgICB0aGlzLlNob3dQT0xpc3QgPSB0cnVlOw0KICAgICAgICAgICAgICAgIC8vdGhpcy5HZXRQcm9jZXNzT3JkZXJWaWV3MigpOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICB0aGlzLlNob3dQT0xpc3QgPSBmYWxzZTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgZ2V0c2VhcmNoKCkgew0KICAgICAgICAgICAgdGhpcy5wYWdlT3B0aW9uczIucGFnZSA9IDE7DQogICAgICAgICAgICB0aGlzLnBhZ2VPcHRpb25zMi5wYWdlU2l6ZSA9IDIwOw0KICAgICAgICAgICAgdGhpcy5HZXRQcm9jZXNzT3JkZXJWaWV3MigpOw0KICAgICAgICB9LA0KICAgICAgICBnZXRlbXB0eSgpIHsNCiAgICAgICAgICAgIHRoaXMuUXVpY2tTZWFyY2ggPSAnJzsNCiAgICAgICAgICAgIHRoaXMudGltZXBpY2tlciA9IFtdOw0KICAgICAgICAgICAgdGhpcy5wYWdlT3B0aW9uczIucGFnZSA9IDE7DQogICAgICAgICAgICB0aGlzLnBhZ2VPcHRpb25zMi5wYWdlU2l6ZSA9IDIwOw0KICAgICAgICAgICAgdGhpcy5zZWFyY2hsaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICAgICAgaXRlbS52YWx1ZSA9ICcnOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB0aGlzLkdldFByb2Nlc3NPcmRlclZpZXcyKCk7DQogICAgICAgIH0sDQogICAgICAgIGxvYWRQcm9ncmVzcyhpZCkgew0KICAgICAgICAgICAgdGhpcy5HZXRFcXVpcG1lbnQoKTsNCiAgICAgICAgICAgIHRoaXMuaGFuZGxlQ2xpY2soKTsNCiAgICAgICAgfSwNCiAgICAgICAgYmFjaygpIHsNCiAgICAgICAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7DQogICAgICAgIH0sDQogICAgICAgIGFzeW5jIEdldFByb2Nlc3NPcmRlclZpZXcyKCkgew0KICAgICAgICAgICAgLy8gY29uc29sZS5sb2coJzk5OTk5OScpOw0KICAgICAgICAgICAgaWYgKHRoaXMudGltZXBpY2tlciA9PSBudWxsKSB7DQogICAgICAgICAgICAgICAgdGhpcy50aW1lcGlja2VyID0gW107DQogICAgICAgICAgICB9DQogICAgICAgICAgICBsZXQgcGFyYW1zID0gew0KICAgICAgICAgICAgICAgIFNlYXJjaDogdGhpcy5zZWFyY2hsaXN0WzBdLnZhbHVlLA0KICAgICAgICAgICAgICAgIExpbmVDb2RlOiB0aGlzLnZpZXd0aXRsZSwNCiAgICAgICAgICAgICAgICBTZWdtZW50OiB0aGlzLnNlYXJjaGxpc3RbMV0udmFsdWUsDQogICAgICAgICAgICAgICAgRmlsbExpbmVDb2RlOiB0aGlzLnNlYXJjaGxpc3RbMl0udmFsdWUsDQogICAgICAgICAgICAgICAgcGFnZUluZGV4OiB0aGlzLnBhZ2VPcHRpb25zMi5wYWdlLA0KICAgICAgICAgICAgICAgIHBhZ2VTaXplOiB0aGlzLnBhZ2VPcHRpb25zMi5wYWdlU2l6ZSwNCiAgICAgICAgICAgICAgICBTdGFydFRpbWU6IHRoaXMudGltZXBpY2tlclswXSwNCiAgICAgICAgICAgICAgICBFbmRUaW1lOiB0aGlzLnRpbWVwaWNrZXJbMV0NCiAgICAgICAgICAgIH07DQogICAgICAgICAgICBsZXQgcmVzID0gYXdhaXQgR2V0UG9MaXN0KHBhcmFtcyk7DQogICAgICAgICAgICB0aGlzLkF2YWlsYWJsZVBPTWFuYWdlbWVuTGlzdCA9IHJlcy5yZXNwb25zZS5kYXRhOw0KICAgICAgICAgICAgdGhpcy5wYWdlT3B0aW9uczIudG90YWwgPSByZXMucmVzcG9uc2UuZGF0YUNvdW50Ow0KICAgICAgICAgICAgbGV0IGVsID0gZG9jdW1lbnQuZ2V0RWxlbWVudHNCeUNsYXNzTmFtZShgZWwtcGFnaW5hdGlvbl9fdG90YWxgKTsNCiAgICAgICAgICAgIGVsWzBdLmlubmVySFRNTCA9IGAke3RoaXMuJHQoJ1BBR0lOQVRJT04uVE9UQUxfQ04nKX0ke3RoaXMucGFnZU9wdGlvbnMyLnRvdGFsfSR7dGhpcy4kdCgnUEFHSU5BVElPTi5UT1RBTCcpfWA7DQogICAgICAgICAgICBsZXQgZWwzID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLmVsLXBhZ2luYXRpb25fX3NpemVzJykucXVlcnlTZWxlY3RvckFsbCgnLmVsLWlucHV0X19pbm5lcicpOw0KICAgICAgICAgICAgZWwzWzBdLnZhbHVlID0gZWwzWzBdLnZhbHVlLnJlcGxhY2UoJ+adoS/pobUnLCB0aGlzLiR0KCdQQUdJTkFUSU9OLk1ZUEFHRScpKTsNCiAgICAgICAgICAgIC8vIHRoaXMuZ2V0TnVtVG9mYXRoZXIoKTsNCiAgICAgICAgfSwNCiAgICAgICAgYXN5bmMgR2V0UHJvY2Vzc09yZGVyVmlld0xpc3QoKSB7DQogICAgICAgICAgICBjb25zb2xlLmxvZygxMik7DQogICAgICAgICAgICBsZXQgcGFyYW1zID0gew0KICAgICAgICAgICAgICAgIEVxdWlwbWVudEdyb3VwUm93SWQ6IHRoaXMuRXF1aXBtZW50R3JvdXBSb3dJZCwNCiAgICAgICAgICAgICAgICBwYWdlSW5kZXg6IHRoaXMucGFnZU9wdGlvbnMucGFnZSwNCiAgICAgICAgICAgICAgICBwYWdlU2l6ZTogdGhpcy5wYWdlT3B0aW9ucy5wYWdlU2l6ZQ0KICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIGxldCByZXMgPSBhd2FpdCBHZXRFcXVpcG1lbnRQcm9jZXNzT3JkZXJWaWV3KHBhcmFtcyk7DQogICAgICAgICAgICB0aGlzLnRhYmxlTGlzdCA9IHJlcy5yZXNwb25zZS5kYXRhOw0KICAgICAgICAgICAgdGhpcy5wYWdlT3B0aW9ucy50b3RhbCA9IHJlcy5yZXNwb25zZS5kYXRhQ291bnQ7DQogICAgICAgICAgICBsZXQgZWwgPSBkb2N1bWVudC5nZXRFbGVtZW50c0J5Q2xhc3NOYW1lKGBlbC1wYWdpbmF0aW9uX190b3RhbGApOw0KICAgICAgICAgICAgZWxbMF0uaW5uZXJIVE1MID0gYCR7dGhpcy4kdCgnUEFHSU5BVElPTi5UT1RBTF9DTicpfSR7dGhpcy5wYWdlT3B0aW9ucy50b3RhbH0ke3RoaXMuJHQoJ1BBR0lOQVRJT04uVE9UQUwnKX1gOw0KICAgICAgICAgICAgbGV0IGVsMyA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJy5lbC1wYWdpbmF0aW9uX19zaXplcycpLnF1ZXJ5U2VsZWN0b3JBbGwoJy5lbC1pbnB1dF9faW5uZXInKTsNCiAgICAgICAgICAgIGVsM1swXS52YWx1ZSA9IGVsM1swXS52YWx1ZS5yZXBsYWNlKCfmnaEv6aG1JywgdGhpcy4kdCgnUEFHSU5BVElPTi5NWVBBR0UnKSk7DQogICAgICAgIH0sDQogICAgICAgIGNoYW5nZVBhZ2luYXRpb24oKSB7DQogICAgICAgICAgICBsZXQgZWwyID0gZG9jdW1lbnQuZ2V0RWxlbWVudHNCeUNsYXNzTmFtZShgZWwtc2VsZWN0LWRyb3Bkb3duX19pdGVtYCk7DQogICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGVsMi5sZW5ndGg7IGkrKykgew0KICAgICAgICAgICAgICAgIGVsMltpXS5pbm5lckhUTUwgPSBlbDJbaV0uaW5uZXJIVE1MLnJlcGxhY2UoJ+adoS/pobUnLCB0aGlzLiR0KCdQQUdJTkFUSU9OLk1ZUEFHRScpKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgYXN5bmMgR2V0RXF1aXBtZW50KCkgew0KICAgICAgICAgICAgbGV0IHBhcmFtcyA9IHsNCiAgICAgICAgICAgICAgICBFcXVpcG1lbnRHcm91cFJvd0lkOiB0aGlzLkVxdWlwbWVudEdyb3VwUm93SWQsDQogICAgICAgICAgICAgICAgcGFnZUluZGV4OiAxLA0KICAgICAgICAgICAgICAgIHBhZ2VTaXplOiAxMDAwDQogICAgICAgICAgICB9Ow0KDQogICAgICAgICAgICBsZXQgcmVzID0gYXdhaXQgR2V0RXF1aXBtZW50RnVuY3Rpb25WaWV3KHBhcmFtcyk7DQogICAgICAgICAgICBpZiAocmVzLnJlc3BvbnNlLmxlbmd0aCAhPSAwKSB7DQogICAgICAgICAgICAgICAgcmVzLnJlc3BvbnNlLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICAgICAgICAgIGl0ZW0uZnVuY3Rpb25saXN0ID0gaXRlbS5GdW5jdGlvbkNvZGVzLnNwbGl0KCcsJyk7DQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLkVxdWlwbWVudGxpc3QgPSByZXMucmVzcG9uc2U7DQogICAgICAgIH0sDQogICAgICAgIGFzeW5jIGhhbmRsZUNsaWNrMihpdGVtLCBpbmRleCkgew0KICAgICAgICAgICAgaWYgKHRoaXMuYWN0aXZlTmFtZTIgPT0gJ0xvZ3NoZWV0cycpIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzW2luZGV4ICsgdGhpcy5hY3RpdmVOYW1lMl1bMF0uYWN0aXZlTmFtZSA9ICcwJzsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMuQWN0aXZlTGlzdCwgMSk7DQogICAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLiRyZWZzW2luZGV4ICsgdGhpcy5hY3RpdmVOYW1lMl1bMF0pOw0KICAgICAgICAgICAgLy8gdGhpcy5HZXRFcXVpcG1lbnQoKTsNCiAgICAgICAgICAgIGxldCBwYXJhbXMgPSB7DQogICAgICAgICAgICAgICAgRXF1aXBtZW50R3JvdXBSb3dJZDogdGhpcy5FcXVpcG1lbnRHcm91cFJvd0lkLA0KICAgICAgICAgICAgICAgIFJ1bkVxdWlwbWVudElkOiB0aGlzLkVxdWlwbWVudElkLA0KICAgICAgICAgICAgICAgIHBhZ2VJbmRleDogdGhpcy5wYWdlT3B0aW9ucy5wYWdlLA0KICAgICAgICAgICAgICAgIHBhZ2VTaXplOiB0aGlzLnBhZ2VPcHRpb25zLnBhZ2VTaXplDQogICAgICAgICAgICB9Ow0KICAgICAgICAgICAgbGV0IHJlcyA9IGF3YWl0IEdldEVxdWlwbWVudFByb2Nlc3NPcmRlclZpZXcocGFyYW1zKTsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlcywgMjIyNDQ0KTsNCiAgICAgICAgICAgIGlmIChyZXMucmVzcG9uc2UuZGF0YS5sZW5ndGggIT0gMCkgew0KICAgICAgICAgICAgICAgIGxldCBsaXN0ID0gcmVzLnJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgICAgICAgbGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgICAgICAgICBpdGVtLk1hdGVyaWFsID0gaXRlbS5NYXRlcmlhbE5hbWUgKyAnLScgKyBpdGVtLk1hdGVyaWFsQ29kZTsNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICB0aGlzLkFjdGl2ZUxpc3QgPSBsaXN0Ow0KICAgICAgICAgICAgICAgIHRoaXMuQmF0Y2hJZCA9IGxpc3RbMF0uQmF0Y2hJZDsNCiAgICAgICAgICAgICAgICB0aGlzLlJ1bkVxdWlwbWVudElkID0gbGlzdFswXS5SdW5FcXVpcG1lbnRJZDsNCiAgICAgICAgICAgICAgICB0aGlzLkV4ZWN1dGlvbklkID0gbGlzdFswXS5FeGVjdXRpb25JZDsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdGhpcy5BY3RpdmVMaXN0ID0gW107DQogICAgICAgICAgICAgICAgdGhpcy5CYXRjaElkID0gJyc7DQogICAgICAgICAgICAgICAgdGhpcy5SdW5FcXVpcG1lbnRJZCA9ICcnOw0KICAgICAgICAgICAgICAgIHRoaXMuRXhlY3V0aW9uSWQgPSAnJzsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgaWYgKHRoaXMuJHJlZnNbaW5kZXggKyB0aGlzLmFjdGl2ZU5hbWUyXVswXS5nZXRFcXVpcG1lbnRNb2RhbCkgew0KICAgICAgICAgICAgICAgIHRoaXMuJHJlZnNbaW5kZXggKyB0aGlzLmFjdGl2ZU5hbWUyXVswXS5nZXRFcXVpcG1lbnRNb2RhbChpdGVtLCB0aGlzLkFjdGl2ZUxpc3RbMF0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgaWYgKHRoaXMuJHJlZnNbaW5kZXggKyB0aGlzLmFjdGl2ZU5hbWUyXVswXS50YWJCZUNsaWNrKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kcmVmc1tpbmRleCArIHRoaXMuYWN0aXZlTmFtZTJdWzBdLnRhYkJlQ2xpY2soaXRlbSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLkFjdGl2ZUxpc3QpOw0KICAgICAgICB9LA0KICAgICAgICBhc3luYyBoYW5kbGVDbGljayhrZXkpIHsNCiAgICAgICAgICAgIGlmIChrZXkgJiYgTnVtYmVyKGtleS5pbmRleCkgPT0gMSkgew0KICAgICAgICAgICAgICAgIHRoaXMuR2V0UHJvY2Vzc09yZGVyVmlldzIoKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMuRXF1aXBtZW50bGlzdCk7DQogICAgICAgICAgICAvLyB0aGlzLkdldEVxdWlwbWVudCgpOw0KICAgICAgICAgICAgdGhpcy5FcXVpcG1lbnRJZCA9IHRoaXMuYWN0aXZlTmFtZTsNCiAgICAgICAgICAgIGxldCBwYXJhbXMgPSB7DQogICAgICAgICAgICAgICAgRXF1aXBtZW50R3JvdXBSb3dJZDogdGhpcy5FcXVpcG1lbnRHcm91cFJvd0lkLA0KICAgICAgICAgICAgICAgIFJ1bkVxdWlwbWVudElkOiB0aGlzLkVxdWlwbWVudElkLA0KICAgICAgICAgICAgICAgIHBhZ2VJbmRleDogdGhpcy5wYWdlT3B0aW9ucy5wYWdlLA0KICAgICAgICAgICAgICAgIHBhZ2VTaXplOiB0aGlzLnBhZ2VPcHRpb25zLnBhZ2VTaXplDQogICAgICAgICAgICB9Ow0KICAgICAgICAgICAgbGV0IHJlczIgPSBhd2FpdCBnZXRDaGVja1RpcHBpbmdUeXBlKEpTT04uc3RyaW5naWZ5KHRoaXMuRXF1aXBtZW50SWQpKTsNCiAgICAgICAgICAgIGlmIChyZXMyLnJlc3BvbnNlID09ICd0cnVlJykgew0KICAgICAgICAgICAgICAgIHRoaXMuaXNUaXBwaW5nc2NhbiA9IHRydWU7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHRoaXMuaXNUaXBwaW5nc2NhbiA9IGZhbHNlOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgY29uc29sZS5sb2coJ2hhbmRsZUNsaWNrJyk7DQogICAgICAgICAgICBsZXQgcmVzID0gYXdhaXQgR2V0RXF1aXBtZW50UHJvY2Vzc09yZGVyVmlldyhwYXJhbXMpOw0KICAgICAgICAgICAgaWYgKHJlcy5yZXNwb25zZS5kYXRhLmxlbmd0aCAhPSAwKSB7DQogICAgICAgICAgICAgICAgbGV0IGxpc3QgPSByZXMucmVzcG9uc2UuZGF0YTsNCiAgICAgICAgICAgICAgICBsaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICAgICAgICAgIGl0ZW0uTWF0ZXJpYWwgPSBpdGVtLk1hdGVyaWFsTmFtZSArICctJyArIGl0ZW0uTWF0ZXJpYWxDb2RlOw0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgIHRoaXMuQWN0aXZlTGlzdCA9IGxpc3Q7DQogICAgICAgICAgICAgICAgdGhpcy5CYXRjaElkID0gbGlzdFswXS5CYXRjaElkOw0KICAgICAgICAgICAgICAgIHRoaXMuUnVuRXF1aXBtZW50SWQgPSBsaXN0WzBdLlJ1bkVxdWlwbWVudElkOw0KICAgICAgICAgICAgICAgIHRoaXMuRXhlY3V0aW9uSWQgPSBsaXN0WzBdLkV4ZWN1dGlvbklkOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICB0aGlzLkFjdGl2ZUxpc3QgPSBbXTsNCiAgICAgICAgICAgICAgICB0aGlzLkJhdGNoSWQgPSAnJzsNCiAgICAgICAgICAgICAgICB0aGlzLlJ1bkVxdWlwbWVudElkID0gJyc7DQogICAgICAgICAgICAgICAgdGhpcy5FeGVjdXRpb25JZCA9ICcnOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgaWYgKGtleSkgew0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKE51bWJlcihrZXkuaW5kZXgpKTsNCiAgICAgICAgICAgICAgICBsZXQgbnVtID0gbnVsbDsNCiAgICAgICAgICAgICAgICBpZiAodGhpcy5TaG93UE9MaXN0KSB7DQogICAgICAgICAgICAgICAgICAgIG51bSA9IE51bWJlcihrZXkuaW5kZXgpIC0gMjsNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICBudW0gPSBOdW1iZXIoa2V5LmluZGV4KSAtIDE7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGlmIChudW0gPj0gMCkgew0KICAgICAgICAgICAgICAgICAgICBsZXQgZnVuY3Rpb25saXN0ID0gdGhpcy5FcXVpcG1lbnRsaXN0W251bV0uZnVuY3Rpb25saXN0Ow0KICAgICAgICAgICAgICAgICAgICAvL2NvbnNvbGUubG9nKHRoaXMuRXF1aXBtZW50bGlzdFtudW1dKTsNCiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coZnVuY3Rpb25saXN0KTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5hY3RpdmVOYW1lMiA9IGZ1bmN0aW9ubGlzdFswXTsNCiAgICAgICAgICAgICAgICAgICAgZnVuY3Rpb25saXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICBpZiAoaXRlbSA9PSAnUE9NYW5hZ2VtZW50Jykgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuYWN0aXZlTmFtZTIgPSAnUE9NYW5hZ2VtZW50JzsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLiRyZWZzW251bSArIHRoaXMuYWN0aXZlTmFtZTJdWzBdLmdldEVxdWlwbWVudE1vZGFsKSB7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRyZWZzW251bSArIHRoaXMuYWN0aXZlTmFtZTJdWzBdLmdldEVxdWlwbWVudE1vZGFsKHRoaXMuRXF1aXBtZW50bGlzdFtudW1dLCB0aGlzLkFjdGl2ZUxpc3RbMF0pOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBoYW5kbGVTaXplQ2hhbmdlKHZhbCkgew0KICAgICAgICAgICAgdGhpcy5wYWdlT3B0aW9ucy5wYWdlU2l6ZSA9IHZhbDsNCiAgICAgICAgICAgIHRoaXMuR2V0UHJvY2Vzc09yZGVyVmlld0xpc3QoKTsNCiAgICAgICAgfSwNCiAgICAgICAgaGFuZGxlQ3VycmVudENoYW5nZSh2YWwpIHsNCiAgICAgICAgICAgIHRoaXMucGFnZU9wdGlvbnMucGFnZSA9IHZhbDsNCiAgICAgICAgICAgIHRoaXMuR2V0UHJvY2Vzc09yZGVyVmlld0xpc3QoKTsNCiAgICAgICAgfSwNCiAgICAgICAgaGFuZGxlU2l6ZUNoYW5nZTIodmFsKSB7DQogICAgICAgICAgICB0aGlzLnBhZ2VPcHRpb25zMi5wYWdlU2l6ZSA9IHZhbDsNCiAgICAgICAgICAgIHRoaXMuR2V0UHJvY2Vzc09yZGVyVmlldzIoKTsNCiAgICAgICAgfSwNCiAgICAgICAgaGFuZGxlQ3VycmVudENoYW5nZTIodmFsKSB7DQogICAgICAgICAgICB0aGlzLnBhZ2VPcHRpb25zMi5wYWdlID0gdmFsOw0KICAgICAgICAgICAgdGhpcy5HZXRQcm9jZXNzT3JkZXJWaWV3MigpOw0KICAgICAgICB9DQogICAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/Producting/Overview", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle overview\">\r\n        <div class=\"InventorySearchBox\">\r\n            <div class=\"searchbox\">\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-back\" @click=\"back()\">{{ this.$t('Overview.Back') }}</el-button>\r\n                <div class=\"searchboxtitle\">\r\n                    {{ viewtitle }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <el-tabs v-model=\"activeName\" type=\"border-card\" @tab-click=\"handleClick\">\r\n            <el-tab-pane :label=\"$t('Overview.Overview')\" name=\"1\">\r\n                <div class=\"tablebox\">\r\n                    <el-table :data=\"tableList\" style=\"width: 100%\" height=\"700\">\r\n                        <el-table-column\r\n                            v-for=\"(item, index) in header\"\r\n                            :key=\"index\"\r\n                            :align=\"item.align\"\r\n                            :prop=\"item.prop ? item.prop : item.value\"\r\n                            :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                            :width=\"item.width\"\r\n                        >\r\n                            <template slot-scope=\"scope\">\r\n                                <span v-if=\"scope.row.ProcessOrder && scope.column.property == 'Complete'\">\r\n                                    <el-progress\r\n                                        :text-inside=\"true\"\r\n                                        color=\"#3dcd58\"\r\n                                        text-color=\"#000000\"\r\n                                        :stroke-width=\"26\"\r\n                                        :percentage=\"Number(((scope.row.Total / scope.row.TargetQuantity) * 100).toFixed(2))\"\r\n                                    ></el-progress>\r\n                                </span>\r\n                                <span v-else-if=\"scope.column.property == 'PlantNode'\">\r\n                                    <div>{{ scope.row.EquipmentCode }}</div>\r\n                                    <div style=\"color: #808080\">{{ scope.row.EquipmentName }}</div>\r\n                                </span>\r\n                                <span v-else-if=\"scope.column.property == 'Material'\">\r\n                                    <div>{{ scope.row.MaterialCode }}</div>\r\n                                    <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                                </span>\r\n                                <span v-else-if=\"scope.column.property == 'BatchQty'\">{{ scope.row.BatchQty }}{{ scope.row.Unit1 }}</span>\r\n                                <span v-else-if=\"scope.column.property == 'Sequence'\">{{ scope.row.ProcessOrder != null ? scope.row.Sequence : '' }}</span>\r\n                                <span v-else>{{ scope.row[item.prop] }}</span>\r\n                            </template>\r\n                        </el-table-column>\r\n                    </el-table>\r\n                    <div class=\"paginationbox\">\r\n                        <el-pagination\r\n                            @size-change=\"handleSizeChange\"\r\n                            @current-change=\"handleCurrentChange\"\r\n                            :current-page=\"pageOptions.page\"\r\n                            :page-sizes=\"pageOptions.pageSizeitems\"\r\n                            :page-size=\"pageOptions.pageSize\"\r\n                            layout=\"total, sizes, prev, pager, next\"\r\n                            :total=\"pageOptions.total\"\r\n                            background\r\n                        ></el-pagination>\r\n                    </div>\r\n                </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane v-if=\"ShowPOList\" :label=\"$t('Overview.POList')\" name=\"2\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"datebox\">\r\n                            <div class=\"datepickbox\">\r\n                                <el-date-picker\r\n                                    v-model=\"timepicker\"\r\n                                    type=\"daterange\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                                    range-separator=\"-\"\r\n                                    :start-placeholder=\"$t('DFM_RL._KSRQ')\"\r\n                                    :end-placeholder=\"$t('DFM_RL._JSRQ')\"\r\n                                ></el-date-picker>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"inputformbox\" :style=\"{ width: item.width }\" v-for=\"(item, index) in searchlist\" :key=\"index\">\r\n                            <el-input v-if=\"item.type == 'input'\" v-model=\"item.value\" :myid=\"item.id\" :placeholder=\"item.name\"></el-input>\r\n                            <el-select :style=\"{ width: item.width }\" v-model=\"item.value\" v-if=\"item.type == 'select'\" :myid=\"item.id\" :placeholder=\"item.name\">\r\n                                <el-option v-for=\"(it, ind) in item.option\" :key=\"ind\" :label=\"it.value\" :value=\"it.key\"></el-option>\r\n                            </el-select>\r\n                        </div>\r\n                        <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"getsearch()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                        <el-button size=\"small\" style=\"margin-left: 5px\" icon=\"el-icon-s-help\" @click=\"getempty()\">{{ this.$t('GLOBAL._CZ') }}</el-button>\r\n                    </div>\r\n                </div>\r\n                <div class=\"tablebox\">\r\n                    <el-table :data=\"AvailablePOManagemenList\" style=\"width: 100%\" height=\"670\">\r\n                        <el-table-column\r\n                            v-for=\"(item, index) in Availableheader\"\r\n                            :key=\"index\"\r\n                            :align=\"item.align\"\r\n                            :prop=\"item.prop ? item.prop : item.value\"\r\n                            :label=\"$t(`$vuetify.dataTable.${AvailabletableId}.${item.value}`)\"\r\n                            :width=\"item.width\"\r\n                        >\r\n                            <template slot-scope=\"scope\">\r\n                                <!-- <span v-if=\"scope.column.property == 'operate'\">\r\n                                    <el-button size=\"mini\" class=\"operatebtn\" v-if=\"scope.row.Status > 1 && scope.row.RunningCount == 0\" @click=\"startOrder(scope)\" icon=\"el-icon-video-play\">\r\n                                        {{ $t('Overview.start') }}\r\n                                    </el-button>\r\n                                </span> -->\r\n                                <span v-if=\"scope.column.property == 'PlanStartTime'\">{{ $dayjs(scope.row.PlanStartTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                                <span v-else-if=\"scope.column.property == 'PlanEndTime'\">{{ $dayjs(scope.row.PlanEndTime).format('YYYY-MM-DD HH:mm') }}</span>\r\n                                <span v-else-if=\"scope.column.property == 'IsHavePreservative'\">\r\n                                    <i :class=\"scope.row[item.value] === '1' ? 'el-icon-star-on' : ''\"></i>\r\n                                </span>\r\n                                <span v-else-if=\"scope.column.property == 'LineNominalSpeed'\">{{ scope.row.Speed }}{{ scope.row.SpeedUom }}</span>\r\n                                <span v-else>{{ scope.row[item.prop] }}</span>\r\n                            </template>\r\n                        </el-table-column>\r\n                    </el-table>\r\n                    <div class=\"paginationbox\">\r\n                        <el-pagination\r\n                            @size-change=\"handleSizeChange2\"\r\n                            @current-change=\"handleCurrentChange2\"\r\n                            :current-page=\"pageOptions2.page\"\r\n                            :page-sizes=\"pageOptions2.pageSizeitems\"\r\n                            :page-size=\"pageOptions2.pageSize\"\r\n                            layout=\"total, sizes, prev, pager, next\"\r\n                            :total=\"pageOptions2.total\"\r\n                            background\r\n                        ></el-pagination>\r\n                    </div>\r\n                    <el-dialog :title=\"$t('Overview.StartOrder')\" id=\"Startdialog\" :visible.sync=\"StartModel\" :width=\"IsPack == '0' ? '1050px' : '650px'\">\r\n                        <span slot=\"title\" class=\"dialog-title\">\r\n                            <div class=\"dialogtitlebox\">\r\n                                {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.StartOrder') }}\r\n                                <div class=\"dialogsubtitlebox\" style=\"display: inline\">{{ chooseItem.ProcessOrder }}</div>\r\n                            </div>\r\n                        </span>\r\n                        <div class=\"splitdetailbox\">\r\n                            <div class=\"splitdetailboxtitle\">{{ chooseItem.MaterialCode }}-{{ chooseItem.MaterialName }}</div>\r\n                            <div class=\"detailsnote\" v-if=\"runningCode != '' && !chooseItem.isResume\">\r\n                                {{ $t('Overview.Note1') }}\r\n                                <span style=\"font-weight: 600\">{{ runningCode }}</span>\r\n                                {{ $t('Overview.Note2') }}\r\n                            </div>\r\n                            <div style=\"display: flex\">\r\n                                <div :style=\"{ width: IsPack == '0' ? '100%' : '100%' }\">\r\n                                    <div class=\"dialogdetailbox\">\r\n                                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: '10%' }\">{{ $t('Overview.ChooseEquipment') + ' *' }}</div>\r\n                                        <div class=\"dialogdetailsinglevalue \" :style=\"{ width: '87%' }\">\r\n                                            <el-select style=\"width: 92%\" v-model=\"MyEquipment\" @change=\"getMyEquipment()\" filterable>\r\n                                                <el-option v-for=\"(it, index) in MyEquipmentList\" :key=\"index\" :label=\"it.EquipmentName\" :value=\"it.ID\"></el-option>\r\n                                            </el-select>\r\n                                        </div>\r\n                                    </div>\r\n                                    <div class=\"dialogdetailbox\" v-for=\"(item, index) in Startlist\" :key=\"index\">\r\n                                        <div class=\"dialogdetailsinglelabel\" :style=\"{ width: item.type == 'BatchCode' ? '10%' : '10%' }\">{{ item.label }}{{ item.require ? ' *' : '' }}</div>\r\n                                        <div class=\"dialogdetailsinglevalue \" :style=\"{ width: item.type == 'BatchCode' || item.type == 'checkBox' ? '80%' : '80%' }\">\r\n                                            <el-input style=\"width: 100%\" v-if=\"item.type == 'input'\" v-model=\"item.value\"></el-input>\r\n                                            <!-- <div class=\"detailsnote2\" v-else-if=\"item.type == 'checkBox' && IsDifferent === true && IsPack === '0'\">\r\n                                                {{ $t('Overview.IsUpdateLtxt') }}\r\n                                            </div>\r\n                                            <div class=\"detailsnote3\" v-else-if=\"item.type == 'checkBox' && IsDifferent === false && IsPack === '0'\">\r\n                                                {{ $t('Overview.TextGood') }}\r\n                                            </div> -->\r\n                                            <!-- <div v-else-if=\"item.type == 'BatchCode'\" style=\"display: flex\">\r\n                                                <el-input v-model=\"item.value\"></el-input>\r\n                                                <el-input v-model=\"item.value2\" disabled></el-input>\r\n                                                <el-input v-model=\"item.value3\"></el-input>\r\n                                                <el-button\r\n                                                    class=\"tablebtn\"\r\n                                                    @click=\"getBatchCode()\"\r\n                                                    size=\"mini\"\r\n                                                    style=\"margin-left: 5px; width: 5vh; background: #3dcd58; color: #fff\"\r\n                                                    icon=\"el-icon-refresh\"\r\n                                                ></el-button>\r\n                                            </div> -->\r\n                                            <el-select style=\"width: 100%\" clearable v-else-if=\"item.type == 'select'\" v-model=\"item.value\" filterable>\r\n                                                <el-option v-for=\"it in item.option\" :key=\"it.ID\" :label=\"it.Number\" :value=\"it.ID\"></el-option>\r\n                                            </el-select>\r\n                                            <el-date-picker\r\n                                                @change=\"GetDate(item.id)\"\r\n                                                v-else-if=\"item.type == 'date'\"\r\n                                                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                                                :disabled=\"item.disabled\"\r\n                                                v-model=\"item.value\"\r\n                                                type=\"datetime\"\r\n                                                style=\"width: 100%\"\r\n                                            ></el-date-picker>\r\n                                            <span v-else-if=\"item.id == 'TargetQuantity'\">{{ chooseItem.TargetQuantity }}{{ chooseItem.Unit1 }}</span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n<!--                                <div v-if=\"IsPack == '0'\" style=\"height: 100%; margin-top: 10px; padding: 0 10px; box-shadow: none; display: flex; flex-direction: column\">-->\r\n\r\n<!--                                    <span class=\"dialogsubtitlebox\" style=\"margin-bottom: 5px\">工单长文本：</span>-->\r\n<!--                                    <el-input type=\"textarea\" :autosize=\"{ minRows: 5 }\" style=\"width: 370px !important\" :disabled=\"isEdit == false\" v-model=\"Text1\"></el-input>-->\r\n<!--                                    <span class=\"dialogsubtitlebox\" style=\"margin: 5px 0\">配方长文本：</span>-->\r\n<!--                                    <el-input type=\"textarea\" :autosize=\"{ minRows: 5 }\" style=\"width: 370px !important\" :disabled=\"isEdit == false\" v-model=\"Text2\"></el-input>-->\r\n<!--                                </div>-->\r\n                            </div>\r\n                          <div style=\"padding: 18px 18px 0 18px\">\r\n                            <el-row>\r\n                              <el-col :span=\"12\">\r\n                                <span class=\"font-M09 bold\">工单长文本</span>\r\n                              </el-col>\r\n                              <el-col :span=\"12\">\r\n                                <span class=\"font-M09 bold\">配方长文本</span>\r\n                              </el-col>\r\n                            </el-row>\r\n                            <CodeDiff\r\n                                hideHeader\r\n                                :old-string=\"Text1\"\r\n                                :new-string=\"Text2\"\r\n                                output-format=\"side-by-side\"\r\n                            />\r\n                          </div>\r\n\r\n                        </div>\r\n                        <span slot=\"footer\" class=\"dialog-footer\">\r\n                            <el-button style=\"float: left\" v-if=\"chooseItem.isResume\">\r\n                                {{ $t('Overview.bottleneck') }}\r\n                            </el-button>\r\n                            <el-button class=\"tablebtn\" icon=\"el-icon-video-play\" @click=\"ProducedStart()\">\r\n                                {{ chooseItem.isResume ? $t('Overview.Resume') : $t('Overview.Start') }}\r\n                            </el-button>\r\n                            <el-button @click=\"StartModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n                        </span>\r\n                    </el-dialog>\r\n                </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane v-for=\"(item, index) in Equipmentlist\" :key=\"index\" :disabled=\"item.FunctionCodes != '' ? false : true\" :name=\"item.ID\">\r\n                <span slot=\"label\">\r\n                    <i :class=\"item.FunctionCodes != '' ? 'el-icon-s-tools' : 'el-icon-s-grid'\"></i>\r\n                    <span>{{ item.EquipmentName }}</span>\r\n                    <div v-if=\"item.FunctionCodes != ''\" class=\"tabiconbox\">\r\n                        <span v-if=\"item.FunctionCodes.indexOf('POManagement') != -1\">\r\n                            <i\r\n                                :style=\"{ color: item.ProductionOrderId == null ? 'red' : '#3DCD58' }\"\r\n                                :class=\"item.ProductionOrderId == null ? 'iconfont icon-pausecircle-fill' : 'iconfont icon-play-fill'\"\r\n                            ></i>\r\n                        </span>\r\n                        <!-- <i class=\"el-icon-s-marketing\"></i> -->\r\n                    </div>\r\n                </span>\r\n                <div class=\"subtabs\">\r\n                    <div class=\"activeTitle\" v-if=\"ActiveList.length == 0\">{{ $t('Overview.NoActiveProcessOrder') }}</div>\r\n                    <div class=\"activeTitle\" v-else>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ ActiveList[0].ProcessOrder }}({{ ActiveList[0].Number }})</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].Material }}</div>\r\n                        </div>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ ActiveList[0].TargetQuantity }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].Unit1 }}</div>\r\n                        </div>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ ActiveList[0].Speed }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].SpeedUom }}</div>\r\n                        </div>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ $t('Overview.BatchCode') }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].BatchCode }}</div>\r\n                        </div>\r\n                        <div class=\"activeBox\">\r\n                            <div class=\"activeLabel\">{{ $t('Overview.ExpirationDate') }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].ExpirationDate }}</div>\r\n                        </div>\r\n                               <div class=\"activeBox\" v-if=\"ActiveList[0].StorageTank != ''&&ActiveList[0].StorageTank != null\">\r\n                            <div class=\"activeLabel\">{{ $t('Overview.CGBM') }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].StorageTank }}</div>\r\n                        </div>\r\n                               <div class=\"activeBox\" v-if=\"ActiveList[0].StorageTankOrderGc != ''&&ActiveList[0].StorageTankOrderGc != null\">\r\n                            <div class=\"activeLabel\">{{ $t('Overview.GC') }}</div>\r\n                            <div class=\"activeValue\">{{ ActiveList[0].StorageTankOrderGc }}</div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"subtabsbox\">\r\n                        <el-tabs v-model=\"activeName2\" type=\"border-card\" @tab-click=\"handleClick2(item, index)\">\r\n                            <!-- v-if=\"item.functionlist.includes('PO Management') != -1\" -->\r\n                            <!-- <el-tab-pane :label=\"$t('Overview.POManagement')\" name=\"1\">\r\n                                <POManagement ref=\"POManagement\" @getNum=\"getNum\" :EquipmentId=\"EquipmentId\"></POManagement>\r\n                            </el-tab-pane> -->\r\n                            <el-tab-pane :label=\"$t(`Overview.${it.trim()}`)\" v-for=\"(it, ind) in item.functionlist\" :key=\"ind\" :name=\"it.trim()\">\r\n                                <div>\r\n                                    <component\r\n                                        @loadProgress=\"loadProgress\"\r\n                                        :is=\"it.trim() == 'Tipping' ? (isTippingscan ? 'Tippingscan' : 'Tipping') : it.trim()\"\r\n                                        :ref=\"index + it.trim()\"\r\n                                        :BatchId=\"BatchId\"\r\n                                        :EquipmentName=\"item.EquipmentName\"\r\n                                        :ExecutionId=\"ExecutionId\"\r\n                                        :EquipmentId=\"EquipmentId\"\r\n                                        :RunEquipmentId=\"RunEquipmentId\"\r\n                                    ></component>\r\n                                </div>\r\n                            </el-tab-pane>\r\n                            <!-- <el-tab-pane label=\"SampleWeighing\" name=\"SampleWeighing\">\r\n                                <SampleWeighing :ref=\"index + 'SampleWeighing'\" :EquipmentId=\"EquipmentId\"></SampleWeighing>\r\n                            </el-tab-pane> -->\r\n                            <!-- <el-tab-pane label=\"ParameterDownload\" name=\"ParameterDownload\">\r\n                                <ParameterDownload :ref=\"index + 'ParameterDownload'\" :EquipmentId=\"EquipmentId\"></ParameterDownload>\r\n                            </el-tab-pane> -->\r\n                            <!--\r\n                            <el-tab-pane label=\"Performance\" name=\"Performance\">\r\n                                <Performance :ref=\"index + 'Performance'\" :EquipmentId=\"EquipmentId\"></Performance>\r\n                            </el-tab-pane> -->\r\n                            <!-- <el-tab-pane label=\"Tipping\" name=\"Tipping\">\r\n                                <Tipping :ref=\"index + 'Tipping'\"></Tipping>\r\n                            </el-tab-pane> -->\r\n                            <!-- <el-tab-pane label=\"Logsheets\" name=\"Logsheets\">\r\n                                <Logsheets :ref=\"index + 'Logsheets'\"></Logsheets>\r\n                            </el-tab-pane> -->\r\n                        </el-tabs>\r\n                    </div>\r\n                </div>\r\n            </el-tab-pane>\r\n        </el-tabs>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { POManagemenOverview } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetProcessOrderViewSegments,\r\n    GetProcessOrderView,\r\n    GetEquipmentFunctionView,\r\n    GetEquipmentProcessOrderView,\r\n    getCheckTippingType,\r\n    GetCookieOrderLtexts,\r\n    GetBBatchListView,\r\n    GetBatchCode,\r\n    PoProducedStart,\r\n    PoProducedResume,\r\n    GetRunOrder,\r\n    GetCookOrderLtexts,\r\n    GetProcessOrderViewSegmentUnits,\r\n    GetPoList\r\n} from '@/api/Inventory/Overview.js';\r\nimport { POManagemenPoList } from '@/columns/factoryPlant/tableHeaders';\r\nimport moment from 'moment';\r\n\r\nimport { Message } from 'element-ui';\r\nimport { CodeDiff } from 'v-code-diff'\r\nexport default {\r\n    components: {\r\n        ParameterDownload: () => import('./components/ParameterDownload'),\r\n        POManagement: () => import('./components/POManagement'),\r\n        Consume: () => import('./components/Consume'),\r\n        Produce: () => import('./components/Produce'),\r\n        Tipping: () => import('./components/Tipping'),\r\n        Storage: () => import('./components/Storage'),\r\n                Performance: () => import('./components/Performance'),\r\n        MaterialPrep: () => import('./components/MaterialPrep'),\r\n        PerformanceEvents: () => import('./components/Performance'),\r\n        Logsheets: () => import('./components/Logsheets'),\r\n        Tippingscan: () => import('./components/Tippingscan'),\r\n        SampleWeighing: () => import('./components/SampleWeighing'),\r\n        ProcessText: () => import('./components/Processlongtext'),\r\n      CodeDiff\r\n    },\r\n    data() {\r\n        return {\r\n            viewtitle: '',\r\n            timepicker: [],\r\n            AvailablePOManagemenList: [],\r\n            Availableheader: POManagemenPoList,\r\n            StartModel: false,\r\n            MyEquipment: '',\r\n            ShowPOList: false,\r\n            MyEquipmentList: [],\r\n            Startlist: [\r\n                {\r\n                    label: this.$t('Overview.StartTime'),\r\n                    id: 'StartTime',\r\n                    value: '',\r\n                    disabled: true,\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.Batch'),\r\n                    id: 'BatchId',\r\n                    value: '',\r\n                    require: true,\r\n                    type: 'select',\r\n                    option: []\r\n                },\r\n                {\r\n                    label: this.$t('Overview.BatchCode'),\r\n                    id: 'LotCode',\r\n                    value: '',\r\n                    value2: '',\r\n                    value3: '',\r\n                    require: true,\r\n                    type: 'BatchCode'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ProductionDate'),\r\n                    id: 'ProductionDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date'\r\n                },\r\n                {\r\n                    label: this.$t('Overview.ExpirationDate'),\r\n                    id: 'ExpirationDate',\r\n                    require: true,\r\n                    value: '',\r\n                    type: 'date',\r\n                    disabled: true\r\n                },\r\n                {\r\n                    label: this.$t('Overview.TargetQuantity'),\r\n                    id: 'TargetQuantity',\r\n                    value: ''\r\n                },\r\n                {\r\n                    label: this.$t('Overview.CrewSize'),\r\n                    id: 'CrewSize',\r\n                    value: '',\r\n                    type: 'input'\r\n                },\r\n                {\r\n                    label: '',\r\n                    id: 'IsUpdateLtxt',\r\n                    value: false,\r\n                    type: 'checkBox'\r\n                }\r\n            ],\r\n            searchlist: [\r\n                {\r\n                    type: 'input',\r\n                    name: this.$t('Overview.QuickSearch'),\r\n                    id: 'QuickSearch',\r\n                    value: ''\r\n                },\r\n                {\r\n                    type: 'select',\r\n                    option: [],\r\n                    width: '20vh',\r\n                    name: this.$t('DFM_JYXM.Segment'),\r\n                    id: 'Segment',\r\n                    value: ''\r\n                },\r\n                {\r\n                    type: 'input',\r\n                    value: '',\r\n                    id: 'FillLineCode',\r\n                    name: this.$t('$vuetify.dataTable.PRO_POLIST.FillLineCode')\r\n                }\r\n            ],\r\n            chooseItem: {\r\n                ProcessOrder: '',\r\n                isResume: false,\r\n                TargetQuantity: '',\r\n                Unit1: '',\r\n                MaterialCode: '',\r\n                MaterialName: ''\r\n            },\r\n            tableList: [],\r\n            Equipmentlist: [],\r\n            AvailabletableId: 'PRO_POManagement',\r\n            tableId: 'PRO_Overview',\r\n            header: POManagemenOverview,\r\n            pageOptions: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            pageOptions2: {\r\n                total: 0,\r\n                page: 1, // 当前页码\r\n                pageSize: 20, // 一页数据\r\n                pageCount: 1, // 页码分页数\r\n                pageSizeitems: [10, 20, 50, 100, 500]\r\n            },\r\n            activeName: '1',\r\n            activeName2: '',\r\n            ActiveList: [],\r\n            EquipmentGroupRowId: '',\r\n            EquipmentId: '',\r\n            RunEquipmentId: '',\r\n            BatchId: '',\r\n            ExecutionId: '',\r\n            runningCode: '',\r\n            isTippingscan: false,\r\n            productionId: '',\r\n            isEdit: false,\r\n            IsDifferent: false,\r\n            EquipmentCode: '',\r\n            IsPack: '1',\r\n            Text1: '',\r\n            Text2: ''\r\n        };\r\n    },\r\n    mounted() {\r\n        this.viewtitle = JSON.parse(this.$route.query.query).Description;\r\n        console.log(this.viewtitle);\r\n        this.EquipmentGroupRowId = JSON.parse(this.$route.query.query).ID;\r\n        // this.FiltersTableData();\r\n        this.GetProcessOrderViewList();\r\n        //this.GetProcessOrderView2();\r\n        this.GetSegment();\r\n        this.GetEquipment();\r\n        this.changePagination();\r\n    },\r\n    methods: {\r\n        async ProducedStart() {\r\n            let flag = this.Startlist.some(item => {\r\n                if (item.require) {\r\n                    return item.value == '';\r\n                }\r\n            });\r\n            if (this.MyEquipment == '') {\r\n                flag = true;\r\n            }\r\n            if (flag) {\r\n                Message({\r\n                    message: `${this.$t('Inventory.ToOver')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let params = {\r\n                SegmentId: this.chooseItem.SegmentId,\r\n                ProductionOrderId: this.chooseItem.ProductionOrderId,\r\n                PoSegmentRequirementId: this.chooseItem.ID,\r\n                BatchId: '',\r\n                LotCode: '',\r\n                EquipmentId: this.MyEquipment,\r\n                StartTime: '',\r\n                ProductionDate: '',\r\n                ExpirationDate: ''\r\n            };\r\n            if (this.chooseItem.isResume == true) {\r\n                params.ExecutionId = this.chooseItem.ExecutionId;\r\n            } else {\r\n                params.ExecutionId = '';\r\n            }\r\n            this.Startlist.forEach(item => {\r\n                if (item.id == 'LotCode') {\r\n                    params[item.id] = item.value + item.value2 + item.value3;\r\n                } else {\r\n                    params[item.id] = item.value;\r\n                }\r\n            });\r\n            params.ExpirationDate = moment(params.ExpirationDate).format('YYYY-MM-DD HH:mm:ss');\r\n            if (params.LotCode.length > 10) {\r\n                Message({\r\n                    message: `${this.$t('Overview.BatchCodeLong')}`,\r\n                    type: 'warning'\r\n                });\r\n                return;\r\n            }\r\n            let res;\r\n            if (this.chooseItem.isResume == true) {\r\n                res = await PoProducedResume(params);\r\n            } else {\r\n                res = await PoProducedStart(params);\r\n            }\r\n            //this.GetProcessOrderView2();\r\n            this.loadProgress();\r\n            this.StartModel = false;\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n        },\r\n        GetDate(id) {\r\n            if (id == 'ProductionDate') {\r\n                if (this.chooseItem.NeedQARelease == '1') {\r\n                    this.Startlist[4].value = this.addDays(this.Startlist[3].value, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);\r\n                } else {\r\n                    this.Startlist[4].value = this.addDays(this.chooseItem.PlanStartTime, this.chooseItem.Mhdhb, this.chooseItem.Iprkz);\r\n                }\r\n            }\r\n        },\r\n        startOrder(item) {\r\n            // this.MyEquipment = '';\r\n            // this.EquipmentCode = '';\r\n            this.SegmentUnits(item);\r\n            console.log(item.row);\r\n            if (item.row) {\r\n                this.IsPack = item.row.NeedQARelease;\r\n                this.getLtext(item.row.ProductionOrderId);\r\n                this.chooseItem = item.row;\r\n                this.chooseItem.isResume = false;\r\n            } else {\r\n                this.chooseItem = item;\r\n                this.chooseItem.isResume = true;\r\n            }\r\n            this.Startlist.forEach((item, index) => {\r\n                if (index == 0) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 3) {\r\n                    item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');\r\n                } else if (index == 2) {\r\n                    item.value = '';\r\n                    item.value2 = '';\r\n                    item.value3 = '';\r\n                } else if (item.id == 'IsUpdateLtxt') {\r\n                    item.value = false;\r\n                } else {\r\n                    item.value = '';\r\n                }\r\n            });\r\n            this.GetDate('ProductionDate');\r\n            this.getBatchList();\r\n        },\r\n        async SegmentUnits(item) {\r\n            let res = await GetProcessOrderViewSegmentUnits('', item.row.Segment);\r\n            this.MyEquipmentList = res.response;\r\n        },\r\n        async getBatchCode() {\r\n            let date = moment(this.Startlist[3].value).format('YYYY-MM-DD HH:mm:ss');\r\n            let p = {\r\n                LineCode: this.Startlist[2].value,\r\n                equipmentCode: this.EquipmentCode,\r\n                productionDate: date,\r\n                productionId: this.chooseItem.ProductionOrderId\r\n            };\r\n            let res = await GetBatchCode(p);\r\n            if (res.response == null) {\r\n                Message({\r\n                    message: res.msg,\r\n                    type: 'warning'\r\n                });\r\n            } else {\r\n                this.Startlist[2].value = res.response.substring(0, 2);\r\n                this.Startlist[2].value2 = res.response.substring(2, 5);\r\n            }\r\n        },\r\n        async getBatchList() {\r\n            let params = {\r\n                PoSegmentRequirementId: this.chooseItem.ID\r\n            };\r\n            let res = await GetBBatchListView(params);\r\n            this.Startlist[1].option = res.response;\r\n            this.Startlist[1].value = this.Startlist[1].option[0].ID;\r\n            this.StartModel = true;\r\n        },\r\n        addDays(date, number, interval) {\r\n            const newDate1 = new Date(date);\r\n            const newDate = new Date(newDate1.getFullYear(), newDate1.getMonth(), newDate1.getDate());\r\n            this.DateAdd(interval, number, newDate);\r\n            newDate.setDate(newDate.getDate() + 1); // 增加一天\r\n            newDate.setSeconds(newDate.getSeconds() - 1); // 减去1秒\r\n            return newDate;\r\n        },\r\n        DateAdd(interval, number, date) {\r\n            switch (interval) {\r\n                case 'Y': {\r\n                    date.setFullYear(date.getFullYear() + number);\r\n                    return date;\r\n                }\r\n                case 'Q': {\r\n                    date.setMonth(date.getMonth() + number * 3);\r\n                    return date;\r\n                }\r\n                case 'M': {\r\n                    date.setMonth(date.getMonth() + number);\r\n                    return date;\r\n                }\r\n                case 'W': {\r\n                    date.setDate(date.getDate() + number * 7);\r\n                    return date;\r\n                }\r\n                case 'D': {\r\n                    date.setDate(date.getDate() + number);\r\n                    return date;\r\n                }\r\n                case 'h': {\r\n                    date.setHours(date.getHours() + number);\r\n                    return date;\r\n                }\r\n                case 'm': {\r\n                    date.setMinutes(date.getMinutes() + number);\r\n                    return date;\r\n                }\r\n                case 's': {\r\n                    date.setSeconds(date.getSeconds() + number);\r\n                    return date;\r\n                }\r\n                default: {\r\n                    date.setDate(date.getDate() + number);\r\n                    return date;\r\n                }\r\n            }\r\n        },\r\n        async getMyEquipment() {\r\n            this.MyGetRunOrder();\r\n            this.MyEquipmentList.forEach(item => {\r\n                if (item.ID == this.MyEquipment) {\r\n                    this.EquipmentCode = item.EquipmentCode;\r\n                }\r\n            });\r\n            this.getBatchCode();\r\n        },\r\n        async MyGetRunOrder() {\r\n            let res = await GetRunOrder('', this.MyEquipment);\r\n            this.runningCode = res.response;\r\n            if (this.runningCode == null) {\r\n                this.runningCode = '';\r\n            }\r\n        },\r\n        async getLtext(id) {\r\n            if (this.IsPack === '0') {\r\n                this.IsDifferent = false;\r\n                let params = {\r\n                    id: id\r\n                };\r\n                let r = await GetCookOrderLtexts(params);\r\n                if (r.response.length == 2) {\r\n                    if (r.msg === '长文本不一致！') {\r\n                        this.IsDifferent = true;\r\n                    }\r\n                    this.Text1 = r.response[0].ProcessData;\r\n                    this.Text2 = r.response[1].ProcessData;\r\n                }\r\n            }\r\n        },\r\n        async GetSegment() {\r\n            let params = {\r\n                LineCode: this.viewtitle\r\n            };\r\n            let res = await GetProcessOrderViewSegments(params);\r\n            // console.log(res,1432345345)\r\n            let data = res.response;\r\n            if (data.length != 0) {\r\n                data.forEach((item, index) => {\r\n                    if (index == 0) {\r\n                        this.searchlist[1].value = item.key;\r\n                    }\r\n                });\r\n                this.searchlist[1].option = data;\r\n                this.ShowPOList = true;\r\n                //this.GetProcessOrderView2();\r\n            } else {\r\n                this.ShowPOList = false;\r\n            }\r\n        },\r\n        getsearch() {\r\n            this.pageOptions2.page = 1;\r\n            this.pageOptions2.pageSize = 20;\r\n            this.GetProcessOrderView2();\r\n        },\r\n        getempty() {\r\n            this.QuickSearch = '';\r\n            this.timepicker = [];\r\n            this.pageOptions2.page = 1;\r\n            this.pageOptions2.pageSize = 20;\r\n            this.searchlist.forEach(item => {\r\n                item.value = '';\r\n            });\r\n            this.GetProcessOrderView2();\r\n        },\r\n        loadProgress(id) {\r\n            this.GetEquipment();\r\n            this.handleClick();\r\n        },\r\n        back() {\r\n            this.$router.go(-1);\r\n        },\r\n        async GetProcessOrderView2() {\r\n            // console.log('999999');\r\n            if (this.timepicker == null) {\r\n                this.timepicker = [];\r\n            }\r\n            let params = {\r\n                Search: this.searchlist[0].value,\r\n                LineCode: this.viewtitle,\r\n                Segment: this.searchlist[1].value,\r\n                FillLineCode: this.searchlist[2].value,\r\n                pageIndex: this.pageOptions2.page,\r\n                pageSize: this.pageOptions2.pageSize,\r\n                StartTime: this.timepicker[0],\r\n                EndTime: this.timepicker[1]\r\n            };\r\n            let res = await GetPoList(params);\r\n            this.AvailablePOManagemenList = res.response.data;\r\n            this.pageOptions2.total = res.response.dataCount;\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions2.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            // this.getNumTofather();\r\n        },\r\n        async GetProcessOrderViewList() {\r\n            console.log(12);\r\n            let params = {\r\n                EquipmentGroupRowId: this.EquipmentGroupRowId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res = await GetEquipmentProcessOrderView(params);\r\n            this.tableList = res.response.data;\r\n            this.pageOptions.total = res.response.dataCount;\r\n            let el = document.getElementsByClassName(`el-pagination__total`);\r\n            el[0].innerHTML = `${this.$t('PAGINATION.TOTAL_CN')}${this.pageOptions.total}${this.$t('PAGINATION.TOTAL')}`;\r\n            let el3 = document.querySelector('.el-pagination__sizes').querySelectorAll('.el-input__inner');\r\n            el3[0].value = el3[0].value.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n        },\r\n        changePagination() {\r\n            let el2 = document.getElementsByClassName(`el-select-dropdown__item`);\r\n            for (let i = 0; i < el2.length; i++) {\r\n                el2[i].innerHTML = el2[i].innerHTML.replace('条/页', this.$t('PAGINATION.MYPAGE'));\r\n            }\r\n        },\r\n        async GetEquipment() {\r\n            let params = {\r\n                EquipmentGroupRowId: this.EquipmentGroupRowId,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n\r\n            let res = await GetEquipmentFunctionView(params);\r\n            if (res.response.length != 0) {\r\n                res.response.forEach(item => {\r\n                    item.functionlist = item.FunctionCodes.split(',');\r\n                });\r\n            }\r\n            this.Equipmentlist = res.response;\r\n        },\r\n        async handleClick2(item, index) {\r\n            if (this.activeName2 == 'Logsheets') {\r\n                this.$refs[index + this.activeName2][0].activeName = '0';\r\n            }\r\n            console.log(this.ActiveList, 1);\r\n            console.log(this.$refs[index + this.activeName2][0]);\r\n            // this.GetEquipment();\r\n            let params = {\r\n                EquipmentGroupRowId: this.EquipmentGroupRowId,\r\n                RunEquipmentId: this.EquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res = await GetEquipmentProcessOrderView(params);\r\n            console.log(res, 222444);\r\n            if (res.response.data.length != 0) {\r\n                let list = res.response.data;\r\n                list.forEach(item => {\r\n                    item.Material = item.MaterialName + '-' + item.MaterialCode;\r\n                });\r\n                this.ActiveList = list;\r\n                this.BatchId = list[0].BatchId;\r\n                this.RunEquipmentId = list[0].RunEquipmentId;\r\n                this.ExecutionId = list[0].ExecutionId;\r\n            } else {\r\n                this.ActiveList = [];\r\n                this.BatchId = '';\r\n                this.RunEquipmentId = '';\r\n                this.ExecutionId = '';\r\n            }\r\n\r\n            if (this.$refs[index + this.activeName2][0].getEquipmentModal) {\r\n                this.$refs[index + this.activeName2][0].getEquipmentModal(item, this.ActiveList[0]);\r\n            }\r\n            if (this.$refs[index + this.activeName2][0].tabBeClick) {\r\n                this.$refs[index + this.activeName2][0].tabBeClick(item);\r\n            }\r\n            console.log(this.ActiveList);\r\n        },\r\n        async handleClick(key) {\r\n            if (key && Number(key.index) == 1) {\r\n                this.GetProcessOrderView2();\r\n            }\r\n            console.log(this.Equipmentlist);\r\n            // this.GetEquipment();\r\n            this.EquipmentId = this.activeName;\r\n            let params = {\r\n                EquipmentGroupRowId: this.EquipmentGroupRowId,\r\n                RunEquipmentId: this.EquipmentId,\r\n                pageIndex: this.pageOptions.page,\r\n                pageSize: this.pageOptions.pageSize\r\n            };\r\n            let res2 = await getCheckTippingType(JSON.stringify(this.EquipmentId));\r\n            if (res2.response == 'true') {\r\n                this.isTippingscan = true;\r\n            } else {\r\n                this.isTippingscan = false;\r\n            }\r\n            console.log('handleClick');\r\n            let res = await GetEquipmentProcessOrderView(params);\r\n            if (res.response.data.length != 0) {\r\n                let list = res.response.data;\r\n                list.forEach(item => {\r\n                    item.Material = item.MaterialName + '-' + item.MaterialCode;\r\n                });\r\n                this.ActiveList = list;\r\n                this.BatchId = list[0].BatchId;\r\n                this.RunEquipmentId = list[0].RunEquipmentId;\r\n                this.ExecutionId = list[0].ExecutionId;\r\n            } else {\r\n                this.ActiveList = [];\r\n                this.BatchId = '';\r\n                this.RunEquipmentId = '';\r\n                this.ExecutionId = '';\r\n            }\r\n            if (key) {\r\n                console.log(Number(key.index));\r\n                let num = null;\r\n                if (this.ShowPOList) {\r\n                    num = Number(key.index) - 2;\r\n                } else {\r\n                    num = Number(key.index) - 1;\r\n                }\r\n                if (num >= 0) {\r\n                    let functionlist = this.Equipmentlist[num].functionlist;\r\n                    //console.log(this.Equipmentlist[num]);\r\n                    console.log(functionlist);\r\n                    this.activeName2 = functionlist[0];\r\n                    functionlist.forEach(item => {\r\n                        if (item == 'POManagement') {\r\n                            this.activeName2 = 'POManagement';\r\n                        }\r\n                    });\r\n                    if (this.$refs[num + this.activeName2][0].getEquipmentModal) {\r\n                        this.$refs[num + this.activeName2][0].getEquipmentModal(this.Equipmentlist[num], this.ActiveList[0]);\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        handleSizeChange(val) {\r\n            this.pageOptions.pageSize = val;\r\n            this.GetProcessOrderViewList();\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.pageOptions.page = val;\r\n            this.GetProcessOrderViewList();\r\n        },\r\n        handleSizeChange2(val) {\r\n            this.pageOptions2.pageSize = val;\r\n            this.GetProcessOrderView2();\r\n        },\r\n        handleCurrentChange2(val) {\r\n            this.pageOptions2.page = val;\r\n            this.GetProcessOrderView2();\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.overview {\r\n    .searchboxtitle {\r\n        font-size: 1.7vh;\r\n        color: #767777;\r\n        padding-bottom: 5px;\r\n        margin-left: 10px;\r\n    }\r\n\r\n    .el-tabs {\r\n        height: 94%;\r\n    }\r\n    .subtabsbox {\r\n        .el-tabs--border-card {\r\n            border: 0 !important;\r\n            box-shadow: none !important;\r\n        }\r\n    }\r\n    .paginationbox {\r\n        height: 65px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n    .dialogdetailbox {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        margin-top: 10px;\r\n        .dialogdetailsinglelabel {\r\n            font-weight: 600;\r\n            width: 47%;\r\n            text-align: right;\r\n        }\r\n        .dialogdetailsinglevalue {\r\n            width: 78%;\r\n            margin-left: 20px;\r\n        }\r\n    }\r\n    .splitdetailbox {\r\n        padding-bottom: 10px;\r\n        border: 1px solid #e8e8e8;\r\n        margin-bottom: 5px;\r\n        .splitdetailboxtitle {\r\n            background: #f5f5f5;\r\n            height: 3.5vh;\r\n            display: flex;\r\n            align-items: center;\r\n            padding-left: 5px;\r\n            font-size: 1.1rem;\r\n            color: #303133;\r\n        }\r\n        .detailsnote {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 0.9rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .detailsnote2 {\r\n            background-color: #fdf6ec;\r\n            border-color: #faecd8;\r\n            color: #e6a23c;\r\n            padding: 8px;\r\n            font-size: 1.2rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .detailsnote3 {\r\n            background-color: #f5fdec;\r\n            border-color: #faecd8;\r\n            color: hsl(135, 55%, 44%);\r\n            padding: 8px;\r\n            font-size: 1.2rem;\r\n            margin: 5px 10px 0px 10px;\r\n        }\r\n        .splitdetailboxtitleTag {\r\n            margin-left: 5px;\r\n            background: #5cb85c;\r\n            color: #fff;\r\n            border-color: #5cb85c;\r\n        }\r\n    }\r\n}\r\n//.el-dialog__body {\r\n//    .el-input {\r\n//        width: 250px !important;\r\n//    }\r\n//    .longwidthinput {\r\n//        .el-input {\r\n//            width: 400px !important;\r\n//        }\r\n//        .el-select {\r\n//            width: 400px !important;\r\n//        }\r\n//    }\r\n//    .el-select {\r\n//        width: 250px !important;\r\n//    }\r\n//}\r\n.code-diff-view{\r\n  margin-top: 5px;\r\n}\r\n</style>\r\n"]}]}