{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\productionManagement\\ResourceDefinition\\components\\form-dialog-phase.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\productionManagement\\ResourceDefinition\\components\\form-dialog-phase.vue", "mtime": 1750254216363}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgeyBzYXZlUGhhc2UsIGdldFNhcFNlZ21lbnRMaXN0IH0gZnJvbSAnQC9hcGkvcHJvZHVjdGlvbk1hbmFnZW1lbnQvRm9ybXVsYSc7DQppbXBvcnQge2dldExhYmVsRm9ybWF0TGlzdH0gZnJvbSAiQC9hcGkvc3lzdGVtTWFuYWdlbWVudC9sYWJlbFByaW50IjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ2FkZCcsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGRpYWxvZ0Zvcm06IHt9LA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBmb3JtTG9hZGluZzogZmFsc2UsDQogICAgICBzYXBFcXVpcG1lbnRJZDogMCwNCiAgICAgIFNhcFNlZ21lbnRzOiBbXSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIFBhcmVudElkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeW3peautScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXSwNCiAgICAgICAgU2VnbWVudENvZGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl57yW56CBJywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgIF0sDQogICAgICAgIFNlZ21lbnROYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWQjeensCcsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICBdDQogICAgICB9DQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KICB9LA0KICBtZXRob2RzOiB7DQogICAgc3VibWl0KCkgew0KICAgICAgdGhpcy5kaWFsb2dGb3JtLlNhcEVxdWlwbWVudElkID0gdGhpcy5zYXBFcXVpcG1lbnRJZA0KICAgICAgdGhpcy4kcmVmcy5kaWFsb2dGb3JtLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBzYXZlUGhhc2UodGhpcy5kaWFsb2dGb3JtKS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MocmVzLm1zZykNCiAgICAgICAgICAgIHRoaXMuJGVtaXQoJ3NhdmVGb3JtJykNCiAgICAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBzaG93KGRhdGEpIHsNCiAgICAgIHRoaXMuZGlhbG9nRm9ybSA9IHt9DQogICAgICB0aGlzLiRzZXQodGhpcy5kaWFsb2dGb3JtLCAnSUQnLCBkYXRhLklEKQ0KICAgICAgdGhpcy5zYXBFcXVpcG1lbnRJZCA9IGRhdGEuU2FwRXF1aXBtZW50SWQNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICB0aGlzLmdldE9wZXJhdGlvbkRhdGEoZGF0YSkNCiAgICAgICAgaWYoZGF0YS5JRCl7DQogICAgICAgICANCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5kaWFsb2dGb3JtLCAnUGFyZW50SWQnLCBkYXRhLlBhcmVudElkKQ0KICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmRpYWxvZ0Zvcm0sICdTZWdtZW50Q29kZScsIGRhdGEuU2VnbWVudENvZGUpDQogICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZGlhbG9nRm9ybSwgJ1NlZ21lbnROYW1lJywgZGF0YS5TZWdtZW50TmFtZSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldE9wZXJhdGlvbkRhdGEoZGF0YSkgew0KICAgICAgdGhpcy5TYXBTZWdtZW50cyA9IFtdDQogICAgICBnZXRTYXBTZWdtZW50TGlzdCh7TGV2ZWw6IDEsU2FwRXF1aXBtZW50SWQ6IHRoaXMuc2FwRXF1aXBtZW50SWR9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMuU2FwU2VnbWVudHMucHVzaCguLi5yZXMucmVzcG9uc2UpDQogICAgICAgIC8vIHRoaXMuZGlhbG9nRm9ybSA9IE9iamVjdC5hc3NpZ24oe30sZGF0YSkNCiAgICAgIH0pDQogICAgICAvLyBjb25zdCB7IHJlc3BvbnNlIH0gPSBhd2FpdCBnZXRTYXBTZWdtZW50TGlzdCh7DQogICAgICAvLyAgIExldmVsOiAxLA0KICAgICAgLy8gICBTYXBFcXVpcG1lbnRJZDogdGhpcy5zYXBFcXVpcG1lbnRJZCwNCiAgICAgIC8vIH0pDQogICAgICAvLyB0aGlzLlNhcFNlZ21lbnRzID0gcmVzcG9uc2UNCiAgICB9LA0KICAgIHNlbGVjdENoYW5nZSgpew0KICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["form-dialog-phase.vue"], "names": [], "mappings": ";AA2BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "form-dialog-phase.vue", "sourceRoot": "src/views/productionManagement/ResourceDefinition/components", "sourcesContent": ["<template>\r\n  <el-dialog :title=\"dialogForm.ID ? '编辑' : '新增'\" :visible.sync=\"dialogVisible\" width=\"600px\"\r\n             :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\r\n             @close=\"dialogVisible = false\">\r\n    <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"80px\">\r\n      <el-form-item label=\"工段\" prop=\"ParentId\">\r\n        <el-select transfer=\"true\" :popper-append-to-body=\"false\" style=\"width: 100%\" v-model=\"dialogForm.ParentId\" @change=\"selectChange\" placeholder=\"请选择\">\r\n          <el-option v-for=\"(item) in SapSegments\" :key=\"item.id\" :label=\"item.SegmentName\" :value=\"item.ID\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"编码\" prop=\"SegmentCode\">\r\n        <el-input v-model=\"dialogForm.SegmentCode\" :maxlength=\"20\" placeholder=\"\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"名称\" prop=\"SegmentName\">\r\n        <el-input v-model=\"dialogForm.SegmentName\" :maxlength=\"20\" placeholder=\"\" />\r\n      </el-form-item>\r\n    </el-form>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\r\n      <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\"\r\n                 @click=\"submit()\">确定\r\n      </el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n<script>\r\nimport { savePhase, getSapSegmentList } from '@/api/productionManagement/Formula';\r\nimport {getLabelFormatList} from \"@/api/systemManagement/labelPrint\";\r\nexport default {\r\n  name: 'add',\r\n  data() {\r\n    return {\r\n      dialogForm: {},\r\n      dialogVisible: false,\r\n      formLoading: false,\r\n      sapEquipmentId: 0,\r\n      SapSegments: [],\r\n      rules: {\r\n        ParentId: [\r\n          { required: true, message: '请选择工段', trigger: 'change' }\r\n        ],\r\n        SegmentCode: [\r\n          { required: true, message: '请输入编码', trigger: 'blur' },\r\n        ],\r\n        SegmentName: [\r\n          { required: true, message: '请输入名称', trigger: 'blur' },\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.dialogForm.SapEquipmentId = this.sapEquipmentId\r\n      this.$refs.dialogForm.validate((valid) => {\r\n        if (valid) {\r\n          savePhase(this.dialogForm).then(res => {\r\n            this.$message.success(res.msg)\r\n            this.$emit('saveForm')\r\n            this.dialogVisible = false\r\n          })\r\n        }\r\n      });\r\n    },\r\n    show(data) {\r\n      this.dialogForm = {}\r\n      this.$set(this.dialogForm, 'ID', data.ID)\r\n      this.sapEquipmentId = data.SapEquipmentId\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.getOperationData(data)\r\n        if(data.ID){\r\n         \r\n          this.$set(this.dialogForm, 'ParentId', data.ParentId)\r\n          this.$set(this.dialogForm, 'SegmentCode', data.SegmentCode)\r\n          this.$set(this.dialogForm, 'SegmentName', data.SegmentName)\r\n        }\r\n      })\r\n    },\r\n    getOperationData(data) {\r\n      this.SapSegments = []\r\n      getSapSegmentList({Level: 1,SapEquipmentId: this.sapEquipmentId}).then(res => {\r\n        this.SapSegments.push(...res.response)\r\n        // this.dialogForm = Object.assign({},data)\r\n      })\r\n      // const { response } = await getSapSegmentList({\r\n      //   Level: 1,\r\n      //   SapEquipmentId: this.sapEquipmentId,\r\n      // })\r\n      // this.SapSegments = response\r\n    },\r\n    selectChange(){\r\n      this.$forceUpdate()\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}