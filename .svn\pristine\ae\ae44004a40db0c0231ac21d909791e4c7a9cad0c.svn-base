﻿using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [AllowAnonymous]
    public class SopAuditController : BaseApiController
    {
        /// <summary>
        /// SopAudit
        /// </summary>
        private readonly ISopAuditServices _sopAuditServices;

        private readonly ISopDocServices _sopDocServices;

        public SopAuditController(ISopAuditServices SopAuditServices, ISopDocServices SopDocServices)
        {
            _sopAuditServices = SopAuditServices;
            _sopDocServices = SopDocServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<SopAuditEntity>>> GetList([FromBody] SopAuditRequestModel reqModel)
        {
            var data = await _sopAuditServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<SopAuditEntity>>> GetPageList([FromBody] SopAuditRequestModel reqModel)
        {
            var data = await _sopAuditServices.GetPageList(reqModel);
            if (data.pageCount > 0)
            {
                foreach (var sopAuditEntity in data.data)
                {
                    sopAuditEntity.Doc = await _sopDocServices.FindEntity(sopAuditEntity.DocId);
                }
            }

            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<SopAuditEntity>> GetEntity(string id)
        {
            var data = await _sopAuditServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] SopAuditEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _sopAuditServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _sopAuditServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] SopAuditEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _sopAuditServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _sopAuditServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class SopAuditRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}