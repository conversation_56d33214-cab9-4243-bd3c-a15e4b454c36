<template>
  <div class="root usemystyle">
    <div class="InventorySearchBox">
      <div class="search-form">
        <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
          <div class="form-content">
            <div class="search-area">
              <div class="search-row">
                <el-form-item :label="$t('SOP.DocName')" prop="docName" label-width="40px" class="search-form-item">
                  <el-input v-model="searchForm.docName" :placeholder="$t('SOP.EnterDocName')" clearable size="small"></el-input>
                </el-form-item>

                <el-form-item :label="$t('SOP.DocCode')" prop="docCode" label-width="40px" class="search-form-item">
                  <el-input v-model="searchForm.docCode" :placeholder="$t('SOP.EnterDocCode')" clearable size="small"></el-input>
                </el-form-item>

                <el-form-item :label="$t('SOP.UploadUser')" prop="uploadUser" label-width="40px" class="search-form-item">
                  <el-input v-model="searchForm.uploadUser" :placeholder="$t('SOP.EnterUploadUser')" clearable size="small"></el-input>
                </el-form-item>

                <div class="action-buttons">
                  <el-button type="primary" icon="el-icon-search" @click="getSearchBtn" size="small">{{ $t('GLOBAL._CX') }}</el-button>
                  <el-button icon="el-icon-refresh" @click="resetForm" size="small">{{ $t('GLOBAL._CZ') }}</el-button>
                </div>
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <div class="root-main">
      <el-table class="mt-3"
                :height="mainH"
                border
                :data="tableData"
                style="width: 100%">
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center">
        </el-table-column>
        <el-table-column v-for="(item) in tableName"
                         :default-sort="{prop: 'date', order: 'descending'}"
                         :key="item.value"
                         :prop="item.value"
                         :label="typeof item.text === 'function' ? item.text() : item.text"
                         :width="item.width"
                         :align="item.alignType || 'center'"
                         sortable
                         show-overflow-tooltip
        >
          <template slot-scope="scope">
            <template v-if="item.value === 'FileSize'">
              {{ formatFileSize(getDocFieldValue(scope.row, item.value)) }}
            </template>
            <template v-else-if="item.value === 'DocStatus'">
              <el-tag :type="getStatusType(getDocFieldValue(scope.row, item.value))" size="small">
                {{ formatStatus(getDocFieldValue(scope.row, item.value)) }}
              </el-tag>
            </template>
            <template v-else-if="item.value === 'OperationType'">
              {{ formatOperationType(scope.row[item.value]) }}
            </template>
            <template v-else-if="item.value === 'AuditResult'">
              <el-tag :type="getAuditResultType(scope.row[item.value])" size="small">
                {{ formatAuditResult(scope.row[item.value]) }}
              </el-tag>
            </template>
            <template v-else-if="['DocName', 'DocCode', 'DocVersion', 'FilePath'].includes(item.value)">
              {{ getDocFieldValue(scope.row, item.value) }}
            </template>
            <template v-else>
              {{ scope.row[item.value] }}
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="operation" width="180" :label="$t('GLOBAL._ACTIONS')" align="center">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <el-button size="mini" type="primary" @click="previewDoc(scope.row)">{{ $t('SOP.Preview') }}</el-button>
              <el-button size="mini" type="success" @click="approveAudit(scope.row)"
                         :disabled="scope.row.AuditResult !== null && scope.row.AuditResult !== 0">{{ $t('SOP.Approve') }}</el-button>
              <el-button size="mini" type="danger" @click="rejectAudit(scope.row)"
                         :disabled="scope.row.AuditResult !== null && scope.row.AuditResult !== 0">{{ $t('SOP.Reject') }}</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="root-footer">
      <el-pagination
          class="mt-3"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchForm.pageIndex"
          :page-sizes="[10,20, 50, 100,500]"
          :page-size="searchForm.pageSize"
          layout="->,total, sizes, prev, pager, next, jumper"
          :total="total"
          background
      ></el-pagination>
    </div>
    <!-- 审核不通过原因对话框 -->
    <el-dialog :title="$t('SOP.RejectReason')" :visible.sync="rejectDialogVisible" width="500px"
               :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form ref="rejectForm" :model="rejectForm" label-width="120px">
        <el-form-item :label="$t('SOP.RejectReason')" prop="auditComment"
                      :rules="[{ required: true, message: $t('SOP.RejectReasonRequired'), trigger: 'blur' }]">
          <el-input type="textarea" v-model="rejectForm.auditComment"
                    :placeholder="$t('SOP.EnterRejectReason')" :rows="4"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="rejectDialogVisible = false">{{ $t('GLOBAL._QX') }}</el-button>
        <el-button type="primary" @click="confirmReject">{{ $t('GLOBAL._QD') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';
import {
    delSopAudit, getSopAuditList, saveSopAuditForm
} from "@/api/SOP/sopAudit";

import { sopAuditColumn } from '@/api/SOP/sopAudit.js';


export default {
  name: 'index.vue',
  data() {
    return {
      searchForm: {
        pageIndex: 1,
        pageSize: 20,
        docName: '',
        docCode: '',
        uploadUser: ''
      },
      total: 0,
      tableData: [],
      tableName: sopAuditColumn,
      loading: false,
      mainH: 0,
      buttonOption:{
        name:'SOP审核',
        serveIp:'baseURL_DFM',
        uploadUrl:'/api/SopAudit/ImportData', //导入
        exportUrl:'/api/SopAudit/ExportData', //导出
        DownLoadUrl:'/api/SopAudit/DownLoadTemplate', //下载模板
      },
      // 审核不通过原因对话框
      rejectDialogVisible: false,
      rejectForm: {
        auditComment: ''
      },
      currentAuditRow: null
    }
  },
  mounted() {
    this.getTableData()
    this.$nextTick(() => {
      this.mainH = this.$webHeight(document.getElementsByClassName('InventorySearchBox')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)
    })
    window.onresize = () => {
      this.mainH = this.$webHeight(document.getElementsByClassName('InventorySearchBox')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)
    }
  },
  methods: {
    showDialog(row) {
      this.$refs.formDialog.show(row)
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    handleSizeChange(size) {
      this.searchForm.pageSize = size
      this.getTableData()
    },
    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },

    resetForm() {
      this.searchForm = {
        pageIndex: 1,
        pageSize: 20,
        docName: '',
        docCode: '',
        uploadUser: ''
      }
      this.getTableData()
    },

    delRow(row) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        delSopAudit([row.ID]).then(res => {
          this.$message.success(res.msg)
          this.getTableData()
        })
      }).catch(err => {
        console.log(err);
      });
    },

    getTableData() {
      getSopAuditList(this.searchForm).then(res => {
        this.tableData = res.response.data
        this.total = res.response.dataCount
      })
    },

    // 获取Doc字段中的文档信息
    getDocFieldValue(row, fieldName) {
      // 如果是文档相关字段，从Doc对象中获取
      if (['DocName', 'DocCode', 'DocVersion', 'FilePath', 'FileSize', 'DocStatus'].includes(fieldName)) {
        return row.Doc ? row.Doc[fieldName] : ''
      }
      // 其他字段直接从row中获取
      return row[fieldName]
    },

    // 预览文档
    previewDoc(row) {
      // 这里可以实现文档预览功能
      this.$message.info(this.$t('SOP.PreviewNotImplemented'))
    },

    // 通过审核
    approveAudit(row) {
      this.$confirm(this.$t('SOP.ConfirmApprove'), this.$t('GLOBAL._TS'), {
        confirmButtonText: this.$t('GLOBAL._QD'),
        cancelButtonText: this.$t('GLOBAL._QX'),
        type: 'warning'
      }).then(() => {
        const auditData = {
          ...row,
          AuditResult: 2, // 审批通过
          AuditUserId: this.$store.getters.name,
          AuditComment: this.$t('SOP.AuditPassed')
        }
        saveSopAuditForm(auditData).then(res => {
          this.$message.success(this.$t('SOP.ApproveSuccess'))
          this.getTableData()
        })
      }).catch(() => {
        this.$message.info(this.$t('GLOBAL._QXCZ'))
      })
    },

    // 不通过审核
    rejectAudit(row) {
      this.currentAuditRow = row
      this.rejectForm.auditComment = ''
      this.rejectDialogVisible = true
    },

    // 确认不通过
    confirmReject() {
      this.$refs.rejectForm.validate((valid) => {
        if (valid) {
          const auditData = {
            ...this.currentAuditRow,
            AuditResult: 3, // 审批不通过
            AuditUserId: this.$store.getters.name,
            AuditComment: this.rejectForm.auditComment
          }
          saveSopAuditForm(auditData).then(res => {
            this.$message.success(this.$t('SOP.RejectSuccess'))
            this.rejectDialogVisible = false
            this.getTableData()
          })
        }
      })
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return ''
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(2)} ${units[index]}`
    },

    // 格式化文档状态
    formatStatus(status) {
      const statusMap = {
        1: this.$t('SOP.StatusValid'),
        0: this.$t('SOP.StatusInvalid')
      }
      return statusMap[status] || this.$t('GLOBAL._WZ')
    },

    // 获取状态类型
    getStatusType(status) {
      return status === 1 ? 'success' : 'danger'
    },

    // 格式化操作类型
    formatOperationType(type) {
      const typeMap = {
        1: this.$t('SOP.OperationCreate'),
        2: this.$t('SOP.OperationModify'),
        3: this.$t('SOP.OperationDelete')
      }
      return typeMap[type] || this.$t('GLOBAL._WZ')
    },

    // 格式化审核结果
    formatAuditResult(result) {
      const resultMap = {
        0: this.$t('SOP.AuditPending'),
        1: this.$t('SOP.AuditInProgress'),
        2: this.$t('SOP.AuditApproved'),
        3: this.$t('SOP.AuditRejected')
      }
      return resultMap[result] || this.$t('GLOBAL._WZ')
    },

    // 获取审核结果类型
    getAuditResultType(result) {
      const typeMap = {
        0: 'warning',
        1: 'primary',
        2: 'success',
        3: 'danger'
      }
      return typeMap[result] || 'info'
    }
  }
}

//<!-- 移到到src/local/en.json和zh-Hans.json -->
//"SopAudit": {
//    "table": {
//        "docId": "docId",
//        "operationType": "operationType",
//        "oldValue": "oldValue",
//        "newValue": "newValue",
//        "operatorId": "operatorId",
//        "operateTime": "operateTime",
//        "clientIp": "clientIp",
//        "createdate": "createdate",
//        "createuserid": "createuserid",
//        "modifydate": "modifydate",
//        "modifyuserid": "modifyuserid",
//        "deleted": "deleted",
//    }
//},
</script>

<style lang="scss" scoped>
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

.search-form-item {
  .el-form-item__content {
    width: 180px; // 统一宽度为180px
  }
}

.InventorySearchBox {
  .search-form {
    :deep(.el-form) {
      .el-form-item--small.el-form-item {
        margin-bottom: 0;
      }
    }

    .form-content {
      padding: 4px;

      .search-area {
        .search-row {
          display: flex;
          align-items: center;
          gap: 4px;

          .el-form-item {
            margin: 0;
            flex: none;

            .el-form-item__label {
              padding-right: 4px;
              line-height: 26px;
              font-size: 12px;
            }

            .el-form-item__content {
              line-height: 26px;

              .el-input,
              .el-select {
                :deep(.el-input__inner) {
                  height: 26px;
                  line-height: 26px;
                  padding: 0 8px;
                  font-size: 12px;
                }

                :deep(.el-input-group__append) {
                  padding: 0;
                  .el-button {
                    padding: 0 10px;
                    height: 26px;
                    border: none;
                  }
                }
              }
            }
          }

          .action-buttons {
            display: flex;
            gap: 4px;
            margin-left: 4px;

            .el-button {
              height: 26px;
              padding: 0 10px;
              font-size: 12px;

              [class^="el-icon-"] {
                margin-right: 3px;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

.mt-8p {
  margin-top: 8px;
}

.pd-left {
  padding-left: 5px
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2px;

  .el-button--mini {
    padding: 2px;
    font-size: 11px;
    border-radius: 3px;
    min-width: auto;
  }
}
</style>