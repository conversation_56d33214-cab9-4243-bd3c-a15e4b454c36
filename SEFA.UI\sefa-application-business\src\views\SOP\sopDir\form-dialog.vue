<template>
  <div class="sop-dir-form">
    <el-dialog
      :title="dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')"
      :visible.sync="dialogVisible"
      width="700px"
      :modal="true"
      :append-to-body="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleClose"
    >
      <el-form ref="dialogForm" :model="dialogForm" :rules="rules" label-width="100px">
        <div class="form-body">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="父级目录" prop="parentId">
                <span class="parent-dir" :title="parentDir">{{ parentDir || '未选择父级目录' }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('SOP.DirName')" prop="dirName">
                <el-input v-model="dialogForm.dirName" :placeholder="$t('SOP.EnterDirName')"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('SOP.DirCode')" prop="dirCode">
                <el-input v-model="dialogForm.dirCode" :placeholder="$t('SOP.EnterDirCode')"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="负责人" prop="ownerUserid">
                <div class="user-selector">
                  <span class="user-text" :title="selectedUser">{{ selectedUser || '未选择负责人' }}</span>
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="showUserDialog = true"
                    class="select-btn"
                    icon="el-icon-user">
                    选择
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button-group>
          <el-button size="small" @click="dialogVisible = false">取 消</el-button>
          <el-button 
            type="primary" 
            size="small"
            v-loading="formLoading" 
            :disabled="formLoading" 
            element-loading-spinner="el-icon-loading"
            @click="submit()">
            确 定
          </el-button>
        </el-button-group>
      </div>
    </el-dialog>
    <select-user 
      :visible.sync="showUserDialog" 
      @select-user="handleSelectUser"
      v-if="dialogVisible"
    />

  </div>
</template>
  
<script>
import {
  getSopDirDetail,
  saveSopDirForm,
  getSopDirTree
} from "@/api/SOP/sopDir";
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import SelectUser from './components/selectUser.vue'

export default {
  name: 'SopDirFormDialog',
  components: {
    SelectUser
  },
  data() {
    return {
      dialogForm: {
        parentId: 0,
        dirName: '',
        dirCode: '',
        ownerUserid: ''
      },
      rules: {
        dirName: [
          { required: true, message: this.$t('SOP.DirNameRequired'), trigger: 'blur' },
          { min: 2, max: 50, message: this.$t('SOP.DirNameLength'), trigger: 'blur' }
        ],
        dirCode: [
          { required: true, message: this.$t('SOP.DirCodeRequired'), trigger: 'blur' },
          { pattern: /^[A-Za-z0-9-_]+$/, message: this.$t('SOP.DirCodeFormat'), trigger: 'blur' }
        ],
        ownerUserid: [
          { required: true, message: this.$t('SOP.DirOwnerRequired'), trigger: 'change' }
        ]
      },
      dialogVisible: false,
      formLoading: false,
      currentRow: {},
      isRootDir: false, // 是否新增根目录
      dataList: [], // 目录树数据
      showUserDialog: false,
      selectedUser: '',
      parentDir: '',
      normalizer: node => ({
        id: node.ID,
        label: node.DirName,
        children: node.Children
      })
    }
  },
  methods: {
    async submit() {
      try {
        await this.$refs.dialogForm.validate()
        this.formLoading = true
        const res = await saveSopDirForm(this.dialogForm)
        if (res.success) {
          this.$message.success(res.msg || '保存成功')
          this.$emit('saveForm')
          this.dialogVisible = false
        } else {
          this.$message.error(res.msg || '保存失败')
        }
      } catch (err) {
        console.error('保存失败:', err)
        this.$message.error('保存失败')
      } finally {
        this.formLoading = false
      }
    },
    async loadTreeData() {
      try {
        const res = await getSopDirTree()
        if (res.success) {
          this.dataList = res.response || []
        } else {
          this.$message.error(res.msg || '获取目录树失败')
        }
      } catch (err) {
        console.error('获取目录树失败:', err)
        this.$message.error('获取目录树失败')
      }
    },
    async show(data, type) {
      if(type === 'add'){
        // 判断是否为新增根目录
        this.isRootDir = !data.id && !data.parentId && !data.parentId != '0'

        this.dialogForm = {
          parentId: this.isRootDir ? 0 : data.id, // 根目录parentId为0，否则为选中节点的id
          dirName: '',
          dirCode: '',
          ownerUserid: ''
        }
        this.selectedUser = ''
        this.parentDir = this.isRootDir ? '' : data.value + '-' + data.name
        this.currentRow = data
      }

      if(type === 'show'){
        this.dialogForm = {
          id: data.id,
          parentId: this.isRootDir ? 0 : data.parentId, // 根目录parentId为0，否则为选中节点的id
          dirName: data.name,
          dirCode: data.value,
          ownerUserid: data.remark
        }
        this.selectedUser = data.extendField
      }      
      this.dialogVisible = true      
    },
    async getDialogDetail(id) {
      try {
        const res = await getSopDirDetail(id)
        if (res.success) {
          this.dialogForm = res.response || {}
          // 如果有负责人信息，设置显示名称
          if (this.dialogForm.ownerUserid && this.dialogForm.ownerUserName) {
            this.selectedUser = this.dialogForm.ownerUserName
          }
        } else {
          this.$message.error(res.msg || '获取详情失败')
        }
      } catch (err) {
        console.error('获取详情失败:', err)
        this.$message.error('获取详情失败')
      }
    },
    handleClose() {
      this.dialogVisible = false
      // 添加表单重置逻辑
      this.$nextTick(() => {
        this.$refs.dialogForm.clearValidate()
      })
    },
    
    // 处理用户选择
    handleSelectUser(user) {
      try {
        console.log('Selected user:', user) // 调试日志
        
        if (!user || !user.ID) {
          throw new Error('无效的用户选择')
        }

        this.dialogForm.ownerUserid = user.LoginName
        this.selectedUser = `${user.DepartmentName || ''} - ${user.LoginName || ''} - ${user.UserName || ''}`.trim()
        
        // 关闭对话框并验证表单
        this.$nextTick(() => {
          this.showUserDialog = false
          this.$refs.dialogForm.validateField('ownerUserid')
        })
        
      } catch (error) {
        console.error('处理用户选择失败:', error)
        this.$message.error('选择负责人失败: ' + (error.message || '未知错误'))
        this.showUserDialog = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sop-dir-form {
  :deep(.el-dialog) {
    border-radius: 8px;

    .el-dialog__header {
      padding: 15px 20px;
      border-bottom: 1px solid #ebeef5;
      margin: 0;
    }
    
    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__footer {
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      background-color: #f9fafb;
    }
  }

  .form-body {
    padding: 10px 0;

    .el-form-item {
      margin-bottom: 18px;
    }
  }

  .parent-dir {
    display: block;
    padding: 5px 12px;
    line-height: 1.5;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #f5f7fa;
    color: #606266;
  }

  .user-selector {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .user-text {
      flex: 1;
      padding: 5px 12px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background-color: #f5f7fa;
      color: #606266;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .select-btn {
      flex-shrink: 0;
      width: 80px;
      padding: 7px 12px;
    }
  }
}
</style>
