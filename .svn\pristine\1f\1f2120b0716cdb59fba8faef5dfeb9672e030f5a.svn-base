﻿using System;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using SEFA.Base.Common.LogHelper;

namespace SEFA.DFM.Services
{
    public class SopDirServices : BaseServices<SopDirEntity>, ISopDirServices
    {
        private readonly IBaseRepository<SopDirEntity> _dal;
        private readonly IBaseRepository<UserinfoEntity> _userDal;
        private readonly IBaseRepository<EquipmentEntity> _equipmentDal;
        private readonly IMapper _mapper;

        public SopDirServices(IBaseRepository<SopDirEntity> dal, IBaseRepository<UserinfoEntity> userDal,
            IBaseRepository<EquipmentEntity> equipmentDal, IMapper mapper)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _userDal = userDal;
            _equipmentDal = equipmentDal;
            _mapper = mapper;
        }

        public async Task<List<SopDirEntity>> GetList(SopDirRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SopDirEntity>()
                .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<SopDirEntity>> GetPageList(SopDirRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SopDirEntity>()
                .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<bool> SaveForm(SopDirEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }

        public async Task<List<TreeModelEx<SopDirEntity>>> GetTreeList(string parentId = "")
        {
            try
            {
                // 1. 合并查询条件构建
                var condition = Expressionable.Create<SopDirEntity>()
                    .And(a => a.Deleted == 0);

                if (string.IsNullOrEmpty(parentId))
                {
                    condition.And(a => a.ParentId == "" || a.ParentId == null || a.ParentId == "0");
                }
                else
                {
                    // 获取子目录ID时复用condition
                    var parentDirs = await _dal.FindList(
                        condition.And(a => a.ParentId == parentId).ToExpression(),
                        c => c.DirCode);
                    var dirIds = parentDirs.Select(g => g.ID).ToList();

                    condition.And(a => a.ParentId == parentId || dirIds.Contains(a.ParentId));
                }

                // 2. 并行获取所有必要数据
                var allDirsTask = _dal.FindList(condition.ToExpression(), c => c.DirCode);
                var userInfoTask = _userDal.FindList(c => c.Deleted == 0);
                var departmentTask = _equipmentDal.FindList(c => c.Deleted == 0);

                await Task.WhenAll(allDirsTask, userInfoTask, departmentTask);

                var allDirs = await allDirsTask;
                if (allDirs?.Count == 0) return new List<TreeModelEx<SopDirEntity>>();

                var userInfoList = await userInfoTask;
                var departmentList = await departmentTask;

                // 3. 使用null安全的字典转换
                var userInfoDict = userInfoList?.ToDictionary(c => c.ID, c => c)
                                   ?? new Dictionary<string, UserinfoEntity>();
                var departmentDict = departmentList?.ToDictionary(c => c.ID, c => c)
                                     ?? new Dictionary<string, EquipmentEntity>();

                // 4. 优化循环处理
                foreach (var dir in allDirs.Where(d => !string.IsNullOrWhiteSpace(d.OwnerUserid)))
                {
                    if (userInfoDict.TryGetValue(dir.OwnerUserid, out var user) &&
                        !string.IsNullOrWhiteSpace(user?.Departmentid) &&
                        departmentDict.TryGetValue(user.Departmentid, out var dept))
                    {
                        dir.OwnerUserid = $"{dept.EquipmentName}-{user.UserName}";
                    }
                }

                return allDirs.Select(item => new TreeModelEx<SopDirEntity>
                {
                    id = item.ID,
                    data = item,
                    parentId = item.ParentId
                }).ToList();
            }
            catch (Exception e)
            {
                SerilogServer.LogError(e, $"获取SOP目录树失败: {e.Message}");
                throw;
            }
        }

        public async Task<List<SopDirDto>> GetAllDirs()
        {
            var expression = Expressionable.Create<SopDirEntity>()
                .And(a => a.Deleted == 0)
                .ToExpression();
            // 2. 并行获取所有必要数据
            var allDirsTask = _dal.FindList(expression);
            var userInfoTask = _userDal.FindList(c => c.Deleted == 0 && !string.IsNullOrWhiteSpace(c.LoginName));
            var departmentTask = _equipmentDal.FindList(c => c.Deleted == 0);

            await Task.WhenAll(allDirsTask, userInfoTask, departmentTask);

            var allDirs = await allDirsTask;
            if (allDirs?.Count == 0) return null;

            var userInfoList = await userInfoTask;
            var departmentList = await departmentTask;

            // 3. 使用null安全的字典转换
            var userInfoDict = userInfoList?.ToDictionary(c => c.LoginName, c => c)
                               ?? new Dictionary<string, UserinfoEntity>();
            var departmentDict = departmentList?.ToDictionary(c => c.EquipmentCode, c => c)
                                 ?? new Dictionary<string, EquipmentEntity>();

            List<SopDirDto> dirDtoList = _mapper.Map<List<SopDirDto>>(allDirs);
            // 4. 优化循环处理
            foreach (var dir in dirDtoList.Where(d => !string.IsNullOrWhiteSpace(d.OwnerUserid)))
            {
                if (userInfoDict.TryGetValue(dir.OwnerUserid, out var user) &&
                    !string.IsNullOrWhiteSpace(user?.Departmentid) &&
                    departmentDict.TryGetValue(user.Departmentid, out var dept))
                {
                    dir.OwnerUser = $"{dept.EquipmentName}-{user.UserName}";
                }
            }

            return dirDtoList;
        }
    }
}